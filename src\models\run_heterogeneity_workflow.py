#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Workflow script to run heterogeneity analysis on precipitation clusters.

This script finds all regionalization output directories and runs the
heterogeneity analysis on each set of clusters.
"""

import os
import sys
import glob
import logging
import argparse
import subprocess
from pathlib import Path

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.utils.logging_config import setup_logging

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Run heterogeneity analysis workflow on precipitation clusters.'
    )
    
    parser.add_argument(
        '--precip-path',
        type=str,
        required=True,
        help='Path to the precipitation data file (NetCDF) containing AMS series'
    )
    
    parser.add_argument(
        '--base-dir',
        type=str,
        default='.',
        help='Base directory to search for regionalization results (default: current directory)'
    )

    parser.add_argument(
        '--pattern',
        type=str,
        default=None,
        help='Custom directory pattern to search for (e.g., "run_w*", "my_results_*"). If not specified, searches for standard patterns.'
    )
    
    parser.add_argument(
        '--output-base-dir',
        type=str,
        default='heterogeneity_results',
        help='Base directory for heterogeneity results (default: heterogeneity_results)'
    )
    
    parser.add_argument(
        '--max-clusters',
        type=int,
        default=20,
        help='Maximum number of clusters to analyze (default: 20)'
    )
    
    parser.add_argument(
        '--nsim',
        type=int,
        default=500,
        help='Number of simulations for the heterogeneity test (default: 500)'
    )
    
    parser.add_argument(
        '--seed',
        type=int,
        default=42,
        help='Random seed for reproducibility (default: 42)'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='Set the logging level (default: INFO)'
    )
    
    return parser.parse_args()

def find_regionalization_dirs(base_dir, custom_pattern=None):
    """
    Find all regionalization output directories.

    Parameters:
    -----------
    base_dir : str
        Base directory to search for regionalization results
    custom_pattern : str, optional
        Custom directory pattern to search for (e.g., "run_w*")

    Returns:
    --------
    list
        List of tuples (directory_path, is_weather_typing)
    """
    # Find directories matching different patterns for regionalization results

    if custom_pattern:
        # Use custom pattern
        logger.info(f"Searching for directories matching pattern: {custom_pattern}")
        custom_dirs = glob.glob(os.path.join(base_dir, custom_pattern))

        # Filter directories to only include those with CI_results.nc
        valid_custom_dirs = []
        for dir_path in custom_dirs:
            ci_results_path = os.path.join(dir_path, "CI_results.nc")
            if os.path.exists(ci_results_path):
                valid_custom_dirs.append(dir_path)
            else:
                logger.warning(f"Directory {dir_path} found but missing CI_results.nc")

        # Assume custom pattern directories are weather typing results
        result_dirs = [(d, True) for d in valid_custom_dirs]

        logger.info(f"Found directories:")
        logger.info(f"  - {custom_pattern} (with CI_results.nc): {len(valid_custom_dirs)}")

    else:
        # Use standard patterns
        wo_wt_dirs = glob.glob(os.path.join(base_dir, "regionalization_wo_WT"))
        w_wt_dirs = glob.glob(os.path.join(base_dir, "regionalization_w_WT_*"))

        # Also search for run_w* directories as a default
        run_w_dirs = glob.glob(os.path.join(base_dir, "run_w*"))

        # Filter run_w* directories to only include those with CI_results.nc
        valid_run_w_dirs = []
        for dir_path in run_w_dirs:
            ci_results_path = os.path.join(dir_path, "CI_results.nc")
            if os.path.exists(ci_results_path):
                valid_run_w_dirs.append(dir_path)
            else:
                logger.warning(f"Directory {dir_path} found but missing CI_results.nc")

        # Create list of tuples (directory_path, is_weather_typing)
        # Assume run_w* directories are weather typing results
        result_dirs = (
            [(d, False) for d in wo_wt_dirs] +
            [(d, True) for d in w_wt_dirs] +
            [(d, True) for d in valid_run_w_dirs]
        )

        logger.info(f"Found directories:")
        logger.info(f"  - regionalization_wo_WT: {len(wo_wt_dirs)}")
        logger.info(f"  - regionalization_w_WT_*: {len(w_wt_dirs)}")
        logger.info(f"  - run_w* (with CI_results.nc): {len(valid_run_w_dirs)}")

    return result_dirs

def run_heterogeneity_analysis(precip_path, cluster_dir, output_dir, max_clusters, nsim, seed, log_level):
    """
    Run heterogeneity analysis for a specific regionalization result.
    
    Parameters:
    -----------
    precip_path : str
        Path to the precipitation data file
    cluster_dir : str
        Directory containing cluster results
    output_dir : str
        Directory to save heterogeneity results
    max_clusters : int
        Maximum number of clusters to analyze
    nsim : int
        Number of simulations
    seed : int
        Random seed
    log_level : str
        Logging level
    
    Returns:
    --------
    bool
        True if analysis was successful, False otherwise
    """
    # Check if CI_results.nc exists in the cluster directory
    cluster_path = os.path.join(cluster_dir, "CI_results.nc")
    if not os.path.exists(cluster_path):
        logger.warning(f"Cluster file not found: {cluster_path}")
        return False
    
    # Create command to run heterogeneity analysis
    cmd = [
        sys.executable,
        os.path.join(os.path.dirname(__file__), "run_heterogeneity_analysis.py"),
        "--precip-path", precip_path,
        "--cluster-path", cluster_path,
        "--output-dir", output_dir,
        "--max-clusters", str(max_clusters),
        "--nsim", str(nsim),
        "--seed", str(seed),
        "--remap-clusters",
        "--log-level", log_level
    ]
    
    # Run the command
    try:
        logger.info(f"Running heterogeneity analysis for {cluster_dir}")
        logger.info(f"Command: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        # Stream output to logger
        for line in process.stdout:
            logger.info(line.strip())
        
        # Wait for process to complete
        process.wait()
        
        # Check return code
        if process.returncode != 0:
            logger.error(f"Heterogeneity analysis failed for {cluster_dir}")
            for line in process.stderr:
                logger.error(line.strip())
            return False
        
        logger.info(f"Heterogeneity analysis completed for {cluster_dir}")
        return True
    
    except Exception as e:
        logger.error(f"Error running heterogeneity analysis for {cluster_dir}: {str(e)}")
        return False

def main():
    """Run the heterogeneity analysis workflow."""
    # Parse command line arguments
    args = parse_args()
    
    # Setup logging
    log_level = getattr(logging, args.log_level)
    setup_logging(log_level=log_level)
    
    logger.info("Starting heterogeneity analysis workflow")
    logger.info(f"Precipitation data: {args.precip_path}")
    logger.info(f"Base directory: {args.base_dir}")
    logger.info(f"Output base directory: {args.output_base_dir}")
    
    try:
        # Find regionalization directories
        region_dirs = find_regionalization_dirs(args.base_dir, args.pattern)
        logger.info(f"Found {len(region_dirs)} regionalization directories")
        
        if not region_dirs:
            logger.warning("No regionalization directories found. Run regionalization scripts first.")
            sys.exit(1)
        
        # Create output base directory if it doesn't exist
        os.makedirs(args.output_base_dir, exist_ok=True)
        
        # Process each regionalization directory
        results = []
        for region_dir, is_wt in region_dirs:
            # Create output directory name
            dir_name = os.path.basename(region_dir)
            output_dir = os.path.join(args.output_base_dir, dir_name)
            
            # Run heterogeneity analysis
            success = run_heterogeneity_analysis(
                args.precip_path,
                region_dir,
                output_dir,
                args.max_clusters,
                args.nsim,
                args.seed,
                args.log_level
            )
            
            results.append((region_dir, success))
        
        # Print summary
        logger.info("Heterogeneity analysis workflow completed")
        logger.info("Summary:")
        for region_dir, success in results:
            status = "SUCCESS" if success else "FAILED"
            logger.info(f"{region_dir}: {status}")
        
    except Exception as e:
        logger.error(f"Error in heterogeneity analysis workflow: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
