{"cells": [{"cell_type": "code", "execution_count": 1, "id": "60ec2981", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/coe/lib/python3.10/site-packages/pandas/core/arrays/masked.py:60: UserWarning: Pandas requires version '1.3.6' or newer of 'bottleneck' (version '1.3.2' currently installed).\n", "  from pandas.core import (\n"]}], "source": ["import xarray as xr\n", "import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "278980ec", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(\n", "    --jp-content-font-color0,\n", "    var(--pst-color-text-base rgba(0, 0, 0, 1))\n", "  );\n", "  --xr-font-color2: var(\n", "    --jp-content-font-color2,\n", "    var(--pst-color-text-base, rgba(0, 0, 0, 0.54))\n", "  );\n", "  --xr-font-color3: var(\n", "    --jp-content-font-color3,\n", "    var(--pst-color-text-base, rgba(0, 0, 0, 0.38))\n", "  );\n", "  --xr-border-color: var(\n", "    --jp-border-color2,\n", "    hsl(from var(--pst-color-on-background, white) h s calc(l - 10))\n", "  );\n", "  --xr-disabled-color: var(\n", "    --jp-layout-color3,\n", "    hsl(from var(--pst-color-on-background, white) h s calc(l - 40))\n", "  );\n", "  --xr-background-color: var(\n", "    --jp-layout-color0,\n", "    var(--pst-color-on-background, white)\n", "  );\n", "  --xr-background-color-row-even: var(\n", "    --jp-layout-color1,\n", "    hsl(from var(--pst-color-on-background, white) h s calc(l - 5))\n", "  );\n", "  --xr-background-color-row-odd: var(\n", "    --jp-layout-color2,\n", "    hsl(from var(--pst-color-on-background, white) h s calc(l - 15))\n", "  );\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: var(\n", "    --jp-content-font-color0,\n", "    var(--pst-color-text-base, rgba(255, 255, 255, 1))\n", "  );\n", "  --xr-font-color2: var(\n", "    --jp-content-font-color2,\n", "    var(--pst-color-text-base, rgba(255, 255, 255, 0.54))\n", "  );\n", "  --xr-font-color3: var(\n", "    --jp-content-font-color3,\n", "    var(--pst-color-text-base, rgba(255, 255, 255, 0.38))\n", "  );\n", "  --xr-border-color: var(\n", "    --jp-border-color2,\n", "    hsl(from var(--pst-color-on-background, #111111) h s calc(l + 10))\n", "  );\n", "  --xr-disabled-color: var(\n", "    --jp-layout-color3,\n", "    hsl(from var(--pst-color-on-background, #111111) h s calc(l + 40))\n", "  );\n", "  --xr-background-color: var(\n", "    --jp-layout-color0,\n", "    var(--pst-color-on-background, #111111)\n", "  );\n", "  --xr-background-color-row-even: var(\n", "    --jp-layout-color1,\n", "    hsl(from var(--pst-color-on-background, #111111) h s calc(l + 5))\n", "  );\n", "  --xr-background-color-row-odd: var(\n", "    --jp-layout-color2,\n", "    hsl(from var(--pst-color-on-background, #111111) h s calc(l + 15))\n", "  );\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "  border: 2px solid transparent !important;\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0) !important;\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  border-color: var(--xr-background-color-row-odd);\n", "  margin-bottom: 0;\n", "  padding-top: 2px;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "  border-color: var(--xr-background-color-row-even);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  border-top: 2px dotted var(--xr-background-color);\n", "  padding-bottom: 20px !important;\n", "  padding-top: 10px !important;\n", "}\n", "\n", ".xr-var-attrs-in + label,\n", ".xr-var-data-in + label,\n", ".xr-index-data-in + label {\n", "  padding: 0 1px;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-data > pre,\n", ".xr-index-data > pre,\n", ".xr-var-data > table > tbody > tr {\n", "  background-color: transparent !important;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "\n", ".xr-var-attrs-in:checked + label > .xr-icon-file-text2,\n", ".xr-var-data-in:checked + label > .xr-icon-database,\n", ".xr-index-data-in:checked + label > .xr-icon-database {\n", "  color: var(--xr-font-color0);\n", "  filter: drop-shadow(1px 1px 5px var(--xr-font-color2));\n", "  stroke-width: 0.8px;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 2GB\n", "Dimensions:     (valid_time: 14976, latitude: 121, longitude: 281)\n", "Coordinates:\n", "    number      int64 8B ...\n", "  * valid_time  (valid_time) datetime64[ns] 120kB 1980-01-01T12:00:00 ... 202...\n", "  * latitude    (latitude) float64 968B 50.0 49.75 49.5 ... 20.5 20.25 20.0\n", "  * longitude   (longitude) float64 2kB -130.0 -129.8 -129.5 ... -60.25 -60.0\n", "    expver      (valid_time) &lt;U4 240kB ...\n", "Data variables:\n", "    msl         (valid_time, latitude, longitude) float32 2GB ...\n", "Attributes:\n", "    GRIB_centre:             ecmf\n", "    GRIB_centreDescription:  European Centre for Medium-Range Weather Forecasts\n", "    GRIB_subCentre:          0\n", "    Conventions:             CF-1.7\n", "    institution:             European Centre for Medium-Range Weather Forecasts\n", "    history:                 2025-04-12T14:28 GRIB to CDM+CF via cfgrib-0.9.1...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-a570237f-c3af-43f5-90c0-8be461ec9a2d' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-a570237f-c3af-43f5-90c0-8be461ec9a2d' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>valid_time</span>: 14976</li><li><span class='xr-has-index'>latitude</span>: 121</li><li><span class='xr-has-index'>longitude</span>: 281</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-fb9b6591-10ad-421a-9c65-81141ecf2481' class='xr-section-summary-in' type='checkbox'  checked><label for='section-fb9b6591-10ad-421a-9c65-81141ecf2481' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>number</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-58cd600b-0a09-493b-a34c-6022f378732f' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-58cd600b-0a09-493b-a34c-6022f378732f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-cdcea211-d84c-4bc1-b0ef-b6f8481f52c0' class='xr-var-data-in' type='checkbox'><label for='data-cdcea211-d84c-4bc1-b0ef-b6f8481f52c0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>ensemble member numerical id</dd><dt><span>units :</span></dt><dd>1</dd><dt><span>standard_name :</span></dt><dd>realization</dd></dl></div><div class='xr-var-data'><pre>[1 values with dtype=int64]</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>valid_time</span></div><div class='xr-var-dims'>(valid_time)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>1980-01-01T12:00:00 ... 2020-12-...</div><input id='attrs-8fc143ab-b773-4247-a691-bdd6eaed121b' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-8fc143ab-b773-4247-a691-bdd6eaed121b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-67a66a9d-41fa-4aa2-a94c-548ac4fe21f0' class='xr-var-data-in' type='checkbox'><label for='data-67a66a9d-41fa-4aa2-a94c-548ac4fe21f0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>long_name :</span></dt><dd>time</dd><dt><span>standard_name :</span></dt><dd>time</dd></dl></div><div class='xr-var-data'><pre>array([&#x27;1980-01-01T12:00:00.000000000&#x27;, &#x27;1980-01-02T12:00:00.000000000&#x27;,\n", "       &#x27;1980-01-03T12:00:00.000000000&#x27;, ..., &#x27;2020-12-29T12:00:00.000000000&#x27;,\n", "       &#x27;2020-12-30T12:00:00.000000000&#x27;, &#x27;2020-12-31T12:00:00.000000000&#x27;],\n", "      dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>latitude</span></div><div class='xr-var-dims'>(latitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>50.0 49.75 49.5 ... 20.5 20.25 20.0</div><input id='attrs-be8c29bf-fcb5-4143-b7d0-7e21190bdeba' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-be8c29bf-fcb5-4143-b7d0-7e21190bdeba' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0f21e1ea-5864-41bb-9d76-7b2aaafaa8c9' class='xr-var-data-in' type='checkbox'><label for='data-0f21e1ea-5864-41bb-9d76-7b2aaafaa8c9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>units :</span></dt><dd>degrees_north</dd><dt><span>standard_name :</span></dt><dd>latitude</dd><dt><span>long_name :</span></dt><dd>latitude</dd><dt><span>stored_direction :</span></dt><dd>decreasing</dd></dl></div><div class='xr-var-data'><pre>array([50.  , 49.75, 49.5 , 49.25, 49.  , 48.75, 48.5 , 48.25, 48.  , 47.75,\n", "       47.5 , 47.25, 47.  , 46.75, 46.5 , 46.25, 46.  , 45.75, 45.5 , 45.25,\n", "       45.  , 44.75, 44.5 , 44.25, 44.  , 43.75, 43.5 , 43.25, 43.  , 42.75,\n", "       42.5 , 42.25, 42.  , 41.75, 41.5 , 41.25, 41.  , 40.75, 40.5 , 40.25,\n", "       40.  , 39.75, 39.5 , 39.25, 39.  , 38.75, 38.5 , 38.25, 38.  , 37.75,\n", "       37.5 , 37.25, 37.  , 36.75, 36.5 , 36.25, 36.  , 35.75, 35.5 , 35.25,\n", "       35.  , 34.75, 34.5 , 34.25, 34.  , 33.75, 33.5 , 33.25, 33.  , 32.75,\n", "       32.5 , 32.25, 32.  , 31.75, 31.5 , 31.25, 31.  , 30.75, 30.5 , 30.25,\n", "       30.  , 29.75, 29.5 , 29.25, 29.  , 28.75, 28.5 , 28.25, 28.  , 27.75,\n", "       27.5 , 27.25, 27.  , 26.75, 26.5 , 26.25, 26.  , 25.75, 25.5 , 25.25,\n", "       25.  , 24.75, 24.5 , 24.25, 24.  , 23.75, 23.5 , 23.25, 23.  , 22.75,\n", "       22.5 , 22.25, 22.  , 21.75, 21.5 , 21.25, 21.  , 20.75, 20.5 , 20.25,\n", "       20.  ])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>longitude</span></div><div class='xr-var-dims'>(longitude)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>-130.0 -129.8 ... -60.25 -60.0</div><input id='attrs-da58ca5b-51bb-46a0-9640-dceef4cbd511' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-da58ca5b-51bb-46a0-9640-dceef4cbd511' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-015297c3-32ef-4bab-a156-d3a2829a18d5' class='xr-var-data-in' type='checkbox'><label for='data-015297c3-32ef-4bab-a156-d3a2829a18d5' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>units :</span></dt><dd>degrees_east</dd><dt><span>standard_name :</span></dt><dd>longitude</dd><dt><span>long_name :</span></dt><dd>longitude</dd></dl></div><div class='xr-var-data'><pre>array([-130.  , -129.75, -129.5 , ...,  -60.5 ,  -60.25,  -60.  ])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>expver</span></div><div class='xr-var-dims'>(valid_time)</div><div class='xr-var-dtype'>&lt;U4</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-beca093f-22c4-4369-8895-c100cc1e9f4c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-beca093f-22c4-4369-8895-c100cc1e9f4c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ca536d50-e07c-4dd1-9b81-135a57e28c58' class='xr-var-data-in' type='checkbox'><label for='data-ca536d50-e07c-4dd1-9b81-135a57e28c58' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>[14976 values with dtype=&lt;U4]</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-6697939b-85d7-4d3d-9c4b-3e4b52e8f110' class='xr-section-summary-in' type='checkbox'  checked><label for='section-6697939b-85d7-4d3d-9c4b-3e4b52e8f110' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>msl</span></div><div class='xr-var-dims'>(valid_time, latitude, longitude)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>...</div><input id='attrs-24d955ac-dfc1-4c80-b0e8-b7cd951ab4d3' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-24d955ac-dfc1-4c80-b0e8-b7cd951ab4d3' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-1835314b-6e04-48ff-84e0-7384c3a78e09' class='xr-var-data-in' type='checkbox'><label for='data-1835314b-6e04-48ff-84e0-7384c3a78e09' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>GRIB_paramId :</span></dt><dd>151</dd><dt><span>GRIB_dataType :</span></dt><dd>an</dd><dt><span>GRIB_numberOfPoints :</span></dt><dd>34001</dd><dt><span>GRIB_typeOfLevel :</span></dt><dd>surface</dd><dt><span>GRIB_stepUnits :</span></dt><dd>1</dd><dt><span>GRIB_stepType :</span></dt><dd>instant</dd><dt><span>GRIB_gridType :</span></dt><dd>regular_ll</dd><dt><span>GRIB_uvRelativeToGrid :</span></dt><dd>0</dd><dt><span>GRIB_NV :</span></dt><dd>0</dd><dt><span>GRIB_Nx :</span></dt><dd>281</dd><dt><span>GRIB_Ny :</span></dt><dd>121</dd><dt><span>GRIB_cfName :</span></dt><dd>air_pressure_at_mean_sea_level</dd><dt><span>GRIB_cfVarName :</span></dt><dd>msl</dd><dt><span>GRIB_gridDefinitionDescription :</span></dt><dd>Latitude/Longitude Grid</dd><dt><span>GRIB_iDirectionIncrementInDegrees :</span></dt><dd>0.25</dd><dt><span>GRIB_iScansNegatively :</span></dt><dd>0</dd><dt><span>GRIB_jDirectionIncrementInDegrees :</span></dt><dd>0.25</dd><dt><span>GRIB_jPointsAreConsecutive :</span></dt><dd>0</dd><dt><span>GRIB_jScansPositively :</span></dt><dd>0</dd><dt><span>GRIB_latitudeOfFirstGridPointInDegrees :</span></dt><dd>50.0</dd><dt><span>GRIB_latitudeOfLastGridPointInDegrees :</span></dt><dd>20.0</dd><dt><span>GRIB_longitudeOfFirstGridPointInDegrees :</span></dt><dd>-130.0</dd><dt><span>GRIB_longitudeOfLastGridPointInDegrees :</span></dt><dd>-60.0</dd><dt><span>GRIB_missingValue :</span></dt><dd>3.4028234663852886e+38</dd><dt><span>GRIB_name :</span></dt><dd>Mean sea level pressure</dd><dt><span>GRIB_shortName :</span></dt><dd>msl</dd><dt><span>GRIB_totalNumber :</span></dt><dd>0</dd><dt><span>GRIB_units :</span></dt><dd>Pa</dd><dt><span>long_name :</span></dt><dd>Mean sea level pressure</dd><dt><span>units :</span></dt><dd>Pa</dd><dt><span>standard_name :</span></dt><dd>air_pressure_at_mean_sea_level</dd><dt><span>GRIB_surface :</span></dt><dd>0.0</dd></dl></div><div class='xr-var-data'><pre>[509198976 values with dtype=float32]</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-77e4b6e2-da90-4809-9fe1-d66a14f1a67a' class='xr-section-summary-in' type='checkbox'  ><label for='section-77e4b6e2-da90-4809-9fe1-d66a14f1a67a' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>valid_time</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-9e4a044e-01a6-4ea2-847f-6eae94e6b030' class='xr-index-data-in' type='checkbox'/><label for='index-9e4a044e-01a6-4ea2-847f-6eae94e6b030' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;1980-01-01 12:00:00&#x27;, &#x27;1980-01-02 12:00:00&#x27;,\n", "               &#x27;1980-01-03 12:00:00&#x27;, &#x27;1980-01-04 12:00:00&#x27;,\n", "               &#x27;1980-01-05 12:00:00&#x27;, &#x27;1980-01-06 12:00:00&#x27;,\n", "               &#x27;1980-01-07 12:00:00&#x27;, &#x27;1980-01-08 12:00:00&#x27;,\n", "               &#x27;1980-01-09 12:00:00&#x27;, &#x27;1980-01-10 12:00:00&#x27;,\n", "               ...\n", "               &#x27;2020-12-22 12:00:00&#x27;, &#x27;2020-12-23 12:00:00&#x27;,\n", "               &#x27;2020-12-24 12:00:00&#x27;, &#x27;2020-12-25 12:00:00&#x27;,\n", "               &#x27;2020-12-26 12:00:00&#x27;, &#x27;2020-12-27 12:00:00&#x27;,\n", "               &#x27;2020-12-28 12:00:00&#x27;, &#x27;2020-12-29 12:00:00&#x27;,\n", "               &#x27;2020-12-30 12:00:00&#x27;, &#x27;2020-12-31 12:00:00&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;valid_time&#x27;, length=14976, freq=None))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>latitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-2a42759f-d33b-4b1b-a5ac-6c333b5fee86' class='xr-index-data-in' type='checkbox'/><label for='index-2a42759f-d33b-4b1b-a5ac-6c333b5fee86' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ 50.0, 49.75,  49.5, 49.25,  49.0, 48.75,  48.5, 48.25,  48.0, 47.75,\n", "       ...\n", "       22.25,  22.0, 21.75,  21.5, 21.25,  21.0, 20.75,  20.5, 20.25,  20.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;latitude&#x27;, length=121))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>longitude</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-ba1222c3-1606-4ee2-b30a-d94344f6451d' class='xr-index-data-in' type='checkbox'/><label for='index-ba1222c3-1606-4ee2-b30a-d94344f6451d' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ -130.0, -129.75,  -129.5, -129.25,  -129.0, -128.75,  -128.5, -128.25,\n", "        -128.0, -127.75,\n", "       ...\n", "        -62.25,   -62.0,  -61.75,   -61.5,  -61.25,   -61.0,  -60.75,   -60.5,\n", "        -60.25,   -60.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;longitude&#x27;, length=281))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-b9f62675-01df-4a2c-b1b7-ba1ceab7a66c' class='xr-section-summary-in' type='checkbox'  checked><label for='section-b9f62675-01df-4a2c-b1b7-ba1ceab7a66c' class='xr-section-summary' >Attributes: <span>(6)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>GRIB_centre :</span></dt><dd>ecmf</dd><dt><span>GRIB_centreDescription :</span></dt><dd>European Centre for Medium-Range Weather Forecasts</dd><dt><span>GRIB_subCentre :</span></dt><dd>0</dd><dt><span>Conventions :</span></dt><dd>CF-1.7</dd><dt><span>institution :</span></dt><dd>European Centre for Medium-Range Weather Forecasts</dd><dt><span>history :</span></dt><dd>2025-04-12T14:28 GRIB to CDM+CF via cfgrib-********/ecCodes-2.39.0 with {&quot;source&quot;: &quot;tmpobnp0k1t/data.grib&quot;, &quot;filter_by_keys&quot;: {&quot;stream&quot;: [&quot;oper&quot;], &quot;stepType&quot;: [&quot;instant&quot;]}, &quot;encode_cf&quot;: [&quot;parameter&quot;, &quot;time&quot;, &quot;geography&quot;, &quot;vertical&quot;]}</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 2GB\n", "Dimensions:     (valid_time: 14976, latitude: 121, longitude: 281)\n", "Coordinates:\n", "    number      int64 8B ...\n", "  * valid_time  (valid_time) datetime64[ns] 120kB 1980-01-01T12:00:00 ... 202...\n", "  * latitude    (latitude) float64 968B 50.0 49.75 49.5 ... 20.5 20.25 20.0\n", "  * longitude   (longitude) float64 2kB -130.0 -129.8 -129.5 ... -60.25 -60.0\n", "    expver      (valid_time) <U4 240kB ...\n", "Data variables:\n", "    msl         (valid_time, latitude, longitude) float32 2GB ...\n", "Attributes:\n", "    GRIB_centre:             ecmf\n", "    GRIB_centreDescription:  European Centre for Medium-Range Weather Forecasts\n", "    GRIB_subCentre:          0\n", "    Conventions:             CF-1.7\n", "    institution:             European Centre for Medium-Range Weather Forecasts\n", "    history:                 2025-04-12T14:28 GRIB to CDM+CF via cfgrib-0.9.1..."]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["ds = xr.open_dataset(\"sample_data/mslp_CONUS_1979_2020.nc\")\n", "ds"]}, {"cell_type": "code", "execution_count": 8, "id": "83a59827", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x1554081674f0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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******************************/HgDfs/dA3JGmA3h7vuVItXtgtz67hB2J6s3vsRfs/QnCLge7baNB7yn3FctmdEwjr35267rRBWUWK4Aetdl7U5bVC82KOi9t5yoduAzqXa+92v6yaQRG88LcAtifVmj13oP3yoAmW+V2JG5ihCo59kvvbjY6RmB379hkSh7Lc9/66dkxvbpke1yzmIG9eyOSCWxlOw/yuv0ey6a3/95cN/Xxje2bS2IvsNvt162zle09ZT5qv6d0seebj4/PvDZQvibYr1UNc7I3egFw9mYHXYiH8N6t/XKJen/IuAm4Ax3lbnxwHf7wax/sNoFq+6j336p213t3wL4HWCN1p8+ht01veQ3qFvK6/Yplo+Ma9o2NmdJfmyXz2B/MaqXS6r57qrzaZ0ehe9scgbqFedzeq2DR2/QrYh6ydr3ud/I9MOv3wP5IaeTMmln13mfWi6wX3/2aVs1NwJ3QQh2YK2Jtzaze8caGtWP08p7PPhpJOIujidO97Y5Avres9LPPvpFYSc4eiWur7lGf/n4mv7x6f/zDQUi6Hdzl9bad5ROgl+**************************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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.imshow(ds['z'][1,0])"]}, {"cell_type": "code", "execution_count": 6, "id": "346ae659", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'inv<PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/var/tmp/pbs.879203.imgt1/ipykernel_1954046/575158081.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mnumpy\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mpandas\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m \u001b[0;32mfrom\u001b[0m \u001b[0minvdisttree\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mInvdisttree\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mmatplotlib\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0mmatplotlib\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mpyplot\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mplt\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'invdisttree'"]}], "source": ["# some dependencies used throughout\n", "from osgeo import gdal\n", "import numpy as np\n", "import pandas as pd\n", "from invdisttree import Invdisttree\n", "import matplotlib\n", "from matplotlib import pyplot as plt\n", "import netCDF4 as nc\n", "import geopandas as gpd\n", "\n", "# GFDL grid\n", "\n", "_path_gfdl = 'mslp_CONUS_1979_2020.nc'\n", "gfdl_file = nc.Dataset(_path_gfdl,'r')\n", "\n", "gfdl_shape = gfdl_file.variables['longitude'][:].shape\n", "\n", "gfdl_d0 = gfdl_file.variables['longitude'][:].data.flatten()\n", "gfdl_d1 = gfdl_file.variables['latitude'][::-1].data.flatten()\n", "\n", "gfdl_d0, gfdl_d1 = np.meshgrid(gfdl_d0,gfdl_d1)\n", "\n", "gfdl_d0 = gfdl_d0.flatten()\n", "gfdl_d1 = gfdl_d1.flatten()\n", "\n", "# CCSM grid\n", "\n", "_path_ccsm = '/lcrc/project/Hydro-model/sgev/data/WRF_CCSM_lat_lon.nc'\n", "ccsm_file = nc.Dataset(_path_ccsm,'r')\n", "\n", "ccsm_shape = ccsm_file.variables['longitude'].shape\n", "\n", "ccsm_d0 = ccsm_file.variables['longitude'][:].flatten()\n", "ccsm_d1 = ccsm_file.variables['latitude'][:].flatten()\n", "\n", "# Interpolation\n", "\n", "from invdisttree import Invdisttree\n", "\n", "X = np.hstack((gfdl_d0.reshape(-1,1), gfdl_d1.reshape(-1,1)))\n", "Xh = np.hstack((ccsm_d0.reshape(-1,1), ccsm_d1.reshape(-1,1)))"]}, {"cell_type": "code", "execution_count": null, "id": "7a4f9f9d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}