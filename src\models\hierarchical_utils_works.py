# -*- coding: utf-8 -*-
import numpy as np
from sklearn.cluster import AgglomerativeClustering
from sklearn.metrics import pairwise_distances
from joblib import <PERSON>llel, delayed
import logging

logger = logging.getLogger(__name__)

import numpy as np
from sklearn.decomposition import PCA
from sklearn.utils.extmath import randomized_svd
from sklearn.cluster import AgglomerativeClustering
from joblib import Parallel, delayed
import logging

logger = logging.getLogger(__name__)

import numpy as np
from sklearn.decomposition import PCA
from sklearn.cluster import AgglomerativeClustering
from joblib import Parallel, delayed
import logging

logger = logging.getLogger(__name__)

use_numba = False


def run_hierarchical_simulation_fast(PC, nclus, c, sim_index):
    """
    Fast hierarchical clustering simulation.

    Parameters:
    -----------
    PC : numpy.ndarray
        Principal components (or input matrix)
    nclus : int
        Number of clusters
    c : int
        Number of features
    sim_index : int
        Simulation index

    Returns:
    --------
    tuple
        (cluster labels, standardized centroids)
    """
    try:
        mean_cluster = np.zeros((nclus, c))

        hierarchical = AgglomerativeClustering(
            n_clusters=nclus,
            linkage='ward',
            memory=None,
            compute_full_tree=False
        )
        labels = hierarchical.fit_predict(PC)

        for j in range(nclus):
            points_in_cluster = PC[labels == j]
            if points_in_cluster.shape[0] > 1:
                mean_cluster[j, :] = np.mean(points_in_cluster, axis=0)
            elif points_in_cluster.shape[0] == 1:
                mean_cluster[j, :] = points_in_cluster[0]
            else:
                mean_cluster[j, :] = np.nan

        # Standardize centroids
        nan_mean = np.nanmean(mean_cluster.T, axis=0)
        nan_std = np.nanstd(mean_cluster.T, axis=0, ddof=1)
        nan_std = np.where(nan_std == 0, 1, nan_std)
        mean_cluster2 = (mean_cluster.T - nan_mean) / nan_std

        return labels, mean_cluster2.flatten("F")

    except Exception as e:
        logger.warning(f"Fast hierarchical clustering simulation {sim_index} failed: {e}")
        labels = np.random.randint(0, nclus, size=PC.shape[0])
        centroids = np.random.normal(0, 1, size=nclus * c)
        return labels, centroids


def hierarchical_ci(X=None, stand=None, weighting=None, prop=None, nclus=None, nsim=None, n_jobs=64):
    """
    Perform hierarchical clustering with classifiability index calculation.
    """
    r, c = X.shape

    if r > 10000:
        nsim = min(nsim, 20)
        n_jobs = min(n_jobs, 4)
        logger.warning(f"Very large dataset ({r}). Reducing nsim={nsim}, n_jobs={n_jobs}")
    elif r > 5000:
        nsim = min(nsim, 30)
        n_jobs = min(n_jobs, 6)
    elif r > 1000:
        nsim = min(nsim, 50)
        n_jobs = min(n_jobs, 8)

    if stand:
        nan_mean = np.nanmean(X, axis=0)
        nan_std = np.nanstd(X, axis=0, ddof=1)
        X = (X - nan_mean) / nan_std

    if prop:
        logger.info("Using PCA for EOF filtering")
        pca = PCA(n_components=prop, svd_solver='randomized', random_state=0)
        PC = pca.fit_transform(X)
        c_used = PC.shape[1]
    else:
        PC = X
        c_used = c

    logger.info(f"Running {nsim} simulations with {n_jobs} jobs, using {nclus} clusters")

    results = Parallel(n_jobs=n_jobs, verbose=1, backend='loky')(
        delayed(run_hierarchical_simulation_fast)(PC, nclus, c_used, i)
        for i in range(nsim)
    )

    k = np.array([res[0] for res in results]).T
    MC = np.stack([res[1] for res in results], axis=1)

    logger.info("Calculating classifiability index...")
    if use_numba:
        ACCmax = compute_accmax(MC, nclus, nsim, c_used)
    else:
        ACCmax = np.zeros((nclus, nsim))
        for i in range(nclus):
            for j in range(nsim):
                sample1 = MC[i * c_used:(i + 1) * c_used, j]
                other = np.delete(MC, j, axis=1).reshape(c_used, (nsim - 1) * nclus, order="F")
                sample1 = np.nan_to_num(sample1)
                other = np.nan_to_num(other)
                if c_used > 1:
                    ACC = (1 / (c_used - 1)) * sample1.T @ other
                    ACC = ACC.reshape(nclus, nsim - 1, order="F")
                    ACCmax[i, j] = np.mean(np.max(ACC, axis=0))

    part = np.argmax(np.mean(ACCmax, axis=0))
    CI = np.mean(ACCmax)
    K = k[:, part]

    logger.info(f"Hierarchical clustering completed. CI = {CI:.4f}")
    return K, CI


def run_hierarchical_with_linkage_matrix(X, PC, nclus, c, sim_index, return_linkage=False):
    """
    Run hierarchical clustering and optionally return linkage matrix for dendrogram plotting.
    
    Parameters:
    -----------
    X : numpy.ndarray
        Input data matrix
    PC : numpy.ndarray
        Principal components
    nclus : int
        Number of clusters
    c : int
        Number of features
    sim_index : int
        Simulation index
    return_linkage : bool, optional
        Whether to return linkage matrix
        
    Returns:
    --------
    tuple
        (cluster labels, standardized centroids, linkage matrix if requested)
    """
    from scipy.cluster.hierarchy import linkage, fcluster
    from scipy.spatial.distance import pdist
    
    mean_cluster = np.zeros((nclus, c))
    
    # Compute linkage matrix using Ward method
    distances = pdist(X, metric='euclidean')
    linkage_matrix = linkage(distances, method='ward')
    
    # Get cluster labels for specified number of clusters
    labels = fcluster(linkage_matrix, nclus, criterion='maxclust') - 1  # Convert to 0-based indexing

    for j in range(nclus):
        points_in_cluster = PC[labels == j]
        if points_in_cluster.shape[0] > 1:
            mean_cluster[j, :] = np.mean(points_in_cluster, axis=0)
        elif points_in_cluster.shape[0] == 1:
            mean_cluster[j, :] = points_in_cluster[0]
        else:
            mean_cluster[j, :] = np.nan

    # Standardize centroids
    nan_mean = np.nanmean(mean_cluster.T, axis=0)
    nan_std = np.nanstd(mean_cluster.T, axis=0, ddof=1)
    mean_cluster2 = (mean_cluster.T - nan_mean) / nan_std

    if return_linkage:
        return labels, mean_cluster2.flatten("F"), linkage_matrix
    else:
        return labels, mean_cluster2.flatten("F")


def plot_dendrogram(linkage_matrix, output_path, max_clusters=10):
    """
    Plot dendrogram from linkage matrix.
    
    Parameters:
    -----------
    linkage_matrix : numpy.ndarray
        Linkage matrix from hierarchical clustering
    output_path : str
        Path to save the dendrogram plot
    max_clusters : int, optional
        Maximum number of clusters to highlight
    """
    import matplotlib.pyplot as plt
    from scipy.cluster.hierarchy import dendrogram
    
    plt.figure(figsize=(12, 8))
    
    # Create dendrogram
    dend = dendrogram(
        linkage_matrix,
        truncate_mode='level',
        p=max_clusters,
        show_leaf_counts=True,
        leaf_rotation=90,
        leaf_font_size=10
    )
    
    plt.title('Hierarchical Clustering Dendrogram (Ward Linkage)')
    plt.xlabel('Sample Index or (Cluster Size)')
    plt.ylabel('Distance')
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Saved dendrogram to {output_path}")
