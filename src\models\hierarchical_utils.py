# -*- coding: utf-8 -*-
import numpy as np
from sklearn.cluster import AgglomerativeClustering
from sklearn.metrics import pairwise_distances
from joblib import <PERSON>llel, delayed
import logging
from sklearn.utils.extmath import randomized_svd
from fastcluster import linkage
from scipy.cluster.hierarchy import fcluster


logger = logging.getLogger(__name__)

def run_hierarchical_simulation(X, PC, nclus, c, sim_index):
    """
    Run a single hierarchical clustering simulation and return cluster assignments and cluster centroids.

    Parameters:
    -----------
    X : numpy.ndarray
        Input data matrix
    PC : numpy.ndarray
        Principal components
    nclus : int
        Number of clusters
    c : int
        Number of features
    sim_index : int
        Simulation index

    Returns:
    --------
    tuple
        (cluster labels, standardized centroids)
    """
    try:
        mean_cluster = np.zeros((nclus, c))

        # Use Ward linkage for hierarchical clustering with memory optimization
        # Note: Ward linkage only works with Euclidean distance, so no metric parameter needed
        hierarchical = AgglomerativeClustering(
            n_clusters=nclus,
            linkage='ward',
            memory=None,  # Don't cache distance matrix to save memory
            compute_full_tree='auto'  # Let sklearn decide based on data size
        )
        labels = hierarchical.fit_predict(X)

        for j in range(nclus):
            points_in_cluster = PC[labels == j]
            if points_in_cluster.shape[0] > 1:
                mean_cluster[j, :] = np.mean(points_in_cluster, axis=0)
            elif points_in_cluster.shape[0] == 1:
                mean_cluster[j, :] = points_in_cluster[0]
            else:
                mean_cluster[j, :] = np.nan

        # Standardize centroids
        nan_mean = np.nanmean(mean_cluster.T, axis=0)
        nan_std = np.nanstd(mean_cluster.T, axis=0, ddof=1)

        # Avoid division by zero
        nan_std = np.where(nan_std == 0, 1, nan_std)
        mean_cluster2 = (mean_cluster.T - nan_mean) / nan_std

        return labels, mean_cluster2.flatten("F")

    except Exception as e:
        logger.warning(f"Hierarchical clustering simulation {sim_index} failed: {e}")
        # Return random assignment as fallback
        labels = np.random.randint(0, nclus, size=X.shape[0])
        centroids = np.random.normal(0, 1, size=nclus * c)
        return labels, centroids

def hierarchical_clustering_simple(X=None, stand=None, weighting=None, prop=None, nclus=None, nsim=None, n_jobs=64):
    """
    Simple hierarchical clustering
    Parameters:
    -----------
    X : numpy.ndarray
        Input data matrix
    stand : bool, optional
        Whether to standardize the data
    weighting : bool, optional
        Whether to apply weighting (currently unused)
    prop : float, optional
        Proportion of variance to retain in EOF prefiltering
    nclus : int
        Number of clusters
    nsim : int, optional
        Number of simulations (not used for simple clustering)
    n_jobs : int, optional
        Number of parallel jobs (not used for simple clustering)

    Returns:
    --------
    tuple
        (cluster labels, dummy CI value of 1.0)
    """
    r, c = X.shape
    logger.info(f"Simple hierarchical clustering: {r} samples, {c} features, {nclus} clusters")

    X_sub = X
    subsample_idx = None
    use_subsampling=False

    # EOF prefiltering if requested
    if prop:
        logger.info("Performing EOF prefiltering using randomized SVD...")
        # Use subsampled data for EOF if applicable       
        max_components = min(X_sub.shape)
        U, S, Vt = randomized_svd(X_sub, n_components=max_components)
        s_squared = S ** 2
        explained_var_ratio = s_squared / np.sum(s_squared)
        cumulative_variance = np.cumsum(explained_var_ratio)
        n_components = np.searchsorted(cumulative_variance, 0.95) + 1

        U_reduced = np.around(U[:, :n_components], 4)
        S_reduced = np.diag(np.around(S[:n_components], 4))
        PC = U_reduced @ S_reduced

        logger.info(f"Pre-filtering using EOF retaining the first {PC.shape} components done.")
    else:
        PC = X_sub
        logger.info("No EOF prefiltering ...")

    # Perform single hierarchical clustering (no simulations needed)
    logger.info(f"Running hierarchical clustering with {nclus} clusters...")

    try:
#         # Use fast hierarchical clustering
#         hierarchical = AgglomerativeClustering(
#             n_clusters=nclus,
#             linkage='ward',
#             memory=None,
#             compute_full_tree=False,  # Skip full tree for speed
#             compute_distances=False   # Skip distance computation for speed
#         )
#         labels_sub = hierarchical.fit_predict(PC)
        
        Z = linkage(PC, method='ward')  # Much faster
        labels_sub = fcluster(Z, t=nclus, criterion='maxclust')

        # Map subsampled results back to full dataset if needed
        if use_subsampling:
            logger.info("Mapping subsampled results back to full dataset using KNN")
            from sklearn.neighbors import KNeighborsClassifier

            # Use fewer neighbors for speed
            n_neighbors = min(3, subsample_size // 20)
            knn = KNeighborsClassifier(n_neighbors=n_neighbors, weights='uniform', algorithm='ball_tree')
            knn.fit(X[subsample_idx], labels_sub)

            # Predict in chunks to avoid memory issues
            chunk_size = min(10000, r)
            labels = np.zeros(r, dtype=np.int32)
            for start_idx in range(0, r, chunk_size):
                end_idx = min(start_idx + chunk_size, r)
                labels[start_idx:end_idx] = knn.predict(X[start_idx:end_idx])
        else:
            labels = labels_sub

        logger.info(f"Hierarchical clustering completed successfully")

        # Return cluster labels and dummy CI value
        return labels, 1.0

    except Exception as e:
        logger.error(f"Hierarchical clustering failed: {e}")
        # Return random assignment as fallback
        labels = np.random.randint(0, nclus, size=r)
        return labels, 0.0


def run_hierarchical_with_linkage_matrix(X, PC, nclus, c, sim_index, return_linkage=False):
    """
    Run hierarchical clustering and optionally return linkage matrix for dendrogram plotting.
    
    Parameters:
    -----------
    X : numpy.ndarray
        Input data matrix
    PC : numpy.ndarray
        Principal components
    nclus : int
        Number of clusters
    c : int
        Number of features
    sim_index : int
        Simulation index
    return_linkage : bool, optional
        Whether to return linkage matrix
        
    Returns:
    --------
    tuple
        (cluster labels, standardized centroids, linkage matrix if requested)
    """
    from scipy.cluster.hierarchy import linkage, fcluster
    from scipy.spatial.distance import pdist
    
    mean_cluster = np.zeros((nclus, c))
    
    # Compute linkage matrix using Ward method
    distances = pdist(X, metric='euclidean')
    linkage_matrix = linkage(distances, method='ward')
    
    # Get cluster labels for specified number of clusters
    labels = fcluster(linkage_matrix, nclus, criterion='maxclust') - 1  # Convert to 0-based indexing

    for j in range(nclus):
        points_in_cluster = PC[labels == j]
        if points_in_cluster.shape[0] > 1:
            mean_cluster[j, :] = np.mean(points_in_cluster, axis=0)
        elif points_in_cluster.shape[0] == 1:
            mean_cluster[j, :] = points_in_cluster[0]
        else:
            mean_cluster[j, :] = np.nan

    # Standardize centroids
    nan_mean = np.nanmean(mean_cluster.T, axis=0)
    nan_std = np.nanstd(mean_cluster.T, axis=0, ddof=1)
    mean_cluster2 = (mean_cluster.T - nan_mean) / nan_std

    if return_linkage:
        return labels, mean_cluster2.flatten("F"), linkage_matrix
    else:
        return labels, mean_cluster2.flatten("F")


def plot_dendrogram(linkage_matrix, output_path, max_clusters=10):
    """
    Plot dendrogram from linkage matrix.
    
    Parameters:
    -----------
    linkage_matrix : numpy.ndarray
        Linkage matrix from hierarchical clustering
    output_path : str
        Path to save the dendrogram plot
    max_clusters : int, optional
        Maximum number of clusters to highlight
    """
    import matplotlib.pyplot as plt
    from scipy.cluster.hierarchy import dendrogram
    
    plt.figure(figsize=(12, 8))
    
    # Create dendrogram
    dend = dendrogram(
        linkage_matrix,
        truncate_mode='level',
        p=max_clusters,
        show_leaf_counts=True,
        leaf_rotation=90,
        leaf_font_size=10
    )
    
    plt.title('Hierarchical Clustering Dendrogram (Ward Linkage)')
    plt.xlabel('Sample Index or (Cluster Size)')
    plt.ylabel('Distance')
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"Saved dendrogram to {output_path}")
