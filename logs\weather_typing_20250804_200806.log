2025-08-04 20:08:06,269 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-08-04 20:08:06,270 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-04 20:08:06,270 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_14_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-04 20:08:06,270 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_14_hierarchical_singlevariable
2025-08-04 20:08:06,270 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-08-04 20:08:06,270 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-08-04 20:08:06,270 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-08-04 20:08:06,273 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-08-04 20:08:06,274 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_14_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-04 20:08:06,274 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-08-04 20:08:06,865 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-08-04 20:08:07,014 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-08-04 20:08:07,049 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-08-04 20:08:07,052 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-08-04 20:08:07,054 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-08-04 20:08:07,055 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-08-04 20:08:07,057 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-08-04 20:08:07,059 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-08-04 20:08:07,080 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-08-04 20:08:07,129 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-08-04 20:08:07,132 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-08-04 20:08:07,134 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-08-04 20:08:07,136 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-08-04 20:08:07,137 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-08-04 20:08:07,139 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-08-04 20:08:07,141 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-08-04 20:08:07,142 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-08-04 20:08:07,143 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-08-04 20:08:07,145 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-08-04 20:08:07,148 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-08-04 20:08:07,149 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-08-04 20:08:07,151 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-08-04 20:08:07,151 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-08-04 20:08:07,152 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-08-04 20:08:07,154 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-08-04 20:08:07,156 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-08-04 20:08:07,157 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-08-04 20:08:07,159 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-08-04 20:08:07,161 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-08-04 20:08:07,163 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-08-04 20:08:07,165 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-08-04 20:08:07,168 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-08-04 20:08:07,170 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-08-04 20:08:07,172 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-08-04 20:08:07,173 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-08-04 20:08:07,175 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-08-04 20:08:07,177 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-08-04 20:08:07,180 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-08-04 20:08:07,182 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-08-04 20:08:07,193 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-08-04 20:08:07,193 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-08-04 20:08:07,193 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-08-04 20:08:07,194 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-08-04 20:08:07,195 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-08-04 20:08:07,199 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-08-04 20:08:07,199 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-08-04 20:08:07,201 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-08-04 20:08:07,201 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-08-04 20:08:07,203 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-08-04 20:08:07,203 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-08-04 20:08:07,205 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-08-04 20:08:07,205 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-08-04 20:08:07,207 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-08-04 20:08:07,207 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-08-04 20:08:07,210 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-08-04 20:08:07,210 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-08-04 20:08:07,213 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-08-04 20:08:07,213 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-08-04 20:08:07,216 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-08-04 20:08:07,216 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-08-04 20:08:07,219 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-08-04 20:08:07,219 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-08-04 20:08:07,221 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-08-04 20:08:07,222 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-08-04 20:08:07,225 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-08-04 20:08:07,225 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-08-04 20:08:07,228 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-08-04 20:08:07,228 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-08-04 20:08:07,231 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-08-04 20:08:07,231 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-08-04 20:08:07,234 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-08-04 20:08:07,234 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-08-04 20:08:07,237 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-08-04 20:08:07,237 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-08-04 20:08:07,240 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-08-04 20:08:07,240 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-08-04 20:08:07,243 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-08-04 20:08:07,243 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-08-04 20:08:07,245 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-08-04 20:08:07,246 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-08-04 20:08:07,248 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-08-04 20:08:07,249 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-08-04 20:08:07,252 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-08-04 20:08:07,252 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-08-04 20:08:07,255 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-08-04 20:08:07,255 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-08-04 20:08:07,258 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-08-04 20:08:07,258 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-08-04 20:08:07,260 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-08-04 20:08:07,261 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-08-04 20:08:07,263 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-08-04 20:08:07,264 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-08-04 20:08:07,267 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-08-04 20:08:07,267 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-08-04 20:08:07,269 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-08-04 20:08:07,269 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-08-04 20:08:07,272 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-08-04 20:08:07,272 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-08-04 20:08:07,275 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-08-04 20:08:07,275 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-08-04 20:08:07,278 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-08-04 20:08:07,279 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-08-04 20:08:07,281 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-08-04 20:08:07,282 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-08-04 20:08:07,284 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-08-04 20:08:07,284 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-08-04 20:08:07,287 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-08-04 20:08:07,287 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-08-04 20:08:07,290 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-08-04 20:08:07,290 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-08-04 20:08:07,293 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-08-04 20:08:07,293 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-08-04 20:08:07,296 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-08-04 20:08:07,296 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-08-04 20:08:07,299 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-08-04 20:08:07,299 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-08-04 20:08:07,302 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-08-04 20:08:07,302 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-08-04 20:08:07,305 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-08-04 20:08:07,305 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-08-04 20:08:07,308 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-08-04 20:08:07,308 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-08-04 20:08:07,311 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-08-04 20:08:07,311 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-08-04 20:08:07,314 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-08-04 20:08:07,314 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-08-04 20:08:07,316 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-08-04 20:08:07,316 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-08-04 20:08:07,319 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-08-04 20:08:07,319 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-08-04 20:08:07,322 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-08-04 20:08:07,322 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-08-04 20:08:07,325 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-08-04 20:08:07,325 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-08-04 20:08:07,328 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-08-04 20:08:07,328 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-08-04 20:08:07,331 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-08-04 20:08:07,331 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-08-04 20:08:07,334 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-08-04 20:08:07,334 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-08-04 20:08:07,337 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-08-04 20:08:07,337 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-08-04 20:08:07,340 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-08-04 20:08:07,340 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-08-04 20:08:07,343 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-08-04 20:08:07,343 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-08-04 20:08:07,345 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-08-04 20:08:07,345 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-08-04 20:08:07,348 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-08-04 20:08:07,348 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-08-04 20:08:07,351 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-08-04 20:08:07,351 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-08-04 20:08:07,354 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-08-04 20:08:07,354 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-08-04 20:08:07,357 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-08-04 20:08:07,357 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-08-04 20:08:07,360 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-08-04 20:08:07,360 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-08-04 20:08:07,363 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-08-04 20:08:07,363 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-08-04 20:08:07,366 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-08-04 20:08:07,366 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-08-04 20:08:07,368 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-08-04 20:08:07,368 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-08-04 20:08:07,371 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-08-04 20:08:07,371 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-08-04 20:08:07,374 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-08-04 20:08:07,374 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-08-04 20:08:07,377 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-08-04 20:08:07,377 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-08-04 20:08:07,380 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-08-04 20:08:07,380 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-08-04 20:08:07,383 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-08-04 20:08:07,383 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-08-04 20:08:07,386 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-08-04 20:08:07,386 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-08-04 20:08:07,389 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-08-04 20:08:07,389 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-08-04 20:08:07,391 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-08-04 20:08:07,391 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-08-04 20:08:07,394 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-08-04 20:08:07,394 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-08-04 20:08:07,397 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-08-04 20:08:07,397 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-08-04 20:08:07,399 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-08-04 20:08:07,400 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-08-04 20:08:07,402 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-08-04 20:08:07,403 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-08-04 20:08:07,405 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-08-04 20:08:07,406 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-08-04 20:08:07,408 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-08-04 20:08:07,409 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-08-04 20:08:07,411 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-08-04 20:08:07,412 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-08-04 20:08:07,414 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-08-04 20:08:07,414 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-08-04 20:08:07,417 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-08-04 20:08:07,417 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-08-04 20:08:07,420 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-08-04 20:08:07,420 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-08-04 20:08:07,422 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-08-04 20:08:07,422 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-08-04 20:08:07,432 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-08-04 20:08:07,432 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-08-04 20:08:07,629 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-08-04 20:08:07,629 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-08-04 20:08:07,632 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-08-04 20:08:07,632 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-08-04 20:08:07,635 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-08-04 20:08:07,635 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-08-04 20:08:07,637 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-08-04 20:08:07,637 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-08-04 20:08:07,640 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-08-04 20:08:07,640 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-08-04 20:08:07,643 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-08-04 20:08:07,643 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-08-04 20:08:07,646 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-08-04 20:08:07,646 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-08-04 20:08:07,649 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-08-04 20:08:07,649 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-08-04 20:08:07,651 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-08-04 20:08:07,652 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-08-04 20:08:07,654 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-08-04 20:08:07,655 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-08-04 20:08:07,657 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-08-04 20:08:07,657 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-08-04 20:08:07,660 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-08-04 20:08:07,660 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-08-04 20:08:07,663 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-08-04 20:08:07,663 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-08-04 20:08:07,666 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-08-04 20:08:07,666 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-08-04 20:08:07,668 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-08-04 20:08:07,668 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-08-04 20:08:07,671 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-08-04 20:08:07,671 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-08-04 20:08:07,674 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-08-04 20:08:07,674 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-08-04 20:08:07,677 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-08-04 20:08:07,677 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-08-04 20:08:07,680 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-08-04 20:08:07,680 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-08-04 20:08:07,682 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-08-04 20:08:07,682 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-08-04 20:08:07,685 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-08-04 20:08:07,685 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-08-04 20:08:07,688 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-08-04 20:08:07,688 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-08-04 20:08:07,690 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-08-04 20:08:07,690 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-08-04 20:08:07,693 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-08-04 20:08:07,693 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-08-04 20:08:07,696 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-08-04 20:08:07,696 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-08-04 20:08:07,699 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-08-04 20:08:07,699 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-08-04 20:08:07,702 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-08-04 20:08:07,702 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-08-04 20:08:07,704 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-08-04 20:08:07,704 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-08-04 20:08:07,707 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-08-04 20:08:07,707 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-08-04 20:08:07,710 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-08-04 20:08:07,710 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-08-04 20:08:07,712 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-08-04 20:08:07,713 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-08-04 20:08:07,715 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-08-04 20:08:07,715 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-08-04 20:08:07,718 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-08-04 20:08:07,718 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-08-04 20:08:07,721 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-08-04 20:08:07,721 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-08-04 20:08:07,724 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-08-04 20:08:07,724 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-08-04 20:08:07,726 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-08-04 20:08:07,726 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-08-04 20:08:07,729 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-08-04 20:08:07,729 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-08-04 20:08:07,732 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-08-04 20:08:07,732 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-08-04 20:08:07,734 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-08-04 20:08:07,735 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-08-04 20:08:07,737 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-08-04 20:08:07,738 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-08-04 20:08:07,740 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-08-04 20:08:07,740 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-08-04 20:08:07,743 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-08-04 20:08:07,743 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-08-04 20:08:07,746 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-08-04 20:08:07,746 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-08-04 20:08:07,748 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-08-04 20:08:07,749 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-08-04 20:08:07,751 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-08-04 20:08:07,751 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_14_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-04 20:08:08,547 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-08-04 20:08:08,548 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-08-04 20:08:08,548 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-08-04 20:08:08,548 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_14_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-04 20:08:08,548 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_14_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-04 20:08:08,548 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 125 variables
2025-08-04 20:08:08,548 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125
2025-08-04 20:08:08,548 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-08-04 20:08:08,551 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_14_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-04 20:08:08,551 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-08-04 20:08:08,551 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-04 20:08:08,558 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-08-04 20:08:13,452 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_14_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-04 20:08:13,485 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-08-04 20:08:13,485 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-08-04 20:08:13,486 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-08-04 20:08:13,487 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-08-04 20:08:13,542 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16423 sites
2025-08-04 20:08:42,874 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=89.471, H2=0.744, H3=-0.478
2025-08-04 20:08:42,967 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 35936 sites
2025-08-04 20:09:46,951 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=99.321, H2=23.472, H3=8.064
2025-08-04 20:09:46,978 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-08-04 20:09:46,978 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-08-04 20:09:46,980 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-08-04 20:09:47,035 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16423 sites
2025-08-04 20:10:16,400 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=89.471, H2=0.744, H3=-0.478
2025-08-04 20:10:16,463 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 20386 sites
2025-08-04 20:10:52,779 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=40.124, H2=12.429, H3=4.756
2025-08-04 20:10:52,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 15550 sites
2025-08-04 20:11:20,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=91.795, H2=28.577, H3=7.918
2025-08-04 20:11:20,433 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-08-04 20:11:20,433 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-08-04 20:11:20,435 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-08-04 20:11:20,489 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16423 sites
2025-08-04 20:11:49,673 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=89.471, H2=0.744, H3=-0.478
2025-08-04 20:11:49,737 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 20386 sites
2025-08-04 20:12:25,833 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=40.124, H2=12.429, H3=4.756
2025-08-04 20:12:25,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2241 sites
2025-08-04 20:12:29,842 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.182, H2=0.423, H3=-2.504
2025-08-04 20:12:29,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 13309 sites
2025-08-04 20:12:53,390 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=43.801, H2=16.347, H3=4.611
2025-08-04 20:12:53,416 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-08-04 20:12:53,416 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-08-04 20:12:53,417 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-08-04 20:12:53,472 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16423 sites
2025-08-04 20:13:22,706 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=89.471, H2=0.744, H3=-0.478
2025-08-04 20:13:22,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6217 sites
2025-08-04 20:13:33,878 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=14.442, H2=5.545, H3=3.246
2025-08-04 20:13:33,929 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 14169 sites
2025-08-04 20:13:59,254 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.544, H2=17.344, H3=6.113
2025-08-04 20:13:59,284 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2241 sites
2025-08-04 20:14:03,322 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.181, H2=0.678, H3=-2.807
2025-08-04 20:14:03,372 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 13309 sites
2025-08-04 20:14:27,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=60.933, H2=15.100, H3=3.717
2025-08-04 20:14:27,233 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-08-04 20:14:27,233 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-08-04 20:14:27,235 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-08-04 20:14:27,289 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16423 sites
2025-08-04 20:14:56,942 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=89.471, H2=0.744, H3=-0.478
2025-08-04 20:14:56,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6217 sites
2025-08-04 20:15:08,234 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=14.442, H2=5.545, H3=3.246
2025-08-04 20:15:08,285 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 14169 sites
2025-08-04 20:15:33,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.544, H2=17.344, H3=6.113
2025-08-04 20:15:33,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2241 sites
2025-08-04 20:15:37,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.181, H2=0.678, H3=-2.807
2025-08-04 20:15:37,770 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8474 sites
2025-08-04 20:15:52,876 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=48.277, H2=12.712, H3=3.776
2025-08-04 20:15:52,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4835 sites
2025-08-04 20:16:01,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=21.344, H2=3.438, H3=0.257
2025-08-04 20:16:01,690 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-08-04 20:16:01,690 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-08-04 20:16:01,692 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-08-04 20:16:01,746 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16423 sites
2025-08-04 20:16:31,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=89.471, H2=0.744, H3=-0.478
2025-08-04 20:16:31,310 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6217 sites
2025-08-04 20:16:42,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=14.442, H2=5.545, H3=3.246
2025-08-04 20:16:42,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6996 sites
2025-08-04 20:16:55,164 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.118, H2=16.535, H3=7.987
2025-08-04 20:16:55,202 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7173 sites
2025-08-04 20:17:08,006 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=14.226, H2=1.452, H3=-1.182
2025-08-04 20:17:08,036 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2241 sites
2025-08-04 20:17:12,025 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=29.324, H2=-0.139, H3=-3.099
2025-08-04 20:17:12,066 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8474 sites
2025-08-04 20:17:27,304 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=35.199, H2=12.564, H3=3.843
2025-08-04 20:17:27,339 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4835 sites
2025-08-04 20:17:35,951 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=16.535, H2=3.403, H3=0.436
2025-08-04 20:17:35,976 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-08-04 20:17:35,977 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-08-04 20:17:35,978 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-08-04 20:17:36,033 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16423 sites
2025-08-04 20:18:05,349 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=89.471, H2=0.744, H3=-0.478
2025-08-04 20:18:05,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6217 sites
2025-08-04 20:18:16,459 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=14.442, H2=5.545, H3=3.246
2025-08-04 20:18:16,497 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6996 sites
2025-08-04 20:18:29,012 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.118, H2=16.535, H3=7.987
2025-08-04 20:18:29,051 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7173 sites
2025-08-04 20:18:41,868 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=14.226, H2=1.452, H3=-1.182
2025-08-04 20:18:41,895 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 480 sites
2025-08-04 20:18:42,753 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=2.311, H2=-9.452, H3=3.169
2025-08-04 20:18:42,782 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1761 sites
2025-08-04 20:18:45,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=28.645, H2=3.232, H3=-4.343
2025-08-04 20:18:45,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8474 sites
2025-08-04 20:19:01,140 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=27.637, H2=13.692, H3=4.426
2025-08-04 20:19:01,175 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4835 sites
2025-08-04 20:19:09,814 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=20.320, H2=4.023, H3=0.186
2025-08-04 20:19:09,840 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-08-04 20:19:09,840 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-08-04 20:19:09,842 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-08-04 20:19:09,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 8178 sites
2025-08-04 20:19:24,469 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=72.802, H2=1.629, H3=-1.775
2025-08-04 20:19:24,509 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8245 sites
2025-08-04 20:19:39,338 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=58.365, H2=-1.565, H3=0.495
2025-08-04 20:19:39,375 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6217 sites
2025-08-04 20:19:50,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.930, H2=6.196, H3=4.347
2025-08-04 20:19:50,474 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6996 sites
2025-08-04 20:20:02,980 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=43.926, H2=14.750, H3=6.141
2025-08-04 20:20:03,018 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7173 sites
2025-08-04 20:20:15,737 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.204, H2=1.005, H3=-0.813
2025-08-04 20:20:15,764 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 480 sites
2025-08-04 20:20:16,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.621, H2=-7.800, H3=2.395
2025-08-04 20:20:16,649 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1761 sites
2025-08-04 20:20:19,810 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.435, H2=3.835, H3=-4.675
2025-08-04 20:20:19,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8474 sites
2025-08-04 20:20:34,944 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=35.476, H2=12.311, H3=3.844
2025-08-04 20:20:34,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4835 sites
2025-08-04 20:20:43,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.200, H2=3.073, H3=0.108
2025-08-04 20:20:43,630 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-08-04 20:20:43,630 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-08-04 20:20:43,632 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-08-04 20:20:43,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 8178 sites
2025-08-04 20:20:58,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=72.802, H2=1.629, H3=-1.775
2025-08-04 20:20:58,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8245 sites
2025-08-04 20:21:12,928 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=58.365, H2=-1.565, H3=0.495
2025-08-04 20:21:12,965 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6217 sites
2025-08-04 20:21:24,091 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.930, H2=6.196, H3=4.347
2025-08-04 20:21:24,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6996 sites
2025-08-04 20:21:36,552 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=43.926, H2=14.750, H3=6.141
2025-08-04 20:21:36,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7173 sites
2025-08-04 20:21:49,499 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.204, H2=1.005, H3=-0.813
2025-08-04 20:21:49,526 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 480 sites
2025-08-04 20:21:50,382 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.621, H2=-7.800, H3=2.395
2025-08-04 20:21:50,411 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1761 sites
2025-08-04 20:21:53,538 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.435, H2=3.835, H3=-4.675
2025-08-04 20:21:53,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8474 sites
2025-08-04 20:22:08,751 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=35.476, H2=12.311, H3=3.844
2025-08-04 20:22:08,781 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2192 sites
2025-08-04 20:22:12,688 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=2.116, H2=1.220, H3=-0.550
2025-08-04 20:22:12,718 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2643 sites
2025-08-04 20:22:17,465 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.722, H2=2.552, H3=0.155
2025-08-04 20:22:17,491 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-08-04 20:22:17,491 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-08-04 20:22:17,493 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-08-04 20:22:17,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 8178 sites
2025-08-04 20:22:32,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=72.802, H2=1.629, H3=-1.775
2025-08-04 20:22:32,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4082 sites
2025-08-04 20:22:39,406 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=22.299, H2=2.458, H3=1.471
2025-08-04 20:22:39,440 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4163 sites
2025-08-04 20:22:46,877 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=54.517, H2=-5.489, H3=-1.783
2025-08-04 20:22:46,913 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6217 sites
2025-08-04 20:22:57,955 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=19.400, H2=7.070, H3=3.658
2025-08-04 20:22:57,994 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6996 sites
2025-08-04 20:23:10,470 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=54.402, H2=15.498, H3=4.551
2025-08-04 20:23:10,509 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 7173 sites
2025-08-04 20:23:23,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.811, H2=1.259, H3=-0.445
2025-08-04 20:23:23,333 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 480 sites
2025-08-04 20:23:24,195 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=3.208, H2=-5.502, H3=3.065
2025-08-04 20:23:24,224 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1761 sites
2025-08-04 20:23:27,380 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.974, H2=3.488, H3=-4.970
2025-08-04 20:23:27,420 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8474 sites
2025-08-04 20:23:42,470 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=32.337, H2=16.667, H3=4.669
2025-08-04 20:23:42,500 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2192 sites
2025-08-04 20:23:46,416 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=1.931, H2=1.030, H3=-0.479
2025-08-04 20:23:46,446 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2643 sites
2025-08-04 20:23:51,148 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.843, H2=2.623, H3=0.534
2025-08-04 20:23:51,173 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-08-04 20:23:51,174 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-08-04 20:23:51,175 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-08-04 20:23:51,215 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 8178 sites
2025-08-04 20:24:05,833 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=72.802, H2=1.629, H3=-1.775
2025-08-04 20:24:05,867 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4082 sites
2025-08-04 20:24:13,140 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=22.299, H2=2.458, H3=1.471
2025-08-04 20:24:13,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4163 sites
2025-08-04 20:24:20,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=54.517, H2=-5.489, H3=-1.783
2025-08-04 20:24:20,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6217 sites
2025-08-04 20:24:31,772 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=19.400, H2=7.070, H3=3.658
2025-08-04 20:24:31,811 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6996 sites
2025-08-04 20:24:44,235 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=54.402, H2=15.498, H3=4.551
2025-08-04 20:24:44,266 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2305 sites
2025-08-04 20:24:48,378 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.948, H2=-1.732, H3=-10.323
2025-08-04 20:24:48,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4868 sites
2025-08-04 20:24:57,156 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=4.222, H2=2.859, H3=3.468
2025-08-04 20:24:57,183 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 480 sites
2025-08-04 20:24:58,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=2.108, H2=-9.248, H3=2.270
2025-08-04 20:24:58,079 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1761 sites
2025-08-04 20:25:01,265 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=32.544, H2=4.156, H3=-4.600
2025-08-04 20:25:01,306 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 8474 sites
2025-08-04 20:25:16,671 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=40.550, H2=18.248, H3=3.731
2025-08-04 20:25:16,700 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2192 sites
2025-08-04 20:25:20,627 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.637, H2=1.073, H3=0.073
2025-08-04 20:25:20,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2643 sites
2025-08-04 20:25:25,405 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.557, H2=2.933, H3=0.636
2025-08-04 20:25:25,430 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-08-04 20:25:25,430 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-08-04 20:25:25,432 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-08-04 20:25:25,472 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 8178 sites
2025-08-04 20:25:40,206 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=72.802, H2=1.629, H3=-1.775
2025-08-04 20:25:40,239 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4082 sites
2025-08-04 20:25:47,631 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=22.299, H2=2.458, H3=1.471
2025-08-04 20:25:47,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4163 sites
2025-08-04 20:25:55,156 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=54.517, H2=-5.489, H3=-1.783
2025-08-04 20:25:55,193 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6217 sites
2025-08-04 20:26:06,359 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=19.400, H2=7.070, H3=3.658
2025-08-04 20:26:06,397 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6996 sites
2025-08-04 20:26:18,900 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=54.402, H2=15.498, H3=4.551
2025-08-04 20:26:18,930 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2305 sites
2025-08-04 20:26:23,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.948, H2=-1.732, H3=-10.323
2025-08-04 20:26:23,106 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4868 sites
2025-08-04 20:26:31,792 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=4.222, H2=2.859, H3=3.468
2025-08-04 20:26:31,819 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 480 sites
2025-08-04 20:26:32,678 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=2.108, H2=-9.248, H3=2.270
2025-08-04 20:26:32,706 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1761 sites
2025-08-04 20:26:35,867 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=32.544, H2=4.156, H3=-4.600
2025-08-04 20:26:35,894 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 698 sites
2025-08-04 20:26:37,137 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-5.963, H2=-3.859, H3=-5.351
2025-08-04 20:26:37,177 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 7776 sites
2025-08-04 20:26:51,055 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=30.908, H2=19.187, H3=6.523
2025-08-04 20:26:51,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2192 sites
2025-08-04 20:26:54,988 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.821, H2=1.553, H3=-0.307
2025-08-04 20:26:55,019 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2643 sites
2025-08-04 20:26:59,721 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.378, H2=2.661, H3=-0.018
2025-08-04 20:26:59,746 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-08-04 20:26:59,746 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-08-04 20:26:59,748 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-08-04 20:26:59,777 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:27:04,002 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:27:04,039 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:27:14,341 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:27:14,374 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:27:21,637 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:27:21,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 4163 sites
2025-08-04 20:27:29,067 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=55.702, H2=-4.964, H3=-1.876
2025-08-04 20:27:29,104 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6217 sites
2025-08-04 20:27:40,165 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.692, H2=5.612, H3=4.292
2025-08-04 20:27:40,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6996 sites
2025-08-04 20:27:52,655 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=45.559, H2=12.108, H3=4.914
2025-08-04 20:27:52,684 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2305 sites
2025-08-04 20:27:56,784 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=11.721, H2=-2.014, H3=-10.495
2025-08-04 20:27:56,818 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4868 sites
2025-08-04 20:28:05,496 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.355, H2=2.923, H3=3.313
2025-08-04 20:28:05,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 480 sites
2025-08-04 20:28:06,377 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=2.761, H2=-7.841, H3=2.443
2025-08-04 20:28:06,406 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1761 sites
2025-08-04 20:28:09,524 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=25.963, H2=3.968, H3=-4.192
2025-08-04 20:28:09,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 698 sites
2025-08-04 20:28:10,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-5.369, H2=-2.964, H3=-4.472
2025-08-04 20:28:10,832 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 7776 sites
2025-08-04 20:28:24,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=47.654, H2=14.539, H3=6.611
2025-08-04 20:28:24,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2192 sites
2025-08-04 20:28:28,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.799, H2=0.740, H3=-0.746
2025-08-04 20:28:28,559 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2643 sites
2025-08-04 20:28:33,249 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=8.889, H2=2.689, H3=0.635
2025-08-04 20:28:33,275 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-08-04 20:28:33,276 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-08-04 20:28:33,277 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-08-04 20:28:33,308 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:28:37,545 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:28:37,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:28:47,981 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:28:48,014 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:28:55,329 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:28:55,355 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 624 sites
2025-08-04 20:28:56,479 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.475, H2=-2.355, H3=-1.696
2025-08-04 20:28:56,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3539 sites
2025-08-04 20:29:02,882 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=58.311, H2=-5.805, H3=-1.494
2025-08-04 20:29:02,918 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6217 sites
2025-08-04 20:29:14,017 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.554, H2=4.551, H3=3.676
2025-08-04 20:29:14,055 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6996 sites
2025-08-04 20:29:26,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.499, H2=12.733, H3=4.678
2025-08-04 20:29:26,614 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2305 sites
2025-08-04 20:29:30,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.641, H2=-1.502, H3=-11.050
2025-08-04 20:29:30,739 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4868 sites
2025-08-04 20:29:39,464 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=5.873, H2=2.706, H3=3.138
2025-08-04 20:29:39,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 480 sites
2025-08-04 20:29:40,346 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.391, H2=-8.009, H3=1.817
2025-08-04 20:29:40,374 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1761 sites
2025-08-04 20:29:43,517 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=30.623, H2=4.074, H3=-4.659
2025-08-04 20:29:43,544 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 698 sites
2025-08-04 20:29:44,790 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-7.424, H2=-5.109, H3=-6.404
2025-08-04 20:29:44,830 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 7776 sites
2025-08-04 20:29:58,798 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=29.320, H2=13.555, H3=5.671
2025-08-04 20:29:58,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2192 sites
2025-08-04 20:30:02,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.596, H2=1.039, H3=-0.160
2025-08-04 20:30:02,766 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2643 sites
2025-08-04 20:30:07,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=13.520, H2=2.847, H3=0.309
2025-08-04 20:30:07,506 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-08-04 20:30:07,507 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-08-04 20:30:07,508 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-08-04 20:30:07,538 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:30:11,828 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:30:11,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:30:22,184 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:30:22,217 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:30:29,531 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:30:29,558 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 624 sites
2025-08-04 20:30:30,675 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.475, H2=-2.355, H3=-1.696
2025-08-04 20:30:30,707 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3539 sites
2025-08-04 20:30:36,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=58.311, H2=-5.805, H3=-1.494
2025-08-04 20:30:37,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6217 sites
2025-08-04 20:30:48,153 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.554, H2=4.551, H3=3.676
2025-08-04 20:30:48,191 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6996 sites
2025-08-04 20:31:00,668 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.499, H2=12.733, H3=4.678
2025-08-04 20:31:00,698 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2305 sites
2025-08-04 20:31:04,817 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.641, H2=-1.502, H3=-11.050
2025-08-04 20:31:04,851 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4868 sites
2025-08-04 20:31:13,543 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=5.873, H2=2.706, H3=3.138
2025-08-04 20:31:13,569 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 480 sites
2025-08-04 20:31:14,426 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.391, H2=-8.009, H3=1.817
2025-08-04 20:31:14,452 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 591 sites
2025-08-04 20:31:15,519 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.011, H2=1.174, H3=0.202
2025-08-04 20:31:15,546 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1170 sites
2025-08-04 20:31:17,655 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=42.546, H2=-1.471, H3=-6.783
2025-08-04 20:31:17,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 698 sites
2025-08-04 20:31:18,950 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-4.930, H2=-3.722, H3=-4.791
2025-08-04 20:31:18,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 7776 sites
2025-08-04 20:31:33,105 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=37.129, H2=13.720, H3=6.701
2025-08-04 20:31:33,134 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2192 sites
2025-08-04 20:31:37,082 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.663, H2=0.907, H3=-0.447
2025-08-04 20:31:37,113 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2643 sites
2025-08-04 20:31:41,864 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=9.205, H2=3.176, H3=0.070
2025-08-04 20:31:41,889 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-08-04 20:31:41,889 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-08-04 20:31:41,890 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-08-04 20:31:41,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:31:46,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:31:46,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:31:56,662 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:31:56,695 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:32:04,079 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:32:04,106 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 624 sites
2025-08-04 20:32:05,222 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.475, H2=-2.355, H3=-1.696
2025-08-04 20:32:05,254 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3539 sites
2025-08-04 20:32:11,595 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=58.311, H2=-5.805, H3=-1.494
2025-08-04 20:32:11,632 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6217 sites
2025-08-04 20:32:22,771 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.554, H2=4.551, H3=3.676
2025-08-04 20:32:22,803 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3580 sites
2025-08-04 20:32:29,190 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=32.860, H2=15.142, H3=1.724
2025-08-04 20:32:29,222 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3416 sites
2025-08-04 20:32:35,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=7.886, H2=-2.879, H3=4.305
2025-08-04 20:32:35,365 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2305 sites
2025-08-04 20:32:39,474 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.000, H2=-2.438, H3=-7.464
2025-08-04 20:32:39,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4868 sites
2025-08-04 20:32:48,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.742, H2=2.774, H3=2.365
2025-08-04 20:32:48,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 480 sites
2025-08-04 20:32:49,095 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.017, H2=-6.719, H3=2.659
2025-08-04 20:32:49,121 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 591 sites
2025-08-04 20:32:50,173 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.976, H2=1.471, H3=-0.388
2025-08-04 20:32:50,201 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1170 sites
2025-08-04 20:32:52,295 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=28.932, H2=-1.775, H3=-6.216
2025-08-04 20:32:52,322 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 698 sites
2025-08-04 20:32:53,564 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-5.348, H2=-3.803, H3=-6.531
2025-08-04 20:32:53,605 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 7776 sites
2025-08-04 20:33:07,444 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=44.782, H2=14.851, H3=6.257
2025-08-04 20:33:07,473 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2192 sites
2025-08-04 20:33:11,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=1.275, H2=1.353, H3=-0.725
2025-08-04 20:33:11,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2643 sites
2025-08-04 20:33:16,108 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=7.513, H2=2.520, H3=0.347
2025-08-04 20:33:16,133 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-08-04 20:33:16,133 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-08-04 20:33:16,135 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-08-04 20:33:16,165 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:33:20,389 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:33:20,426 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:33:30,660 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:33:30,693 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:33:37,889 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:33:37,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 624 sites
2025-08-04 20:33:39,022 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.475, H2=-2.355, H3=-1.696
2025-08-04 20:33:39,054 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3539 sites
2025-08-04 20:33:45,394 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=58.311, H2=-5.805, H3=-1.494
2025-08-04 20:33:45,431 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6217 sites
2025-08-04 20:33:56,446 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.554, H2=4.551, H3=3.676
2025-08-04 20:33:56,478 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3580 sites
2025-08-04 20:34:02,846 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=32.860, H2=15.142, H3=1.724
2025-08-04 20:34:02,878 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3416 sites
2025-08-04 20:34:08,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=7.886, H2=-2.879, H3=4.305
2025-08-04 20:34:08,988 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2305 sites
2025-08-04 20:34:13,129 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.000, H2=-2.438, H3=-7.464
2025-08-04 20:34:13,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4868 sites
2025-08-04 20:34:21,874 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.742, H2=2.774, H3=2.365
2025-08-04 20:34:21,901 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 480 sites
2025-08-04 20:34:22,759 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.017, H2=-6.719, H3=2.659
2025-08-04 20:34:22,785 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 591 sites
2025-08-04 20:34:23,828 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.976, H2=1.471, H3=-0.388
2025-08-04 20:34:23,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1170 sites
2025-08-04 20:34:25,934 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=28.932, H2=-1.775, H3=-6.216
2025-08-04 20:34:25,961 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 698 sites
2025-08-04 20:34:27,200 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-5.348, H2=-3.803, H3=-6.531
2025-08-04 20:34:27,229 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1471 sites
2025-08-04 20:34:29,859 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=15.251, H2=1.969, H3=-2.315
2025-08-04 20:34:29,896 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 6305 sites
2025-08-04 20:34:41,065 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=21.094, H2=20.245, H3=6.535
2025-08-04 20:34:41,094 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2192 sites
2025-08-04 20:34:44,990 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=1.638, H2=1.102, H3=-0.462
2025-08-04 20:34:45,021 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2643 sites
2025-08-04 20:34:49,709 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.311, H2=2.807, H3=0.811
2025-08-04 20:34:49,734 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-08-04 20:34:49,734 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-08-04 20:34:49,736 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-08-04 20:34:49,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:34:53,977 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:34:54,013 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:35:04,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:35:04,420 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:35:11,717 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:35:11,744 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 624 sites
2025-08-04 20:35:12,861 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.475, H2=-2.355, H3=-1.696
2025-08-04 20:35:12,893 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3539 sites
2025-08-04 20:35:19,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=58.311, H2=-5.805, H3=-1.494
2025-08-04 20:35:19,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6217 sites
2025-08-04 20:35:30,269 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.554, H2=4.551, H3=3.676
2025-08-04 20:35:30,301 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3580 sites
2025-08-04 20:35:36,703 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=32.860, H2=15.142, H3=1.724
2025-08-04 20:35:36,734 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3416 sites
2025-08-04 20:35:42,814 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=7.886, H2=-2.879, H3=4.305
2025-08-04 20:35:42,844 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2305 sites
2025-08-04 20:35:46,956 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.000, H2=-2.438, H3=-7.464
2025-08-04 20:35:46,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4868 sites
2025-08-04 20:35:55,625 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.742, H2=2.774, H3=2.365
2025-08-04 20:35:55,651 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 480 sites
2025-08-04 20:35:56,509 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.017, H2=-6.719, H3=2.659
2025-08-04 20:35:56,535 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 591 sites
2025-08-04 20:35:57,593 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.976, H2=1.471, H3=-0.388
2025-08-04 20:35:57,621 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1170 sites
2025-08-04 20:35:59,713 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=28.932, H2=-1.775, H3=-6.216
2025-08-04 20:35:59,740 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 698 sites
2025-08-04 20:36:00,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-5.348, H2=-3.803, H3=-6.531
2025-08-04 20:36:01,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1471 sites
2025-08-04 20:36:03,645 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=15.251, H2=1.969, H3=-2.315
2025-08-04 20:36:03,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 6305 sites
2025-08-04 20:36:14,981 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=21.094, H2=20.245, H3=6.535
2025-08-04 20:36:15,011 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2192 sites
2025-08-04 20:36:18,890 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=1.638, H2=1.102, H3=-0.462
2025-08-04 20:36:18,917 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 978 sites
2025-08-04 20:36:20,669 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-1.714, H2=1.101, H3=-2.041
2025-08-04 20:36:20,697 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1665 sites
2025-08-04 20:36:23,692 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.733, H2=-0.594, H3=1.324
2025-08-04 20:36:23,717 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-08-04 20:36:23,718 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-08-04 20:36:23,719 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-08-04 20:36:23,749 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:36:28,053 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:36:28,089 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:36:38,464 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:36:38,497 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:36:45,864 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:36:45,891 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 624 sites
2025-08-04 20:36:47,008 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.475, H2=-2.355, H3=-1.696
2025-08-04 20:36:47,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3539 sites
2025-08-04 20:36:53,423 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=58.311, H2=-5.805, H3=-1.494
2025-08-04 20:36:53,453 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2353 sites
2025-08-04 20:36:57,692 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=3.164, H2=0.913, H3=2.615
2025-08-04 20:36:57,724 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3864 sites
2025-08-04 20:37:04,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=9.087, H2=5.865, H3=3.978
2025-08-04 20:37:04,668 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3580 sites
2025-08-04 20:37:11,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=37.784, H2=13.138, H3=1.382
2025-08-04 20:37:11,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3416 sites
2025-08-04 20:37:17,219 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.523, H2=-3.643, H3=4.409
2025-08-04 20:37:17,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2305 sites
2025-08-04 20:37:21,378 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.024, H2=-1.976, H3=-7.294
2025-08-04 20:37:21,412 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4868 sites
2025-08-04 20:37:30,004 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.088, H2=3.640, H3=3.044
2025-08-04 20:37:30,031 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 480 sites
2025-08-04 20:37:30,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.500, H2=-7.284, H3=2.345
2025-08-04 20:37:30,919 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 591 sites
2025-08-04 20:37:31,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.775, H2=1.399, H3=-0.039
2025-08-04 20:37:32,007 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1170 sites
2025-08-04 20:37:34,106 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=25.068, H2=-1.615, H3=-6.645
2025-08-04 20:37:34,133 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 698 sites
2025-08-04 20:37:35,388 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-5.722, H2=-3.057, H3=-5.644
2025-08-04 20:37:35,416 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1471 sites
2025-08-04 20:37:38,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=14.506, H2=2.365, H3=-1.821
2025-08-04 20:37:38,087 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 6305 sites
2025-08-04 20:37:49,367 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=27.046, H2=9.285, H3=7.096
2025-08-04 20:37:49,396 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2192 sites
2025-08-04 20:37:53,277 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=1.257, H2=1.229, H3=0.122
2025-08-04 20:37:53,305 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 978 sites
2025-08-04 20:37:55,054 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-2.768, H2=1.434, H3=-3.064
2025-08-04 20:37:55,083 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1665 sites
2025-08-04 20:37:58,065 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.919, H2=-0.458, H3=1.260
2025-08-04 20:37:58,090 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-08-04 20:37:58,090 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-08-04 20:37:58,092 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-08-04 20:37:58,122 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:38:02,394 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:38:02,430 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:38:12,720 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:38:12,753 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:38:20,083 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:38:20,109 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 624 sites
2025-08-04 20:38:21,221 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.475, H2=-2.355, H3=-1.696
2025-08-04 20:38:21,252 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3539 sites
2025-08-04 20:38:27,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=58.311, H2=-5.805, H3=-1.494
2025-08-04 20:38:27,593 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2353 sites
2025-08-04 20:38:31,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=3.164, H2=0.913, H3=2.615
2025-08-04 20:38:31,823 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3864 sites
2025-08-04 20:38:38,700 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=9.087, H2=5.865, H3=3.978
2025-08-04 20:38:38,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3580 sites
2025-08-04 20:38:45,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=37.784, H2=13.138, H3=1.382
2025-08-04 20:38:45,151 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3416 sites
2025-08-04 20:38:51,224 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.523, H2=-3.643, H3=4.409
2025-08-04 20:38:51,254 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2305 sites
2025-08-04 20:38:55,392 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.024, H2=-1.976, H3=-7.294
2025-08-04 20:38:55,426 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4868 sites
2025-08-04 20:39:04,127 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.088, H2=3.640, H3=3.044
2025-08-04 20:39:04,154 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 480 sites
2025-08-04 20:39:05,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.500, H2=-7.284, H3=2.345
2025-08-04 20:39:05,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 591 sites
2025-08-04 20:39:06,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.775, H2=1.399, H3=-0.039
2025-08-04 20:39:06,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1170 sites
2025-08-04 20:39:08,214 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=25.068, H2=-1.615, H3=-6.645
2025-08-04 20:39:08,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 698 sites
2025-08-04 20:39:09,484 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-5.722, H2=-3.057, H3=-5.644
2025-08-04 20:39:09,513 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1471 sites
2025-08-04 20:39:12,148 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=14.506, H2=2.365, H3=-1.821
2025-08-04 20:39:12,177 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2022 sites
2025-08-04 20:39:15,803 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=12.011, H2=2.153, H3=-1.154
2025-08-04 20:39:15,836 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 4283 sites
2025-08-04 20:39:23,394 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=10.636, H2=5.549, H3=4.921
2025-08-04 20:39:23,423 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2192 sites
2025-08-04 20:39:27,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=1.234, H2=1.020, H3=-0.753
2025-08-04 20:39:27,341 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 978 sites
2025-08-04 20:39:29,087 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-1.698, H2=1.445, H3=-2.910
2025-08-04 20:39:29,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1665 sites
2025-08-04 20:39:32,089 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=8.247, H2=-0.749, H3=1.483
2025-08-04 20:39:32,114 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-08-04 20:39:32,114 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-08-04 20:39:32,116 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-08-04 20:39:32,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:39:36,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:39:36,476 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5796 sites
2025-08-04 20:39:46,831 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=33.486, H2=1.113, H3=-1.550
2025-08-04 20:39:46,865 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4082 sites
2025-08-04 20:39:54,212 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=31.412, H2=2.641, H3=2.678
2025-08-04 20:39:54,239 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 624 sites
2025-08-04 20:39:55,357 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.475, H2=-2.355, H3=-1.696
2025-08-04 20:39:55,389 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3539 sites
2025-08-04 20:40:01,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=58.311, H2=-5.805, H3=-1.494
2025-08-04 20:40:01,764 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2353 sites
2025-08-04 20:40:05,989 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=3.164, H2=0.913, H3=2.615
2025-08-04 20:40:06,022 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3864 sites
2025-08-04 20:40:12,923 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=9.087, H2=5.865, H3=3.978
2025-08-04 20:40:12,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3580 sites
2025-08-04 20:40:19,348 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=37.784, H2=13.138, H3=1.382
2025-08-04 20:40:19,380 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3416 sites
2025-08-04 20:40:25,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.523, H2=-3.643, H3=4.409
2025-08-04 20:40:25,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2305 sites
2025-08-04 20:40:29,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.024, H2=-1.976, H3=-7.294
2025-08-04 20:40:29,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1816 sites
2025-08-04 20:40:32,916 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=0.075, H2=0.630, H3=0.357
2025-08-04 20:40:32,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3052 sites
2025-08-04 20:40:38,383 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.957, H2=1.294, H3=2.083
2025-08-04 20:40:38,409 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 480 sites
2025-08-04 20:40:39,260 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.729, H2=-9.934, H3=2.710
2025-08-04 20:40:39,286 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 591 sites
2025-08-04 20:40:40,330 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.149, H2=0.611, H3=-0.261
2025-08-04 20:40:40,358 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1170 sites
2025-08-04 20:40:42,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=43.597, H2=-1.489, H3=-6.709
2025-08-04 20:40:42,463 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 698 sites
2025-08-04 20:40:43,709 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-5.647, H2=-4.457, H3=-4.582
2025-08-04 20:40:43,737 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1471 sites
2025-08-04 20:40:46,367 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=15.291, H2=2.055, H3=-2.462
2025-08-04 20:40:46,396 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2022 sites
2025-08-04 20:40:50,025 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.969, H2=1.892, H3=-0.805
2025-08-04 20:40:50,059 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 4283 sites
2025-08-04 20:40:57,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=9.839, H2=5.446, H3=6.123
2025-08-04 20:40:57,785 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2192 sites
2025-08-04 20:41:01,746 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.591, H2=1.212, H3=-0.357
2025-08-04 20:41:01,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 978 sites
2025-08-04 20:41:03,529 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-1.800, H2=1.811, H3=-1.832
2025-08-04 20:41:03,558 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1665 sites
2025-08-04 20:41:06,549 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=8.644, H2=-0.297, H3=1.349
2025-08-04 20:41:06,574 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-08-04 20:41:06,574 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-08-04 20:41:06,576 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-08-04 20:41:06,605 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2382 sites
2025-08-04 20:41:10,901 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=27.172, H2=-2.454, H3=-1.605
2025-08-04 20:41:10,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3778 sites
2025-08-04 20:41:17,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=22.365, H2=-1.309, H3=-0.014
2025-08-04 20:41:17,726 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2018 sites
2025-08-04 20:41:21,324 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=5.622, H2=-0.589, H3=-4.165
2025-08-04 20:41:21,357 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 4082 sites
2025-08-04 20:41:28,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=29.086, H2=1.951, H3=1.544
2025-08-04 20:41:28,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 624 sites
2025-08-04 20:41:29,774 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=3.932, H2=-2.023, H3=-2.056
2025-08-04 20:41:29,806 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3539 sites
2025-08-04 20:41:36,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=43.045, H2=-4.344, H3=-1.089
2025-08-04 20:41:36,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2353 sites
2025-08-04 20:41:40,344 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=2.922, H2=0.605, H3=1.953
2025-08-04 20:41:40,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3864 sites
2025-08-04 20:41:47,252 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=11.380, H2=4.828, H3=3.027
2025-08-04 20:41:47,284 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3580 sites
2025-08-04 20:41:53,734 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=48.608, H2=21.955, H3=1.357
2025-08-04 20:41:53,766 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3416 sites
2025-08-04 20:41:59,867 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.087, H2=-3.941, H3=3.303
2025-08-04 20:41:59,896 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2305 sites
2025-08-04 20:42:04,025 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.906, H2=-1.616, H3=-7.275
2025-08-04 20:42:04,054 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1816 sites
2025-08-04 20:42:07,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=0.400, H2=0.826, H3=-0.150
2025-08-04 20:42:07,318 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3052 sites
2025-08-04 20:42:12,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=4.692, H2=0.665, H3=1.490
2025-08-04 20:42:12,803 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 480 sites
2025-08-04 20:42:13,659 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.239, H2=-7.762, H3=1.942
2025-08-04 20:42:13,686 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 591 sites
2025-08-04 20:42:14,730 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=4.048, H2=0.947, H3=-0.148
2025-08-04 20:42:14,757 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1170 sites
2025-08-04 20:42:16,835 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=31.086, H2=-1.821, H3=-7.233
2025-08-04 20:42:16,862 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 698 sites
2025-08-04 20:42:18,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-5.611, H2=-3.381, H3=-5.324
2025-08-04 20:42:18,135 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1471 sites
2025-08-04 20:42:20,757 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=11.950, H2=2.790, H3=-2.065
2025-08-04 20:42:20,786 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2022 sites
2025-08-04 20:42:24,372 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=9.535, H2=1.870, H3=-1.675
2025-08-04 20:42:24,405 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 4283 sites
2025-08-04 20:42:32,056 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=10.845, H2=4.959, H3=5.019
2025-08-04 20:42:32,086 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2192 sites
2025-08-04 20:42:35,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=2.383, H2=1.597, H3=-0.503
2025-08-04 20:42:36,018 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 978 sites
2025-08-04 20:42:37,772 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-1.426, H2=1.902, H3=-2.810
2025-08-04 20:42:37,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1665 sites
2025-08-04 20:42:40,790 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=5.456, H2=-0.183, H3=1.334
2025-08-04 20:42:40,815 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-08-04 20:42:40,815 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-08-04 20:42:40,817 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-08-04 20:42:40,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1358 sites
2025-08-04 20:42:43,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=30.231, H2=-0.219, H3=0.231
2025-08-04 20:42:43,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1024 sites
2025-08-04 20:42:45,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=8.143, H2=-3.187, H3=-2.334
2025-08-04 20:42:45,167 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3778 sites
2025-08-04 20:42:51,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.071, H2=-1.970, H3=0.255
2025-08-04 20:42:51,951 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2018 sites
2025-08-04 20:42:55,527 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.544, H2=-0.258, H3=-3.472
2025-08-04 20:42:55,559 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4082 sites
2025-08-04 20:43:02,813 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=39.545, H2=1.786, H3=1.828
2025-08-04 20:43:02,840 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 624 sites
2025-08-04 20:43:03,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.074, H2=-1.840, H3=-1.426
2025-08-04 20:43:03,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3539 sites
2025-08-04 20:43:10,266 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=36.699, H2=-4.645, H3=-1.187
2025-08-04 20:43:10,297 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2353 sites
2025-08-04 20:43:14,509 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.220, H2=0.492, H3=1.367
2025-08-04 20:43:14,542 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3864 sites
2025-08-04 20:43:21,430 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=12.985, H2=5.134, H3=3.145
2025-08-04 20:43:21,462 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3580 sites
2025-08-04 20:43:27,854 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=42.157, H2=14.676, H3=1.000
2025-08-04 20:43:27,885 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3416 sites
2025-08-04 20:43:33,956 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=7.670, H2=-3.645, H3=5.092
2025-08-04 20:43:33,986 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2305 sites
2025-08-04 20:43:38,117 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=13.793, H2=-1.899, H3=-10.299
2025-08-04 20:43:38,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1816 sites
2025-08-04 20:43:41,389 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-0.090, H2=0.343, H3=-0.615
2025-08-04 20:43:41,420 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3052 sites
2025-08-04 20:43:46,915 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=4.597, H2=0.638, H3=1.791
2025-08-04 20:43:46,942 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 480 sites
2025-08-04 20:43:47,802 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.703, H2=-6.834, H3=2.654
2025-08-04 20:43:47,828 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 591 sites
2025-08-04 20:43:48,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=3.905, H2=1.923, H3=-0.114
2025-08-04 20:43:48,910 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1170 sites
2025-08-04 20:43:50,990 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=35.637, H2=-1.759, H3=-9.526
2025-08-04 20:43:51,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 698 sites
2025-08-04 20:43:52,255 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-4.968, H2=-3.974, H3=-4.724
2025-08-04 20:43:52,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1471 sites
2025-08-04 20:43:54,885 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=18.221, H2=2.302, H3=-2.106
2025-08-04 20:43:54,914 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2022 sites
2025-08-04 20:43:58,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=8.241, H2=1.867, H3=-1.361
2025-08-04 20:43:58,537 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 4283 sites
2025-08-04 20:44:06,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=11.110, H2=7.056, H3=5.994
2025-08-04 20:44:06,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2192 sites
2025-08-04 20:44:10,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.656, H2=2.089, H3=-0.469
2025-08-04 20:44:10,162 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 978 sites
2025-08-04 20:44:11,921 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-1.515, H2=1.436, H3=-1.746
2025-08-04 20:44:11,949 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1665 sites
2025-08-04 20:44:14,932 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=6.175, H2=-0.394, H3=1.992
2025-08-04 20:44:14,957 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-08-04 20:44:14,957 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-08-04 20:44:14,959 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-08-04 20:44:14,987 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1358 sites
2025-08-04 20:44:17,417 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=30.231, H2=-0.219, H3=0.231
2025-08-04 20:44:17,445 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1024 sites
2025-08-04 20:44:19,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=8.143, H2=-3.187, H3=-2.334
2025-08-04 20:44:19,318 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3778 sites
2025-08-04 20:44:26,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.071, H2=-1.970, H3=0.255
2025-08-04 20:44:26,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2018 sites
2025-08-04 20:44:29,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.544, H2=-0.258, H3=-3.472
2025-08-04 20:44:29,738 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4082 sites
2025-08-04 20:44:37,109 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=39.545, H2=1.786, H3=1.828
2025-08-04 20:44:37,136 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 624 sites
2025-08-04 20:44:38,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.074, H2=-1.840, H3=-1.426
2025-08-04 20:44:38,285 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1797 sites
2025-08-04 20:44:41,542 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.682, H2=-0.550, H3=1.332
2025-08-04 20:44:41,570 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1742 sites
2025-08-04 20:44:44,731 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.356, H2=-6.429, H3=-4.256
2025-08-04 20:44:44,761 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2353 sites
2025-08-04 20:44:49,026 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.807, H2=0.213, H3=1.200
2025-08-04 20:44:49,059 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3864 sites
2025-08-04 20:44:56,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=13.195, H2=5.679, H3=2.603
2025-08-04 20:44:56,095 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3580 sites
2025-08-04 20:45:02,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=33.722, H2=19.142, H3=1.918
2025-08-04 20:45:02,560 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3416 sites
2025-08-04 20:45:08,775 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=11.559, H2=-3.456, H3=6.839
2025-08-04 20:45:08,804 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2305 sites
2025-08-04 20:45:12,973 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=11.446, H2=-2.503, H3=-7.303
2025-08-04 20:45:13,002 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1816 sites
2025-08-04 20:45:16,248 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.067, H2=0.393, H3=0.014
2025-08-04 20:45:16,279 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3052 sites
2025-08-04 20:45:21,708 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=6.030, H2=0.759, H3=1.477
2025-08-04 20:45:21,734 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 480 sites
2025-08-04 20:45:22,591 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.364, H2=-10.613, H3=2.088
2025-08-04 20:45:22,618 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 591 sites
2025-08-04 20:45:23,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.725, H2=1.041, H3=-0.136
2025-08-04 20:45:23,693 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1170 sites
2025-08-04 20:45:25,790 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=28.731, H2=-1.487, H3=-8.347
2025-08-04 20:45:25,817 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 698 sites
2025-08-04 20:45:27,065 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-8.571, H2=-3.563, H3=-6.415
2025-08-04 20:45:27,093 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1471 sites
2025-08-04 20:45:29,732 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=11.622, H2=2.213, H3=-1.836
2025-08-04 20:45:29,762 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2022 sites
2025-08-04 20:45:33,404 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=14.636, H2=2.141, H3=-1.265
2025-08-04 20:45:33,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 4283 sites
2025-08-04 20:45:41,103 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=10.086, H2=6.062, H3=7.323
2025-08-04 20:45:41,132 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2192 sites
2025-08-04 20:45:45,062 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=1.384, H2=1.647, H3=-0.114
2025-08-04 20:45:45,089 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 978 sites
2025-08-04 20:45:46,843 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-1.648, H2=1.604, H3=-2.170
2025-08-04 20:45:46,872 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1665 sites
2025-08-04 20:45:49,842 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=7.960, H2=-0.887, H3=1.234
2025-08-04 20:45:49,868 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-08-04 20:45:49,869 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-08-04 20:45:49,870 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-08-04 20:45:49,898 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1358 sites
2025-08-04 20:45:52,324 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=30.231, H2=-0.219, H3=0.231
2025-08-04 20:45:52,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1024 sites
2025-08-04 20:45:54,183 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=8.143, H2=-3.187, H3=-2.334
2025-08-04 20:45:54,215 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3778 sites
2025-08-04 20:46:00,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.071, H2=-1.970, H3=0.255
2025-08-04 20:46:01,005 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2018 sites
2025-08-04 20:46:04,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.544, H2=-0.258, H3=-3.472
2025-08-04 20:46:04,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4082 sites
2025-08-04 20:46:11,973 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=39.545, H2=1.786, H3=1.828
2025-08-04 20:46:12,000 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 624 sites
2025-08-04 20:46:13,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.074, H2=-1.840, H3=-1.426
2025-08-04 20:46:13,137 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1797 sites
2025-08-04 20:46:16,353 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.682, H2=-0.550, H3=1.332
2025-08-04 20:46:16,381 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1742 sites
2025-08-04 20:46:19,487 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.356, H2=-6.429, H3=-4.256
2025-08-04 20:46:19,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2353 sites
2025-08-04 20:46:23,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.807, H2=0.213, H3=1.200
2025-08-04 20:46:23,738 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3864 sites
2025-08-04 20:46:30,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=13.195, H2=5.679, H3=2.603
2025-08-04 20:46:30,667 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3580 sites
2025-08-04 20:46:37,066 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=33.722, H2=19.142, H3=1.918
2025-08-04 20:46:37,097 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3416 sites
2025-08-04 20:46:43,164 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=11.559, H2=-3.456, H3=6.839
2025-08-04 20:46:43,194 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2305 sites
2025-08-04 20:46:47,285 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=11.446, H2=-2.503, H3=-7.303
2025-08-04 20:46:47,313 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1816 sites
2025-08-04 20:46:50,548 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.067, H2=0.393, H3=0.014
2025-08-04 20:46:50,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3052 sites
2025-08-04 20:46:55,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=6.030, H2=0.759, H3=1.477
2025-08-04 20:46:56,005 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 480 sites
2025-08-04 20:46:56,852 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.364, H2=-10.613, H3=2.088
2025-08-04 20:46:56,878 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 591 sites
2025-08-04 20:46:57,925 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.725, H2=1.041, H3=-0.136
2025-08-04 20:46:57,953 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1170 sites
2025-08-04 20:47:00,024 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=28.731, H2=-1.487, H3=-8.347
2025-08-04 20:47:00,052 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 698 sites
2025-08-04 20:47:01,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-8.571, H2=-3.563, H3=-6.415
2025-08-04 20:47:01,316 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1471 sites
2025-08-04 20:47:03,924 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=11.622, H2=2.213, H3=-1.836
2025-08-04 20:47:03,955 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2022 sites
2025-08-04 20:47:07,545 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=14.636, H2=2.141, H3=-1.265
2025-08-04 20:47:07,574 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1441 sites
2025-08-04 20:47:10,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-4.200, H2=-3.231, H3=-1.002
2025-08-04 20:47:10,149 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2842 sites
2025-08-04 20:47:15,208 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=3.005, H2=6.878, H3=6.268
2025-08-04 20:47:15,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2192 sites
2025-08-04 20:47:19,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=1.690, H2=1.829, H3=-0.198
2025-08-04 20:47:19,170 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 978 sites
2025-08-04 20:47:20,908 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-2.268, H2=0.949, H3=-2.472
2025-08-04 20:47:20,937 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1665 sites
2025-08-04 20:47:23,894 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=6.243, H2=-0.720, H3=1.372
2025-08-04 20:47:23,920 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-08-04 20:47:23,920 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-08-04 20:47:23,921 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-08-04 20:47:23,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1358 sites
2025-08-04 20:47:26,366 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=30.231, H2=-0.219, H3=0.231
2025-08-04 20:47:26,395 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1024 sites
2025-08-04 20:47:28,218 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=8.143, H2=-3.187, H3=-2.334
2025-08-04 20:47:28,251 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3778 sites
2025-08-04 20:47:34,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.071, H2=-1.970, H3=0.255
2025-08-04 20:47:34,984 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2018 sites
2025-08-04 20:47:38,582 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.544, H2=-0.258, H3=-3.472
2025-08-04 20:47:38,609 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 961 sites
2025-08-04 20:47:40,308 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=3.167, H2=0.340, H3=-1.814
2025-08-04 20:47:40,339 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3121 sites
2025-08-04 20:47:45,897 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.631, H2=2.387, H3=2.822
2025-08-04 20:47:45,923 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 624 sites
2025-08-04 20:47:47,030 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.654, H2=-1.525, H3=-1.623
2025-08-04 20:47:47,059 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1797 sites
2025-08-04 20:47:50,247 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.140, H2=-0.478, H3=0.743
2025-08-04 20:47:50,277 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1742 sites
2025-08-04 20:47:53,371 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=20.812, H2=-9.637, H3=-4.154
2025-08-04 20:47:53,400 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2353 sites
2025-08-04 20:47:57,583 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.566, H2=0.360, H3=1.070
2025-08-04 20:47:57,615 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3864 sites
2025-08-04 20:48:04,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=11.333, H2=8.520, H3=3.698
2025-08-04 20:48:04,575 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3580 sites
2025-08-04 20:48:11,033 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=47.648, H2=19.126, H3=1.864
2025-08-04 20:48:11,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3416 sites
2025-08-04 20:48:17,239 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=9.104, H2=-3.234, H3=3.356
2025-08-04 20:48:17,267 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2305 sites
2025-08-04 20:48:21,451 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=9.654, H2=-2.477, H3=-5.974
2025-08-04 20:48:21,479 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1816 sites
2025-08-04 20:48:24,767 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.250, H2=0.340, H3=-0.435
2025-08-04 20:48:24,797 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3052 sites
2025-08-04 20:48:30,292 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=3.979, H2=0.704, H3=1.186
2025-08-04 20:48:30,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 480 sites
2025-08-04 20:48:31,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.129, H2=-7.773, H3=2.721
2025-08-04 20:48:31,202 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 591 sites
2025-08-04 20:48:32,253 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.940, H2=1.356, H3=-0.019
2025-08-04 20:48:32,280 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1170 sites
2025-08-04 20:48:34,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=30.236, H2=-1.212, H3=-7.944
2025-08-04 20:48:34,400 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 698 sites
2025-08-04 20:48:35,637 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-5.996, H2=-4.894, H3=-6.976
2025-08-04 20:48:35,665 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1471 sites
2025-08-04 20:48:38,285 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=17.615, H2=2.279, H3=-2.166
2025-08-04 20:48:38,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2022 sites
2025-08-04 20:48:41,907 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=12.086, H2=2.681, H3=-1.590
2025-08-04 20:48:41,934 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1441 sites
2025-08-04 20:48:44,496 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.864, H2=-3.345, H3=-0.666
2025-08-04 20:48:44,526 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2842 sites
2025-08-04 20:48:49,592 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=3.219, H2=6.478, H3=7.533
2025-08-04 20:48:49,621 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2192 sites
2025-08-04 20:48:53,521 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=1.551, H2=1.019, H3=-0.313
2025-08-04 20:48:53,548 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 978 sites
2025-08-04 20:48:55,287 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-1.545, H2=1.133, H3=-2.345
2025-08-04 20:48:55,315 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1665 sites
2025-08-04 20:48:58,278 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=6.548, H2=-0.506, H3=1.524
2025-08-04 20:48:58,302 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-08-04 20:48:58,303 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-08-04 20:48:58,304 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-08-04 20:48:58,332 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1358 sites
2025-08-04 20:49:00,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=30.231, H2=-0.219, H3=0.231
2025-08-04 20:49:00,772 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1024 sites
2025-08-04 20:49:02,596 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=8.143, H2=-3.187, H3=-2.334
2025-08-04 20:49:02,628 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3778 sites
2025-08-04 20:49:09,382 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.071, H2=-1.970, H3=0.255
2025-08-04 20:49:09,411 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2018 sites
2025-08-04 20:49:12,966 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.544, H2=-0.258, H3=-3.472
2025-08-04 20:49:12,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 961 sites
2025-08-04 20:49:14,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=3.167, H2=0.340, H3=-1.814
2025-08-04 20:49:14,722 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3121 sites
2025-08-04 20:49:20,294 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.631, H2=2.387, H3=2.822
2025-08-04 20:49:20,320 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 624 sites
2025-08-04 20:49:21,425 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.654, H2=-1.525, H3=-1.623
2025-08-04 20:49:21,454 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1797 sites
2025-08-04 20:49:24,641 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.140, H2=-0.478, H3=0.743
2025-08-04 20:49:24,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1742 sites
2025-08-04 20:49:27,774 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=20.812, H2=-9.637, H3=-4.154
2025-08-04 20:49:27,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2353 sites
2025-08-04 20:49:31,977 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.566, H2=0.360, H3=1.070
2025-08-04 20:49:32,009 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3864 sites
2025-08-04 20:49:38,904 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=11.333, H2=8.520, H3=3.698
2025-08-04 20:49:38,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3580 sites
2025-08-04 20:49:45,283 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=47.648, H2=19.126, H3=1.864
2025-08-04 20:49:45,310 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 909 sites
2025-08-04 20:49:46,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-2.324, H2=-4.591, H3=-1.735
2025-08-04 20:49:46,952 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2507 sites
2025-08-04 20:49:51,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.231, H2=-2.054, H3=5.465
2025-08-04 20:49:51,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2305 sites
2025-08-04 20:49:55,532 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.344, H2=-1.920, H3=-7.479
2025-08-04 20:49:55,560 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1816 sites
2025-08-04 20:49:58,794 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-0.609, H2=0.642, H3=-0.521
2025-08-04 20:49:58,825 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3052 sites
2025-08-04 20:50:04,245 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=4.850, H2=0.597, H3=1.531
2025-08-04 20:50:04,271 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 480 sites
2025-08-04 20:50:05,123 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.676, H2=-10.787, H3=2.304
2025-08-04 20:50:05,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 591 sites
2025-08-04 20:50:06,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=4.403, H2=0.953, H3=-0.435
2025-08-04 20:50:06,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1170 sites
2025-08-04 20:50:08,306 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=32.588, H2=-1.556, H3=-7.745
2025-08-04 20:50:08,333 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 698 sites
2025-08-04 20:50:09,575 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-6.461, H2=-4.946, H3=-7.095
2025-08-04 20:50:09,603 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1471 sites
2025-08-04 20:50:12,222 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=12.778, H2=2.716, H3=-2.692
2025-08-04 20:50:12,251 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2022 sites
2025-08-04 20:50:15,841 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=7.888, H2=2.401, H3=-0.886
2025-08-04 20:50:15,869 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1441 sites
2025-08-04 20:50:18,423 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-3.955, H2=-2.762, H3=-0.757
2025-08-04 20:50:18,453 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2842 sites
2025-08-04 20:50:23,491 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=4.290, H2=6.385, H3=5.656
2025-08-04 20:50:23,520 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2192 sites
2025-08-04 20:50:27,430 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=1.582, H2=1.384, H3=-0.133
2025-08-04 20:50:27,457 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 978 sites
2025-08-04 20:50:29,194 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-1.297, H2=1.375, H3=-2.715
2025-08-04 20:50:29,222 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1665 sites
2025-08-04 20:50:32,167 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=6.453, H2=-0.165, H3=1.341
2025-08-04 20:50:32,192 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-08-04 20:50:32,192 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-08-04 20:50:32,194 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-08-04 20:50:32,223 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1358 sites
2025-08-04 20:50:34,633 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=30.231, H2=-0.219, H3=0.231
2025-08-04 20:50:34,661 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1024 sites
2025-08-04 20:50:36,472 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=8.143, H2=-3.187, H3=-2.334
2025-08-04 20:50:36,505 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3778 sites
2025-08-04 20:50:43,253 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.071, H2=-1.970, H3=0.255
2025-08-04 20:50:43,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2018 sites
2025-08-04 20:50:46,851 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.544, H2=-0.258, H3=-3.472
2025-08-04 20:50:46,878 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 961 sites
2025-08-04 20:50:48,578 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=3.167, H2=0.340, H3=-1.814
2025-08-04 20:50:48,610 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3121 sites
2025-08-04 20:50:54,136 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.631, H2=2.387, H3=2.822
2025-08-04 20:50:54,162 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 624 sites
2025-08-04 20:50:55,268 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.654, H2=-1.525, H3=-1.623
2025-08-04 20:50:55,297 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1797 sites
2025-08-04 20:50:58,490 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.140, H2=-0.478, H3=0.743
2025-08-04 20:50:58,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1742 sites
2025-08-04 20:51:01,613 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=20.812, H2=-9.637, H3=-4.154
2025-08-04 20:51:01,642 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2353 sites
2025-08-04 20:51:05,842 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.566, H2=0.360, H3=1.070
2025-08-04 20:51:05,875 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3864 sites
2025-08-04 20:51:12,759 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=11.333, H2=8.520, H3=3.698
2025-08-04 20:51:12,792 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3580 sites
2025-08-04 20:51:19,126 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=47.648, H2=19.126, H3=1.864
2025-08-04 20:51:19,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 909 sites
2025-08-04 20:51:20,761 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-2.324, H2=-4.591, H3=-1.735
2025-08-04 20:51:20,791 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2507 sites
2025-08-04 20:51:25,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.231, H2=-2.054, H3=5.465
2025-08-04 20:51:25,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2305 sites
2025-08-04 20:51:29,391 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.344, H2=-1.920, H3=-7.479
2025-08-04 20:51:29,419 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1816 sites
2025-08-04 20:51:32,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-0.609, H2=0.642, H3=-0.521
2025-08-04 20:51:32,696 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3052 sites
2025-08-04 20:51:38,168 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=4.850, H2=0.597, H3=1.531
2025-08-04 20:51:38,195 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 480 sites
2025-08-04 20:51:39,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.676, H2=-10.787, H3=2.304
2025-08-04 20:51:39,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 591 sites
2025-08-04 20:51:40,141 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=4.403, H2=0.953, H3=-0.435
2025-08-04 20:51:40,167 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 321 sites
2025-08-04 20:51:40,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=2.965, H2=2.232, H3=-3.923
2025-08-04 20:51:40,771 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 849 sites
2025-08-04 20:51:42,287 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=49.777, H2=-3.640, H3=-7.337
2025-08-04 20:51:42,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 698 sites
2025-08-04 20:51:43,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-5.362, H2=-4.095, H3=-7.377
2025-08-04 20:51:43,583 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1471 sites
2025-08-04 20:51:46,191 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=14.207, H2=2.539, H3=-1.580
2025-08-04 20:51:46,220 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2022 sites
2025-08-04 20:51:49,804 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=10.224, H2=2.596, H3=-1.034
2025-08-04 20:51:49,833 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1441 sites
2025-08-04 20:51:52,394 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-4.947, H2=-4.077, H3=-0.839
2025-08-04 20:51:52,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2842 sites
2025-08-04 20:51:57,441 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=3.626, H2=7.084, H3=6.980
2025-08-04 20:51:57,470 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2192 sites
2025-08-04 20:52:01,353 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=1.811, H2=1.072, H3=-0.183
2025-08-04 20:52:01,380 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 978 sites
2025-08-04 20:52:03,110 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-1.662, H2=1.391, H3=-2.584
2025-08-04 20:52:03,138 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1665 sites
2025-08-04 20:52:06,078 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=7.927, H2=-0.767, H3=1.762
2025-08-04 20:52:06,104 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-08-04 20:52:06,104 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-08-04 20:52:06,105 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-08-04 20:52:06,133 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1358 sites
2025-08-04 20:52:08,535 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=30.231, H2=-0.219, H3=0.231
2025-08-04 20:52:08,563 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1024 sites
2025-08-04 20:52:10,367 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=8.143, H2=-3.187, H3=-2.334
2025-08-04 20:52:10,399 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3778 sites
