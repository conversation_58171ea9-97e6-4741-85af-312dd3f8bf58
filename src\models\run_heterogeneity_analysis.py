#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON><PERSON> to run heterogeneity analysis on precipitation clusters.

This script calculates heterogeneity statistics (H1, H2, H3) using L-moments
as described in <PERSON><PERSON><PERSON> and <PERSON> (1997) for precipitation clusters.

Updated to work with the new function-based heterogeneity.py implementation
that uses lmoments3 instead of R packages.
"""

import os
import sys
import logging
import argparse
import numpy as np
import pandas as pd
import xarray as xr
import matplotlib.pyplot as plt
from pathlib import Path

# Import the standalone functions from heterogeneity.py (in same directory)
from src.models.heterogeneity import (
    calc_heterogeneity_parameter,
    calc_regional_lmoments,
    lmoment_ratio_diagram,
    calc_V,
    kappa_sim,
    calc_heterogeneity_parameter_parallel,
)
from src.models.remap_clusters import remap_clusters_file
from src.utils.logging_config import setup_logging

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Calculate heterogeneity statistics for precipitation clusters.'
    )
    
    parser.add_argument(
        '--precip-path',
        type=str,
        required=True,
        help='Path to the precipitation data file (NetCDF) containing AMS series'
    )
    
    parser.add_argument(
        '--cluster-path',
        type=str,
        required=True,
        help='Path to the cluster data file containing cluster assignments for each grid point'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='heterogeneity_results',
        help='Directory to save the results (default: heterogeneity_results)'
    )
    
    parser.add_argument(
        '--max-clusters',
        type=int,
        default=20,
        help='Maximum number of clusters to analyze (default: 20)'
    )
    
    parser.add_argument(
        '--nsim',
        type=int,
        default=500,
        help='Number of simulations for the heterogeneity test (default: 500)'
    )
    
    parser.add_argument(
        '--seed',
        type=int,
        default=42,
        help='Random seed for reproducibility (default: 42)'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='Set the logging level (default: INFO)'
    )

    parser.add_argument(
        '--remap-clusters',
        action='store_true',
        default=True,
        help='Remap cluster assignments from 1D to 2D grid before analysis'
    )

    parser.add_argument(
        '--stats-file',
        type=str,
        default=None,
        help='Path to cluster_precip_stats.nc file for remapping (default: cluster_precip_stats.nc)'
    )

    return parser.parse_args()

def load_data(precip_path, cluster_path):
    """
    Load precipitation and cluster data from NetCDF files.

    Parameters:
    -----------
    precip_path : str
        Path to precipitation data file
    cluster_path : str
        Path to cluster data file

    Returns:
    --------
    tuple
        (precip_data, cluster_data) as xarray datasets
    """
    logger.info(f"Loading precipitation data from {precip_path}")
    precip_data = xr.open_dataset(precip_path)

    # If the precipitation data has a time dimension, compute annual maxima
    if 'time' in precip_data.dims:
        logger.info("Computing annual maxima from time series data")
        precip_data = precip_data.groupby('time.year').max(dim='time')

    logger.info(f"Loading cluster data from {cluster_path}")
    cluster_data = xr.open_dataset(cluster_path)

    logger.info("Data loaded successfully")
    return precip_data, cluster_data


def extract_region_data(precip_data, cluster_assignments, region_id):
    """
    Extract precipitation data for a specific region/cluster.

    Parameters:
    -----------
    precip_data : xarray.Dataset
        Precipitation data
    cluster_assignments : numpy.ndarray
        2D array of cluster assignments
    region_id : int
        ID of the region to extract

    Returns:
    --------
    list
        List of precipitation time series for each site in the region
    """
    # Create mask for this region
    region_mask = (cluster_assignments == region_id)

    # Get precipitation variable (assume it's the main data variable)
    precip_var = list(precip_data.data_vars)[0]
    precip_values = precip_data[precip_var].values

    # Extract precipitation data for this region
    region_precip = []

    # Handle different data structures
    if len(precip_values.shape) == 3:  # (year, lat, lon)
        for i in range(precip_values.shape[1]):  # lat
            for j in range(precip_values.shape[2]):  # lon
                if region_mask[i, j]:
                    site_data = precip_values[:, i, j]
                    # Filter out NaN values
                    site_data = site_data[~np.isnan(site_data)]
                    if len(site_data) >= 5:  # Need at least 5 values for L-moments
                        region_precip.append(site_data)
    elif len(precip_values.shape) == 2:  # (year, site) - already processed
        for site in range(precip_values.shape[1]):
            if region_mask.flat[site]:  # Use flat indexing for 1D mask
                site_data = precip_values[:, site]
                site_data = site_data[~np.isnan(site_data)]
                if len(site_data) >= 5:
                    region_precip.append(site_data)

    return region_precip


def run_heterogeneity_analysis_for_region(region_data, region_id, nsim, seed):
    """
    Run heterogeneity analysis for a single region.

    Parameters:
    -----------
    region_data : list
        List of precipitation time series for sites in the region
    region_id : int
        Region identifier
    nsim : int
        Number of simulations
    seed : int
        Random seed

    Returns:
    --------
    dict
        Dictionary containing heterogeneity statistics
    """
    if len(region_data) < 2:
        logger.warning(f"Region {region_id} has fewer than 2 sites, skipping")
        return None

    logger.info(f"Processing region {region_id} with {len(region_data)} sites")

    # Set random seed for reproducibility
    np.random.seed(seed + region_id)  # Different seed for each region

    # Prepare data for heterogeneity calculation
    rec_length = [len(site_data) for site_data in region_data]
    nstations = len(region_data)

    try:
        # Calculate heterogeneity parameters
        H, H2, H3 = calc_heterogeneity_parameter_parallel(
            region_data, rec_length, nstations, nsim,n_jobs=64
        )

        # Calculate regional L-moments for additional information
        regional_lmom, station_lmoms = calc_regional_lmoments(region_data)

        # Interpret results
        def interpret_h(h_val):
            if np.isnan(h_val):
                return "Unknown"
            elif abs(h_val) < 1:
                return "Acceptably homogeneous"
            elif abs(h_val) < 2:
                return "Possibly heterogeneous"
            else:
                return "Definitely heterogeneous"

        result = {
            'region_id': region_id,
            'n_sites': nstations,
            'H1': H[0],
            'H2': H2[0],
            'H3': H3[0],
            'regional_l1': regional_lmom[0],
            'regional_t': regional_lmom[1],
            'regional_t3': regional_lmom[2],
            'regional_t4': regional_lmom[3],
            'homogeneity_H1': interpret_h(H[0]),
            'homogeneity_H2': interpret_h(H2[0]),
            'homogeneity_H3': interpret_h(H3[0]),
            'record_lengths': rec_length
        }

        logger.info(f"Region {region_id}: H1={H[0]:.3f}, H2={H2[0]:.3f}, H3={H3[0]:.3f}")
        return result

    except Exception as e:
        logger.error(f"Error processing region {region_id}: {str(e)}")
        return None


def save_results(results, output_dir):
    """
    Save heterogeneity analysis results to CSV files.

    Parameters:
    -----------
    results : dict
        Dictionary containing results for each k value
    output_dir : str
        Output directory path
    """
    logger.info("Saving results to CSV files")

    # Create summary data for all k values
    summary_data = []

    for k, k_results in results.items():
        for region_result in k_results:
            if region_result is not None:
                row = {
                    'k': k,
                    'region_id': region_result['region_id'],
                    'n_sites': region_result['n_sites'],
                    'H1': region_result['H1'],
                    'H2': region_result['H2'],
                    'H3': region_result['H3'],
                    'regional_l1': region_result['regional_l1'],
                    'regional_t': region_result['regional_t'],
                    'regional_t3': region_result['regional_t3'],
                    'regional_t4': region_result['regional_t4'],
                    'homogeneity_H1': region_result['homogeneity_H1'],
                    'homogeneity_H2': region_result['homogeneity_H2'],
                    'homogeneity_H3': region_result['homogeneity_H3']
                }
                summary_data.append(row)

    # Save summary table
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        summary_path = os.path.join(output_dir, "heterogeneity_summary.csv")
        summary_df.to_csv(summary_path, index=False)
        logger.info(f"Summary results saved to {summary_path}")

        # Save individual k results
        for k in results.keys():
            k_data = [row for row in summary_data if row['k'] == k]
            if k_data:
                k_df = pd.DataFrame(k_data)
                k_path = os.path.join(output_dir, f"heterogeneity_k{k}.csv")
                k_df.to_csv(k_path, index=False)
                logger.info(f"Results for k={k} saved to {k_path}")
    else:
        logger.warning("No valid results to save")


def create_plots(results, output_dir):
    """
    Create plots of heterogeneity statistics.

    Parameters:
    -----------
    results : dict
        Dictionary containing results for each k value
    output_dir : str
        Output directory path
    """
    logger.info("Creating heterogeneity plots")

    if not results:
        logger.warning("No results to plot")
        return

    # Collect data for plotting
    k_values = sorted(results.keys())

    # Create figure for H1, H2, H3 statistics
    fig, axes = plt.subplots(3, 1, figsize=(12, 15), sharex=True)

    for k in k_values:
        k_results = [r for r in results[k] if r is not None]
        if not k_results:
            continue

        h1_values = [r['H1'] for r in k_results]
        h2_values = [r['H2'] for r in k_results]
        h3_values = [r['H3'] for r in k_results]

        axes[0].scatter([k] * len(h1_values), h1_values, alpha=0.7, s=50)
        axes[1].scatter([k] * len(h2_values), h2_values, alpha=0.7, s=50)
        axes[2].scatter([k] * len(h3_values), h3_values, alpha=0.7, s=50)

    # Add reference lines for heterogeneity thresholds
    for ax in axes:
        ax.axhline(y=1, color='g', linestyle='--', alpha=0.7, label="H=1 (Acceptably homogeneous)")
        ax.axhline(y=-1, color='g', linestyle='--', alpha=0.7)
        ax.axhline(y=2, color='r', linestyle='--', alpha=0.7, label="H=2 (Possibly heterogeneous)")
        ax.axhline(y=-2, color='r', linestyle='--', alpha=0.7)
        ax.grid(True, alpha=0.3)

    # Set labels and titles
    axes[0].set_title("H1 Statistic (L-CV)", fontsize=14)
    axes[1].set_title("H2 Statistic (L-CV and L-skewness)", fontsize=14)
    axes[2].set_title("H3 Statistic (L-skewness and L-kurtosis)", fontsize=14)

    for ax in axes:
        ax.set_ylabel("Heterogeneity Statistic", fontsize=12)

    axes[2].set_xlabel("Number of Clusters (k)", fontsize=12)
    axes[2].set_xticks(k_values)

    # Add legend to the first plot
    axes[0].legend(loc="upper right")

    plt.tight_layout()

    # Save figure
    output_path = os.path.join(output_dir, "heterogeneity_statistics.png")
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"Heterogeneity statistics plot saved to {output_path}")

    # Create average H1 plot across all regions for each k value
    create_average_h1_plot(results, output_dir)

    # Create L-moment ratio diagram if we have data
    try:
        all_t3 = []
        all_t4 = []
        for k_results in results.values():
            for r in k_results:
                if r is not None:
                    all_t3.append(r['regional_t3'])
                    all_t4.append(r['regional_t4'])

        if all_t3 and all_t4:
            fig = lmoment_ratio_diagram(all_t3, all_t4)
            lmom_path = os.path.join(output_dir, "lmoment_ratio_diagram.png")
            plt.savefig(lmom_path, dpi=300, bbox_inches='tight')
            plt.close()
            logger.info(f"L-moment ratio diagram saved to {lmom_path}")
    except Exception as e:
        logger.warning(f"Could not create L-moment ratio diagram: {str(e)}")


def create_average_h1_plot(results, output_dir):
    """
    Create a plot showing the average H1 parameter across all regions for each k cluster value.

    Parameters:
    -----------
    results : dict
        Dictionary containing results for each k value
    output_dir : str
        Output directory path
    """
    logger.info("Creating average H1 plot")

    if not results:
        logger.warning("No results to plot")
        return

    # Collect data for plotting
    k_values = []
    avg_h1_values = []
    std_h1_values = []
    num_regions = []

    for k in sorted(results.keys()):
        k_results = [r for r in results[k] if r is not None]
        if not k_results:
            continue

        h1_values = [r['H1'] for r in k_results if not np.isnan(r['H1'])]

        if h1_values:
            k_values.append(k)
            avg_h1_values.append(np.mean(h1_values))
            std_h1_values.append(np.std(h1_values))
            num_regions.append(len(h1_values))

    if not k_values:
        logger.warning("No valid H1 data to plot")
        return

    # Create the plot
    fig, ax = plt.subplots(figsize=(12, 8))

    # Plot average H1 with error bars (standard deviation)
    ax.errorbar(k_values, avg_h1_values, yerr=std_h1_values,
                marker='o', markersize=8, linewidth=2, capsize=5, capthick=2,
                color='blue', ecolor='lightblue', label='Average H1 ± Std Dev')

    # Add reference lines for heterogeneity thresholds
    ax.axhline(y=1, color='g', linestyle='--', alpha=0.7, linewidth=2,
               label="H=1 (Acceptably homogeneous threshold)")
    ax.axhline(y=-1, color='g', linestyle='--', alpha=0.7, linewidth=2)
    ax.axhline(y=2, color='r', linestyle='--', alpha=0.7, linewidth=2,
               label="H=2 (Possibly heterogeneous threshold)")
    ax.axhline(y=-2, color='r', linestyle='--', alpha=0.7, linewidth=2)

    # Customize the plot
    ax.set_xlabel("Number of Clusters (k)", fontsize=14)
    ax.set_ylabel("Average H1 Statistic", fontsize=14)
    ax.set_title("Average H1 Heterogeneity Statistic by Number of Clusters", fontsize=16, pad=20)
    ax.grid(True, alpha=0.3)
    ax.legend(fontsize=12)

    # Set x-axis ticks to integer values
    ax.set_xticks(k_values)
    ax.tick_params(axis='x', labelrotation=90)

    # Set fixed y-axis range for H1: -20 to 200, increments of 20
    ax.set_ylim(-20, 200)
    ax.set_yticks(np.arange(-20, 201, 20))

    # Add text annotations showing number of regions for each k
    # for i, (k, avg_h1, num_reg) in enumerate(zip(k_values, avg_h1_values, num_regions)):
    #     ax.annotate(f'n={num_reg}',
    #                xy=(k, avg_h1),
    #                xytext=(5, 10),
    #                textcoords='offset points',
    #                fontsize=10,
    #                alpha=0.7)

    # Improve layout
    plt.tight_layout()

    # Save the plot
    output_path = os.path.join(output_dir, "average_h1_by_k.png")
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"Average H1 plot saved to {output_path}")

    # Also create a summary table of the average H1 values
    summary_data = {
        'k': k_values,
        'avg_H1': avg_h1_values,
        'std_H1': std_h1_values,
        'num_regions': num_regions
    }

    summary_df = pd.DataFrame(summary_data)
    summary_path = os.path.join(output_dir, "average_h1_summary.csv")
    summary_df.to_csv(summary_path, index=False)

    logger.info(f"Average H1 summary table saved to {summary_path}")


def main():
    """Run the heterogeneity analysis."""
    # Parse command line arguments
    args = parse_args()

    # Setup logging
    log_level = getattr(logging, args.log_level)
    setup_logging(log_level=log_level)

    logger.info("Starting heterogeneity analysis")
    logger.info(f"Precipitation data: {args.precip_path}")
    logger.info(f"Cluster data: {args.cluster_path}")
    logger.info(f"Output directory: {args.output_dir}")
    logger.info(f"Maximum clusters: {args.max_clusters}")
    logger.info(f"Number of simulations: {args.nsim}")
    logger.info(f"Remap clusters: {args.remap_clusters}")

    try:
        # Create output directory if it doesn't exist
        os.makedirs(args.output_dir, exist_ok=True)

        # Remap clusters if requested
        cluster_path_to_use = args.cluster_path
        if args.remap_clusters:
            logger.info("Remapping clusters from 1D to 2D grid format")
            try:
                remapped_path = remap_clusters_file(
                    ci_results_path=args.cluster_path,
                    stats_file_path=args.stats_file,
                    output_suffix="_remap"
                )
                cluster_path_to_use = remapped_path
                logger.info(f"Using remapped cluster file: {cluster_path_to_use}")
            except Exception as e:
                logger.error(f"Failed to remap clusters: {e}")
                logger.info("Continuing with original cluster file")

        # Load data
        logger.info("Loading data")
        precip_data, cluster_data = load_data(args.precip_path, cluster_path_to_use)

        # Run analysis for each k value
        logger.info("Running heterogeneity analysis")
        results = {}

        for k in range(2, args.max_clusters + 1):
            logger.info(f"Processing k={k}")

            # Get cluster assignments for this k value
            cluster_var = f"k={k}"
            if cluster_var not in cluster_data:
                logger.warning(f"Cluster variable {cluster_var} not found in data, skipping")
                continue

            cluster_assignments = cluster_data[cluster_var].values

            # Process each region
            k_results = []
            kvals = np.unique(cluster_assignments).astype(int)
            logger.warning(f"Region {kvals}")
            for region_id in kvals:
                # Extract region data
                region_data = extract_region_data(precip_data, cluster_assignments, region_id)

                if len(region_data) >= 2:  # Need at least 2 sites
                    # Run heterogeneity analysis for this region
                    result = run_heterogeneity_analysis_for_region(
                        region_data, region_id, args.nsim, args.seed
                    )
                    k_results.append(result)
                else:
                    logger.warning(f"Region {region_id} in k={k} has insufficient data")
                    k_results.append(None)

            results[k] = k_results

        # Save results
        logger.info("Saving results")
        save_results(results, args.output_dir)

        # Create plots
        logger.info("Creating plots")
        create_plots(results, args.output_dir)

        logger.info("Heterogeneity analysis completed successfully")

    except Exception as e:
        logger.error(f"Error in heterogeneity analysis: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
