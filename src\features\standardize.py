# -*- coding: utf-8 -*-
import numpy as np
import xarray as xr
import logging

logger = logging.getLogger(__name__)

class VariableStandardizer:
    """
    Class for standardizing variables for clustering analysis.
    """
    def __init__(self, variable_paths: dict, variable_names: dict = None):
        """
        Initialize and load data from file paths or xarray DataArrays.
        
        Parameters:
        -----------
        variable_paths : dict
            Mapping variable keys to either NetCDF file paths or xarray.DataArray objects.
        variable_names : dict, optional
            Mapping variable keys to variable names (used only for file inputs).
        """
        self.variables = {}
        self.means = {}
        self.stds = {}
        self.mask = None
        self.original_shape = None
        self.skipped_variables = []  # Track variables that were skipped due to all NaN
        self.load_variables(variable_paths, variable_names or {})
        
    def load_variables(self, sources_dict, varnames_dict):
        """
        Load 2D data arrays from xarray objects or NetCDF files.
        Skip variables that contain only NaN values.
        
        Parameters:
        -----------
        sources_dict : dict
            Dictionary of data sources
        varnames_dict : dict
            Dictionary of variable names
        """
        for key, source in sources_dict.items():
            if isinstance(source, xr.DataArray):
                data = source
            elif isinstance(source, str):  # assume it's a file path
                ds = xr.open_dataset(source)
                varname = varnames_dict.get(key)
                if varname is None:
                    raise ValueError(f"Variable name for key '{key}' is missing.")
                data = ds[varname]
            else:
                raise TypeError(f"Unsupported input type for key '{key}': {type(source)}")
            
            if data.ndim == 3:
                data = data[0]  # Use first time step
            
            data_squeezed = data.squeeze()
            
            # Check if variable contains only NaN values
            if np.all(np.isnan(data_squeezed.values)):
                logger.warning(f"Variable '{key}' contains only NaN values - skipping")
                self.skipped_variables.append(key)
                continue
            
            # Check if variable contains no valid (non-NaN) values
            valid_count = np.sum(~np.isnan(data_squeezed.values))
            if valid_count == 0:
                logger.warning(f"Variable '{key}' has no valid (non-NaN) values - skipping")
                self.skipped_variables.append(key)
                continue
                
            logger.info(f"Loaded variable '{key}': {valid_count} valid values out of {data_squeezed.size} total")
            self.variables[key] = data_squeezed
        
        # Check if any variables were successfully loaded
        if not self.variables:
            raise ValueError("No valid variables loaded - all variables contained only NaN values")
        
        if self.skipped_variables:
            logger.info(f"Skipped variables due to all NaN values: {self.skipped_variables}")
        
        # Assume all variables are the same shape
        first_key = next(iter(self.variables))
        self.original_shape = self.variables[first_key].shape
        
        # Compute common mask: True where all variables are valid (not NaN)
        combined_mask = np.ones(self.original_shape, dtype=bool)
        for arr in self.variables.values():
            combined_mask &= ~np.isnan(arr.values)
        
        self.mask = combined_mask
        valid_points = np.sum(self.mask)
        logger.info(f"Common mask created: {valid_points} valid points out of {np.prod(self.original_shape)} total")
        
        if valid_points == 0:
            raise ValueError("No points are valid across all variables - check for spatial misalignment")
    
    def fit(self):
        """
        Compute mean and std for each variable (ignoring NaNs).
        """
        logger.info("Computing statistics for standardization:")
        for key, arr in self.variables.items():
            data = arr.values
            self.means[key] = np.nanmean(data)
            self.stds[key] = np.nanstd(data, ddof=1)
            logger.info(f"  {key}: mean={self.means[key]:.4f}, std={self.stds[key]:.4f}")
            
            # Check for zero standard deviation (constant variables)
            if self.stds[key] == 0 or np.isnan(self.stds[key]):
                logger.warning(f"  Warning: Variable '{key}' has zero or NaN standard deviation")
    
    def transform(self):
        """
        Standardize each variable, apply common mask, and return stacked 2D array [n_valid_points, n_variables].
        
        Returns:
        --------
        numpy.ndarray
            Standardized data array
        """
        standardized = []
        for key, arr in self.variables.items():
            data = arr.values
            
            # Handle zero standard deviation case
            if self.stds[key] == 0 or np.isnan(self.stds[key]):
                logger.warning(f"Cannot standardize variable '{key}' (std={self.stds[key]}) - using zeros")
                z = np.zeros_like(data)
            else:
                z = (data - self.means[key]) / self.stds[key]
            
            standardized.append(z[self.mask])
        result = np.stack(standardized, axis=1)
        logger.info(f"Transformed data shape: {result.shape} [{result.shape[0]} points x {result.shape[1]} variables]")
        return result
    
    def fit_transform(self):
        """
        Fit and transform the data.
        
        Returns:
        --------
        numpy.ndarray
            Standardized data array
        """
        self.fit()
        return self.transform()
    
    def inverse_transform_to_grid(self, values: np.ndarray):
        """
        Project 1D values (e.g., cluster labels or predictions) back to original 2D shape with NaNs where masked.
        
        Parameters:
        -----------
        values : numpy.ndarray
            1D array of length equal to number of valid points
            
        Returns:
        --------
        numpy.ndarray
            2D array with original shape, with values filled and NaNs in masked locations
        """
        if self.mask is None or self.original_shape is None:
            raise RuntimeError("Data must be loaded and mask computed before inverse_transform_to_grid.")
        
        if len(values) != np.sum(self.mask):
            raise ValueError(f"Values length ({len(values)}) doesn't match number of valid points ({np.sum(self.mask)})")
        
        output = np.full(self.original_shape, np.nan)
        output[self.mask] = values
        return output
    
    def get_variable_info(self):
        """
        Return summary information about loaded and skipped variables.
        
        Returns:
        --------
        dict
            Dictionary with variable information
        """
        info = {
            'loaded_variables': list(self.variables.keys()),
            'skipped_variables': self.skipped_variables,
            'n_loaded': len(self.variables),
            'n_skipped': len(self.skipped_variables),
            'original_shape': self.original_shape,
            'n_valid_points': np.sum(self.mask) if self.mask is not None else 0
        }
        return info
