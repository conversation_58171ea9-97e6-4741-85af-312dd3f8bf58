2025-07-24 16:34:45,160 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-07-24 16:34:45,163 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_1981_2020_AMS.nc
2025-07-24 16:34:45,165 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_wo_WT_twovariables/CI_results_hierarchical.nc
2025-07-24 16:34:45,166 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_wo_WT_twovariables
2025-07-24 16:34:45,168 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 150
2025-07-24 16:34:45,169 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 200
2025-07-24 16:34:45,171 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-07-24 16:34:45,177 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-07-24 16:34:45,183 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_wo_WT_twovariables/CI_results_hierarchical.nc
2025-07-24 16:34:45,185 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-24 16:34:51,018 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-24 16:34:52,544 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-24 16:34:52,663 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-24 16:34:52,739 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-24 16:34:52,770 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-24 16:34:52,828 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-24 16:34:52,892 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-24 16:34:53,006 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-24 16:34:53,056 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-24 16:34:53,239 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-24 16:34:53,258 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-24 16:34:53,282 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-24 16:34:53,437 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-24 16:34:53,452 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-24 16:34:53,477 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-24 16:34:53,494 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-24 16:34:53,716 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-24 16:34:53,731 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-24 16:34:53,756 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-24 16:34:53,788 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-24 16:34:53,790 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-24 16:34:53,819 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-24 16:34:53,822 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-24 16:34:53,824 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-24 16:34:53,865 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-24 16:34:53,890 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-24 16:34:53,915 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-24 16:34:53,956 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-24 16:34:53,984 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-24 16:34:54,025 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-24 16:34:54,066 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-24 16:34:54,107 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-24 16:34:54,148 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-24 16:34:54,176 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-24 16:34:54,204 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-24 16:34:54,232 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-24 16:34:54,273 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-24 16:34:54,314 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-24 16:34:54,355 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-24 16:34:54,477 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-07-24 16:34:54,479 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-24 16:34:54,483 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-24 16:34:54,492 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 150 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125', 'k=126', 'k=127', 'k=128', 'k=129', 'k=130', 'k=131', 'k=132', 'k=133', 'k=134', 'k=135', 'k=136', 'k=137', 'k=138', 'k=139', 'k=140', 'k=141', 'k=142', 'k=143', 'k=144', 'k=145', 'k=146', 'k=147', 'k=148', 'k=149', 'k=150']
2025-07-24 16:34:54,503 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-24 16:34:54,559 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-24 16:34:54,561 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-24 16:34:54,594 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-24 16:34:54,596 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-24 16:34:54,629 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-24 16:34:54,631 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-24 16:34:54,664 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-24 16:34:54,666 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-24 16:34:54,699 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-24 16:34:54,700 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-24 16:34:54,739 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-24 16:34:54,741 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-24 16:34:54,785 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-24 16:34:54,787 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-24 16:34:54,830 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-24 16:34:54,832 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-24 16:34:54,869 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-24 16:34:54,871 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-24 16:34:54,915 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-24 16:34:54,917 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-24 16:34:54,960 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-24 16:34:54,962 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-24 16:34:54,999 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-24 16:34:55,001 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-24 16:34:55,045 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-24 16:34:55,046 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-24 16:34:55,090 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-24 16:34:55,092 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-24 16:34:55,135 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-24 16:34:55,137 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-24 16:34:55,180 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-24 16:34:55,182 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-24 16:34:55,218 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-24 16:34:55,220 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-24 16:34:55,264 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-24 16:34:55,266 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-24 16:34:55,309 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-24 16:34:55,311 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-24 16:34:55,354 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-24 16:34:55,356 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-24 16:34:55,399 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-24 16:34:55,401 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-24 16:34:55,438 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-24 16:34:55,440 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-24 16:34:55,483 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-24 16:34:55,485 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-24 16:34:55,528 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-24 16:34:55,530 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-24 16:34:55,566 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-24 16:34:55,568 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-24 16:34:55,611 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-24 16:34:55,749 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-24 16:34:55,792 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-24 16:34:55,794 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-24 16:34:55,837 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-24 16:34:55,839 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-24 16:34:55,882 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-24 16:34:55,883 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-24 16:34:55,920 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-24 16:34:55,922 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-24 16:34:55,965 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-24 16:34:55,966 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-24 16:34:56,009 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-24 16:34:56,011 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-24 16:34:56,047 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-24 16:34:56,049 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-24 16:34:56,092 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-24 16:34:56,094 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-24 16:34:56,137 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-24 16:34:56,139 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-24 16:34:56,181 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-24 16:34:56,183 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-24 16:34:56,226 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-24 16:34:56,228 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-24 16:34:56,264 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-24 16:34:56,266 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-24 16:34:56,309 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-24 16:34:56,310 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-24 16:34:56,368 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-24 16:34:56,369 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-24 16:34:56,587 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-24 16:34:56,589 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-24 16:34:56,632 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-24 16:34:56,634 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-24 16:34:56,676 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-24 16:34:56,678 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-24 16:34:56,721 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-24 16:34:56,723 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-24 16:34:56,765 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-24 16:34:56,767 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-24 16:34:56,803 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-24 16:34:56,805 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-24 16:34:56,848 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-24 16:34:56,849 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-24 16:34:56,892 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-24 16:34:56,894 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-24 16:34:56,929 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-24 16:34:56,931 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-24 16:34:56,974 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-24 16:34:56,976 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-24 16:34:57,018 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-24 16:34:57,020 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-24 16:34:57,062 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-24 16:34:57,064 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-24 16:34:57,106 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-24 16:34:57,108 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-24 16:34:57,144 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-24 16:34:57,146 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-24 16:34:57,188 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-24 16:34:57,190 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-24 16:34:57,232 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-24 16:34:57,234 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-24 16:34:57,269 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-24 16:34:57,271 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-24 16:34:57,313 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-24 16:34:57,315 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-24 16:34:57,358 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-24 16:34:57,360 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-24 16:34:57,402 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-24 16:34:57,404 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-24 16:34:57,446 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-24 16:34:57,448 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-24 16:34:57,483 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-24 16:34:57,485 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-24 16:34:57,527 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-24 16:34:57,529 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-07-24 16:34:57,571 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-07-24 16:34:57,573 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-07-24 16:34:57,609 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-07-24 16:34:57,610 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-07-24 16:34:57,653 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-07-24 16:34:57,654 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-07-24 16:34:57,696 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-07-24 16:34:57,698 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-07-24 16:34:57,740 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-07-24 16:34:57,742 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-07-24 16:34:57,784 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-07-24 16:34:57,786 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-07-24 16:34:57,821 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-07-24 16:34:57,823 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-07-24 16:34:57,865 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-07-24 16:34:57,867 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-07-24 16:34:57,909 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-07-24 16:34:57,911 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-07-24 16:34:57,946 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-07-24 16:34:57,948 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-07-24 16:34:57,990 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-07-24 16:34:57,992 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-07-24 16:34:58,034 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-07-24 16:34:58,036 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-07-24 16:34:58,078 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-07-24 16:34:58,079 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-07-24 16:34:58,121 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-07-24 16:34:58,123 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-07-24 16:34:58,158 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-07-24 16:34:58,160 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-07-24 16:34:58,202 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-07-24 16:34:58,204 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-07-24 16:34:58,245 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-07-24 16:34:58,247 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-07-24 16:34:58,282 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-07-24 16:34:58,284 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-07-24 16:34:58,326 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-07-24 16:34:58,328 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-07-24 16:34:58,370 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-07-24 16:34:58,371 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-07-24 16:34:58,413 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-07-24 16:34:58,415 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-07-24 16:34:58,456 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-07-24 16:34:58,458 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-07-24 16:34:58,493 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-07-24 16:34:58,495 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-07-24 16:34:58,537 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-07-24 16:34:58,539 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-07-24 16:34:58,580 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-07-24 16:34:58,582 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-07-24 16:34:58,617 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-07-24 16:34:58,619 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-07-24 16:34:58,661 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-07-24 16:34:58,663 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-07-24 16:34:58,704 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-07-24 16:34:58,706 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-07-24 16:34:58,748 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-07-24 16:34:58,749 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-07-24 16:34:58,791 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-07-24 16:34:58,793 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-07-24 16:34:58,828 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-07-24 16:34:58,830 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-07-24 16:34:58,871 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-07-24 16:34:58,873 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-07-24 16:34:58,914 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-07-24 16:34:58,916 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-07-24 16:34:58,958 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-07-24 16:34:58,959 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-07-24 16:34:59,001 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-07-24 16:34:59,003 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-07-24 16:34:59,044 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-07-24 16:34:59,046 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-07-24 16:34:59,087 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-07-24 16:34:59,089 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-07-24 16:34:59,130 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-07-24 16:34:59,132 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-07-24 16:34:59,167 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-07-24 16:34:59,169 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-07-24 16:34:59,210 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-07-24 16:34:59,211 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-07-24 16:34:59,253 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-07-24 16:34:59,254 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-07-24 16:34:59,295 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-07-24 16:34:59,297 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-07-24 16:34:59,338 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-07-24 16:34:59,340 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-07-24 16:34:59,375 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-07-24 16:34:59,377 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-07-24 16:34:59,417 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-07-24 16:34:59,419 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-07-24 16:34:59,460 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-07-24 16:34:59,462 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-07-24 16:34:59,496 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-07-24 16:34:59,498 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-07-24 16:34:59,539 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-07-24 16:34:59,541 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-07-24 16:34:59,582 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-07-24 16:34:59,584 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-07-24 16:34:59,625 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-07-24 16:34:59,627 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-07-24 16:34:59,667 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-07-24 16:34:59,669 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-07-24 16:34:59,703 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-07-24 16:34:59,705 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-07-24 16:34:59,746 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-07-24 16:34:59,748 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-07-24 16:34:59,789 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-07-24 16:34:59,791 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-07-24 16:34:59,825 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-07-24 16:34:59,827 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-07-24 16:34:59,868 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-07-24 16:34:59,869 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-07-24 16:34:59,910 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-07-24 16:34:59,912 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-07-24 16:34:59,953 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-07-24 16:34:59,955 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-07-24 16:34:59,995 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-07-24 16:34:59,997 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-07-24 16:35:00,031 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-07-24 16:35:00,033 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-07-24 16:35:00,074 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-07-24 16:35:00,075 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-07-24 16:35:00,116 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-07-24 16:35:00,118 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=126
2025-07-24 16:35:00,152 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=126
2025-07-24 16:35:00,153 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=127
2025-07-24 16:35:00,194 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=127
2025-07-24 16:35:00,196 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=128
2025-07-24 16:35:00,236 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=128
2025-07-24 16:35:00,238 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=129
2025-07-24 16:35:00,279 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=129
2025-07-24 16:35:00,280 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=130
2025-07-24 16:35:00,321 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=130
2025-07-24 16:35:00,323 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=131
2025-07-24 16:35:00,356 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=131
2025-07-24 16:35:00,358 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=132
2025-07-24 16:35:00,398 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=132
2025-07-24 16:35:00,400 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=133
2025-07-24 16:35:00,440 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=133
2025-07-24 16:35:00,442 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=134
2025-07-24 16:35:00,476 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=134
2025-07-24 16:35:00,478 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=135
2025-07-24 16:35:00,518 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=135
2025-07-24 16:35:00,520 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=136
2025-07-24 16:35:00,560 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=136
2025-07-24 16:35:00,562 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=137
2025-07-24 16:35:00,603 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=137
2025-07-24 16:35:00,604 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=138
2025-07-24 16:35:00,645 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=138
2025-07-24 16:35:00,646 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=139
2025-07-24 16:35:00,680 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=139
2025-07-24 16:35:00,682 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=140
2025-07-24 16:35:00,722 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=140
2025-07-24 16:35:00,724 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=141
2025-07-24 16:35:00,764 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=141
2025-07-24 16:35:00,766 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=142
2025-07-24 16:35:00,799 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=142
2025-07-24 16:35:00,801 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=143
2025-07-24 16:35:00,841 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=143
2025-07-24 16:35:00,843 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=144
2025-07-24 16:35:00,883 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=144
2025-07-24 16:35:00,885 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=145
2025-07-24 16:35:00,925 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=145
2025-07-24 16:35:00,927 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=146
2025-07-24 16:35:00,967 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=146
2025-07-24 16:35:00,969 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=147
2025-07-24 16:35:01,002 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=147
2025-07-24 16:35:01,004 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=148
2025-07-24 16:35:01,044 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=148
2025-07-24 16:35:01,045 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=149
2025-07-24 16:35:01,085 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=149
2025-07-24 16:35:01,087 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=150
2025-07-24 16:35:01,120 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=150
2025-07-24 16:35:01,122 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_wo_WT_twovariables/CI_results_hierarchical_remap.nc
2025-07-24 16:35:11,106 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-07-24 16:35:11,108 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-07-24 16:35:11,109 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-07-24 16:35:11,111 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_wo_WT_twovariables/CI_results_hierarchical.nc
2025-07-24 16:35:11,112 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_wo_WT_twovariables/CI_results_hierarchical_remap.nc
2025-07-24 16:35:11,113 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 150 variables
2025-07-24 16:35:11,115 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125, k=126, k=127, k=128, k=129, k=130, k=131, k=132, k=133, k=134, k=135, k=136, k=137, k=138, k=139, k=140, k=141, k=142, k=143, k=144, k=145, k=146, k=147, k=148, k=149, k=150
2025-07-24 16:35:11,116 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-07-24 16:35:11,145 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_wo_WT_twovariables/CI_results_hierarchical_remap.nc
2025-07-24 16:35:11,146 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-07-24 16:35:11,148 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_1981_2020_AMS.nc
2025-07-24 16:35:11,151 - __main__ - ERROR - run_heterogeneity_analysis.py:604 - Error in heterogeneity analysis: [Errno 2] No such file or directory: '/lcrc/project/Hydro-model/sgev/GDO/test2/sample_data/PRISM_1981_2020_AMS.nc'
Traceback (most recent call last):
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/file_manager.py", line 211, in _acquire_with_cache_info
    file = self._cache[self._key]
           ~~~~~~~~~~~^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/lru_cache.py", line 56, in __getitem__
    value = self._cache[key]
            ~~~~~~~~~~~^^^^^
KeyError: [<class 'netCDF4._netCDF4.Dataset'>, ('/lcrc/project/Hydro-model/sgev/GDO/test2/sample_data/PRISM_1981_2020_AMS.nc',), 'r', (('clobber', True), ('diskless', False), ('format', 'NETCDF4'), ('persist', False)), '1d2179fc-a823-498c-9550-ea729d26a2c6']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/lcrc/project/Hydro-model/sgev/GDO/test2/src/models/run_heterogeneity_analysis.py", line 556, in main
    precip_data, cluster_data = load_data(args.precip_path, cluster_path_to_use)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/lcrc/project/Hydro-model/sgev/GDO/test2/src/models/run_heterogeneity_analysis.py", line 127, in load_data
    precip_data = xr.open_dataset(precip_path)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/api.py", line 687, in open_dataset
    backend_ds = backend.open_dataset(
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 666, in open_dataset
    store = NetCDF4DataStore.open(
            ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 452, in open
    return cls(manager, group=group, mode=mode, lock=lock, autoclose=autoclose)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 393, in __init__
    self.format = self.ds.data_model
                  ^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 461, in ds
    return self._acquire()
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 455, in _acquire
    with self._manager.acquire_context(needs_lock) as root:
  File "/home/<USER>/.conda/envs/fastclust/lib/python3.11/contextlib.py", line 137, in __enter__
    return next(self.gen)
           ^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/file_manager.py", line 199, in acquire_context
    file, cached = self._acquire_with_cache_info(needs_lock)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/file_manager.py", line 217, in _acquire_with_cache_info
    file = self._opener(*self._args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "src/netCDF4/_netCDF4.pyx", line 2521, in netCDF4._netCDF4.Dataset.__init__
  File "src/netCDF4/_netCDF4.pyx", line 2158, in netCDF4._netCDF4._ensure_nc_success
FileNotFoundError: [Errno 2] No such file or directory: '/lcrc/project/Hydro-model/sgev/GDO/test2/sample_data/PRISM_1981_2020_AMS.nc'
