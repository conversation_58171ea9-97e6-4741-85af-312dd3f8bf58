2025-08-01 13:05:52,143 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-08-01 13:05:52,143 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-01 13:05:52,143 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_10_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-01 13:05:52,143 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_10_hierarchical_twovariables
2025-08-01 13:05:52,143 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-08-01 13:05:52,143 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 50
2025-08-01 13:05:52,143 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-08-01 13:05:52,145 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-08-01 13:05:52,146 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_10_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-01 13:05:52,146 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-08-01 13:05:53,188 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-08-01 13:05:53,701 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-08-01 13:05:53,735 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-08-01 13:05:53,762 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-08-01 13:05:53,765 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-08-01 13:05:53,783 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-08-01 13:05:53,830 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-08-01 13:05:53,866 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-08-01 13:05:53,883 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-08-01 13:05:53,928 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-08-01 13:05:53,930 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-08-01 13:05:53,932 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-08-01 13:05:53,966 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-08-01 13:05:53,967 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-08-01 13:05:53,969 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-08-01 13:05:53,971 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-08-01 13:05:54,033 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-08-01 13:05:54,034 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-08-01 13:05:54,036 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-08-01 13:05:54,039 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-08-01 13:05:54,039 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-08-01 13:05:54,042 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-08-01 13:05:54,042 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-08-01 13:05:54,042 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-08-01 13:05:54,045 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-08-01 13:05:54,046 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-08-01 13:05:54,047 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-08-01 13:05:54,050 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-08-01 13:05:54,051 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-08-01 13:05:54,054 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-08-01 13:05:54,056 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-08-01 13:05:54,058 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-08-01 13:05:54,061 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-08-01 13:05:54,062 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-08-01 13:05:54,064 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-08-01 13:05:54,066 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-08-01 13:05:54,068 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-08-01 13:05:54,070 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-08-01 13:05:54,072 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-08-01 13:05:54,083 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-08-01 13:05:54,083 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-08-01 13:05:54,083 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-08-01 13:05:54,084 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-08-01 13:05:54,085 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-08-01 13:05:54,105 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-08-01 13:05:54,105 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-08-01 13:05:54,161 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-08-01 13:05:54,161 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-08-01 13:05:54,163 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-08-01 13:05:54,163 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-08-01 13:05:54,165 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-08-01 13:05:54,165 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-08-01 13:05:54,167 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-08-01 13:05:54,167 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-08-01 13:05:54,170 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-08-01 13:05:54,170 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-08-01 13:05:54,172 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-08-01 13:05:54,173 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-08-01 13:05:54,176 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-08-01 13:05:54,176 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-08-01 13:05:54,179 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-08-01 13:05:54,179 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-08-01 13:05:54,181 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-08-01 13:05:54,182 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-08-01 13:05:54,185 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-08-01 13:05:54,185 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-08-01 13:05:54,188 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-08-01 13:05:54,188 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-08-01 13:05:54,191 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-08-01 13:05:54,191 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-08-01 13:05:54,194 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-08-01 13:05:54,194 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-08-01 13:05:54,197 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-08-01 13:05:54,197 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-08-01 13:05:54,200 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-08-01 13:05:54,200 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-08-01 13:05:54,203 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-08-01 13:05:54,203 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-08-01 13:05:54,206 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-08-01 13:05:54,206 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-08-01 13:05:54,209 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-08-01 13:05:54,209 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-08-01 13:05:54,212 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-08-01 13:05:54,212 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-08-01 13:05:54,215 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-08-01 13:05:54,215 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-08-01 13:05:54,218 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-08-01 13:05:54,218 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-08-01 13:05:54,221 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-08-01 13:05:54,221 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-08-01 13:05:54,224 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-08-01 13:05:54,224 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-08-01 13:05:54,227 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-08-01 13:05:54,227 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-08-01 13:05:54,229 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-08-01 13:05:54,229 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-08-01 13:05:54,232 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-08-01 13:05:54,233 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-08-01 13:05:54,235 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-08-01 13:05:54,236 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-08-01 13:05:54,239 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-08-01 13:05:54,239 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-08-01 13:05:54,242 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-08-01 13:05:54,242 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-08-01 13:05:54,244 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-08-01 13:05:54,244 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-08-01 13:05:54,247 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-08-01 13:05:54,247 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-08-01 13:05:54,250 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-08-01 13:05:54,251 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-08-01 13:05:54,253 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-08-01 13:05:54,253 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-08-01 13:05:54,256 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-08-01 13:05:54,256 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-08-01 13:05:54,259 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-08-01 13:05:54,259 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-08-01 13:05:54,262 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-08-01 13:05:54,262 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-08-01 13:05:54,265 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-08-01 13:05:54,265 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-08-01 13:05:54,268 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-08-01 13:05:54,268 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-08-01 13:05:54,271 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-08-01 13:05:54,271 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-08-01 13:05:54,274 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-08-01 13:05:54,274 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-08-01 13:05:54,276 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-08-01 13:05:54,277 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-08-01 13:05:54,279 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-08-01 13:05:54,280 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-08-01 13:05:54,282 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-08-01 13:05:54,283 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-08-01 13:05:54,286 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-08-01 13:05:54,286 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-08-01 13:05:54,289 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-08-01 13:05:54,289 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-08-01 13:05:54,291 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-08-01 13:05:54,291 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-08-01 13:05:54,294 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-08-01 13:05:54,294 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-08-01 13:05:54,297 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-08-01 13:05:54,297 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-08-01 13:05:54,300 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-08-01 13:05:54,300 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-08-01 13:05:54,303 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-08-01 13:05:54,303 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-08-01 13:05:54,306 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-08-01 13:05:54,306 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-08-01 13:05:54,309 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-08-01 13:05:54,309 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-08-01 13:05:54,312 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-08-01 13:05:54,312 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-08-01 13:05:54,314 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-08-01 13:05:54,314 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-08-01 13:05:54,317 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-08-01 13:05:54,317 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-08-01 13:05:54,320 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-08-01 13:05:54,320 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-08-01 13:05:54,323 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-08-01 13:05:54,323 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-08-01 13:05:54,326 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-08-01 13:05:54,326 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-08-01 13:05:54,329 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-08-01 13:05:54,329 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-08-01 13:05:54,332 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-08-01 13:05:54,332 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-08-01 13:05:54,335 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-08-01 13:05:54,335 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-08-01 13:05:54,337 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-08-01 13:05:54,337 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-08-01 13:05:54,340 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-08-01 13:05:54,340 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-08-01 13:05:54,343 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-08-01 13:05:54,343 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-08-01 13:05:54,346 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-08-01 13:05:54,346 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-08-01 13:05:54,349 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-08-01 13:05:54,349 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-08-01 13:05:54,352 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-08-01 13:05:54,352 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-08-01 13:05:54,354 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-08-01 13:05:54,355 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-08-01 13:05:54,357 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-08-01 13:05:54,358 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-08-01 13:05:54,360 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-08-01 13:05:54,360 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-08-01 13:05:54,363 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-08-01 13:05:54,363 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-08-01 13:05:54,366 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-08-01 13:05:54,366 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-08-01 13:05:54,369 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-08-01 13:05:54,369 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-08-01 13:05:54,372 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-08-01 13:05:54,372 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-08-01 13:05:54,374 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-08-01 13:05:54,374 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-08-01 13:05:54,377 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-08-01 13:05:54,377 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-08-01 13:05:54,380 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-08-01 13:05:54,380 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-08-01 13:05:54,382 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-08-01 13:05:54,383 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-08-01 13:05:54,980 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-08-01 13:05:54,980 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-08-01 13:05:54,983 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-08-01 13:05:54,983 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-08-01 13:05:54,986 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-08-01 13:05:54,986 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-08-01 13:05:54,989 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-08-01 13:05:54,989 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-08-01 13:05:54,991 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-08-01 13:05:54,991 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-08-01 13:05:54,994 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-08-01 13:05:54,994 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-08-01 13:05:54,997 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-08-01 13:05:54,997 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-08-01 13:05:55,000 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-08-01 13:05:55,000 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-08-01 13:05:55,002 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-08-01 13:05:55,003 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-08-01 13:05:55,005 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-08-01 13:05:55,006 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-08-01 13:05:55,008 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-08-01 13:05:55,008 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-08-01 13:05:55,011 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-08-01 13:05:55,011 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-08-01 13:05:55,014 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-08-01 13:05:55,014 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-08-01 13:05:55,017 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-08-01 13:05:55,017 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-08-01 13:05:55,020 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-08-01 13:05:55,020 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-08-01 13:05:55,022 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-08-01 13:05:55,022 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-08-01 13:05:55,025 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-08-01 13:05:55,025 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-08-01 13:05:55,028 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-08-01 13:05:55,028 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-08-01 13:05:55,031 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-08-01 13:05:55,031 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-08-01 13:05:55,034 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-08-01 13:05:55,034 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-08-01 13:05:55,036 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-08-01 13:05:55,036 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-08-01 13:05:55,039 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-08-01 13:05:55,039 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-08-01 13:05:55,042 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-08-01 13:05:55,042 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-08-01 13:05:55,044 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-08-01 13:05:55,044 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-08-01 13:05:55,047 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-08-01 13:05:55,047 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-08-01 13:05:55,050 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-08-01 13:05:55,050 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-08-01 13:05:55,053 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-08-01 13:05:55,053 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-08-01 13:05:55,056 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-08-01 13:05:55,056 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-08-01 13:05:55,058 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-08-01 13:05:55,058 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-08-01 13:05:55,061 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-08-01 13:05:55,061 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-08-01 13:05:55,064 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-08-01 13:05:55,064 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-08-01 13:05:55,066 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-08-01 13:05:55,067 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-08-01 13:05:55,069 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-08-01 13:05:55,069 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-08-01 13:05:55,072 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-08-01 13:05:55,072 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-08-01 13:05:55,075 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-08-01 13:05:55,075 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-08-01 13:05:55,078 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-08-01 13:05:55,078 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-08-01 13:05:55,080 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-08-01 13:05:55,081 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-08-01 13:05:55,083 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-08-01 13:05:55,083 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-08-01 13:05:55,086 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-08-01 13:05:55,086 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-08-01 13:05:55,088 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-08-01 13:05:55,089 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-08-01 13:05:55,091 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-08-01 13:05:55,091 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-08-01 13:05:55,094 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-08-01 13:05:55,094 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-08-01 13:05:55,097 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-08-01 13:05:55,097 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-08-01 13:05:55,100 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-08-01 13:05:55,100 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-08-01 13:05:55,102 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-08-01 13:05:55,102 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-08-01 13:05:55,105 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-08-01 13:05:55,105 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_10_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-01 13:05:56,410 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-08-01 13:05:56,410 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-08-01 13:05:56,411 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-08-01 13:05:56,411 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_10_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-01 13:05:56,411 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_10_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-01 13:05:56,411 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 125 variables
2025-08-01 13:05:56,411 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125
2025-08-01 13:05:56,411 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-08-01 13:05:56,414 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_10_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-01 13:05:56,414 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-08-01 13:05:56,414 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-01 13:05:56,512 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-08-01 13:06:02,536 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_10_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-01 13:06:02,942 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-08-01 13:06:02,942 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-08-01 13:06:02,942 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-08-01 13:06:02,944 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-08-01 13:06:03,001 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17156 sites
2025-08-01 13:07:14,899 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=125.198, H2=2.726, H3=-0.804
2025-08-01 13:07:14,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 35203 sites
2025-08-01 13:09:42,025 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=78.678, H2=22.478, H3=8.107
2025-08-01 13:09:42,053 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-08-01 13:09:42,053 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-08-01 13:09:42,055 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-08-01 13:09:42,111 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17156 sites
2025-08-01 13:10:53,995 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=125.198, H2=2.726, H3=-0.804
2025-08-01 13:10:54,056 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 19143 sites
2025-08-01 13:12:14,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=47.244, H2=12.725, H3=3.660
2025-08-01 13:12:14,149 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 16060 sites
2025-08-01 13:13:21,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=79.068, H2=17.432, H3=6.307
2025-08-01 13:13:21,164 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-08-01 13:13:21,164 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-08-01 13:13:21,166 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-08-01 13:13:21,223 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17156 sites
2025-08-01 13:14:32,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=125.198, H2=2.726, H3=-0.804
2025-08-01 13:14:33,017 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 19143 sites
2025-08-01 13:15:53,203 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=47.244, H2=12.725, H3=3.660
2025-08-01 13:15:53,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7870 sites
2025-08-01 13:16:26,073 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=26.812, H2=7.588, H3=-0.872
2025-08-01 13:16:26,113 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8190 sites
2025-08-01 13:17:00,327 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=52.022, H2=16.827, H3=10.750
2025-08-01 13:17:00,353 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-08-01 13:17:00,353 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-08-01 13:17:00,355 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-08-01 13:17:00,412 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17156 sites
2025-08-01 13:18:12,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=125.198, H2=2.726, H3=-0.804
2025-08-01 13:18:12,366 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 7659 sites
2025-08-01 13:18:44,395 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.923, H2=4.583, H3=3.590
2025-08-01 13:18:44,441 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 11484 sites
2025-08-01 13:19:32,858 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.951, H2=15.408, H3=1.505
2025-08-01 13:19:32,898 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7870 sites
2025-08-01 13:20:06,320 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=24.856, H2=8.015, H3=-1.286
2025-08-01 13:20:06,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8190 sites
2025-08-01 13:20:40,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=60.793, H2=15.702, H3=10.610
2025-08-01 13:20:41,021 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-08-01 13:20:41,021 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-08-01 13:20:41,023 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-08-01 13:20:41,079 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17156 sites
2025-08-01 13:21:53,680 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=125.198, H2=2.726, H3=-0.804
2025-08-01 13:21:53,719 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 7659 sites
2025-08-01 13:22:26,313 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.923, H2=4.583, H3=3.590
2025-08-01 13:22:26,360 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 11484 sites
2025-08-01 13:23:14,669 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.951, H2=15.408, H3=1.505
2025-08-01 13:23:14,708 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7870 sites
2025-08-01 13:23:47,894 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=24.856, H2=8.015, H3=-1.286
2025-08-01 13:23:47,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6921 sites
2025-08-01 13:24:17,141 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=41.950, H2=14.359, H3=10.458
2025-08-01 13:24:17,169 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1269 sites
2025-08-01 13:24:22,529 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.051, H2=-0.340, H3=-0.406
2025-08-01 13:24:22,555 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-08-01 13:24:22,555 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-08-01 13:24:22,556 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-08-01 13:24:22,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5450 sites
2025-08-01 13:24:45,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=55.114, H2=0.040, H3=-1.093
2025-08-01 13:24:45,828 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11706 sites
2025-08-01 13:25:35,035 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=85.579, H2=2.637, H3=-0.559
2025-08-01 13:25:35,075 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7659 sites
2025-08-01 13:26:07,414 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.653, H2=5.744, H3=4.369
2025-08-01 13:26:07,461 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 11484 sites
2025-08-01 13:26:55,758 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=40.747, H2=16.142, H3=1.457
2025-08-01 13:26:55,798 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7870 sites
2025-08-01 13:27:28,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=29.909, H2=7.261, H3=-1.019
2025-08-01 13:27:28,703 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6921 sites
2025-08-01 13:27:57,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=31.765, H2=12.370, H3=9.434
2025-08-01 13:27:57,618 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1269 sites
2025-08-01 13:28:02,927 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=9.866, H2=-0.348, H3=-0.525
2025-08-01 13:28:02,952 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-08-01 13:28:02,953 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-08-01 13:28:02,954 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-08-01 13:28:02,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5450 sites
2025-08-01 13:28:25,854 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=55.114, H2=0.040, H3=-1.093
2025-08-01 13:28:25,890 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6026 sites
2025-08-01 13:28:51,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.447, H2=2.125, H3=-0.951
2025-08-01 13:28:51,152 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5680 sites
2025-08-01 13:29:14,808 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=75.726, H2=-0.956, H3=-0.058
2025-08-01 13:29:14,847 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7659 sites
2025-08-01 13:29:46,853 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.702, H2=5.065, H3=4.050
2025-08-01 13:29:46,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 11484 sites
2025-08-01 13:30:35,015 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=43.956, H2=12.020, H3=1.377
2025-08-01 13:30:35,055 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 7870 sites
2025-08-01 13:31:07,998 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=22.695, H2=5.955, H3=-0.971
2025-08-01 13:31:08,036 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6921 sites
2025-08-01 13:31:37,123 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=32.894, H2=15.124, H3=10.492
2025-08-01 13:31:37,151 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1269 sites
2025-08-01 13:31:42,498 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.245, H2=-0.353, H3=-0.616
2025-08-01 13:31:42,523 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-08-01 13:31:42,523 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-08-01 13:31:42,525 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-08-01 13:31:42,561 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5450 sites
2025-08-01 13:32:05,376 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=55.114, H2=0.040, H3=-1.093
2025-08-01 13:32:05,412 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6026 sites
2025-08-01 13:32:30,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.447, H2=2.125, H3=-0.951
2025-08-01 13:32:30,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5680 sites
2025-08-01 13:32:54,344 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=75.726, H2=-0.956, H3=-0.058
2025-08-01 13:32:54,384 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7659 sites
2025-08-01 13:33:26,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.702, H2=5.065, H3=4.050
2025-08-01 13:33:26,482 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 11484 sites
2025-08-01 13:34:14,581 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=43.956, H2=12.020, H3=1.377
2025-08-01 13:34:14,612 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2858 sites
2025-08-01 13:34:26,536 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.722, H2=0.940, H3=1.437
2025-08-01 13:34:26,570 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 5012 sites
2025-08-01 13:34:47,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=15.781, H2=3.927, H3=-2.849
2025-08-01 13:34:47,621 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6921 sites
2025-08-01 13:35:16,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=32.937, H2=13.363, H3=10.174
2025-08-01 13:35:16,656 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1269 sites
2025-08-01 13:35:21,973 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.283, H2=-0.744, H3=-0.703
2025-08-01 13:35:21,998 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-08-01 13:35:21,998 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-08-01 13:35:22,000 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-08-01 13:35:22,035 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5450 sites
2025-08-01 13:35:44,888 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=55.114, H2=0.040, H3=-1.093
2025-08-01 13:35:44,924 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6026 sites
2025-08-01 13:36:10,112 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.447, H2=2.125, H3=-0.951
2025-08-01 13:36:10,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5680 sites
2025-08-01 13:36:33,951 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=75.726, H2=-0.956, H3=-0.058
2025-08-01 13:36:33,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7659 sites
2025-08-01 13:37:06,078 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.702, H2=5.065, H3=4.050
2025-08-01 13:37:06,125 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 11484 sites
2025-08-01 13:37:54,036 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=43.956, H2=12.020, H3=1.377
2025-08-01 13:37:54,068 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2858 sites
2025-08-01 13:38:06,022 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.722, H2=0.940, H3=1.437
2025-08-01 13:38:06,050 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1634 sites
2025-08-01 13:38:12,901 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.022, H2=2.894, H3=0.382
2025-08-01 13:38:12,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3378 sites
2025-08-01 13:38:26,970 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=12.993, H2=0.077, H3=-5.430
2025-08-01 13:38:27,008 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6921 sites
2025-08-01 13:38:56,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=32.045, H2=13.335, H3=10.388
2025-08-01 13:38:56,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1269 sites
2025-08-01 13:39:01,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.384, H2=-0.376, H3=-0.575
2025-08-01 13:39:01,466 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-08-01 13:39:01,466 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-08-01 13:39:01,467 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-08-01 13:39:01,504 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5450 sites
2025-08-01 13:39:24,341 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=55.114, H2=0.040, H3=-1.093
2025-08-01 13:39:24,378 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6026 sites
2025-08-01 13:39:49,567 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.447, H2=2.125, H3=-0.951
2025-08-01 13:39:49,613 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5680 sites
2025-08-01 13:40:13,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=75.726, H2=-0.956, H3=-0.058
2025-08-01 13:40:13,372 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7659 sites
2025-08-01 13:40:45,186 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.702, H2=5.065, H3=4.050
2025-08-01 13:40:45,223 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6402 sites
2025-08-01 13:41:11,927 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=44.766, H2=15.621, H3=2.429
2025-08-01 13:41:11,962 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 5082 sites
2025-08-01 13:41:33,117 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.793, H2=1.919, H3=-1.283
2025-08-01 13:41:33,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2858 sites
2025-08-01 13:41:45,069 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.210, H2=0.999, H3=1.404
2025-08-01 13:41:45,097 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1634 sites
2025-08-01 13:41:51,916 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.818, H2=2.849, H3=0.113
2025-08-01 13:41:51,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3378 sites
2025-08-01 13:42:05,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.033, H2=-0.147, H3=-4.911
2025-08-01 13:42:06,014 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6921 sites
2025-08-01 13:42:34,755 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=39.637, H2=14.486, H3=8.842
2025-08-01 13:42:34,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1269 sites
2025-08-01 13:42:40,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=7.631, H2=-0.468, H3=-0.371
2025-08-01 13:42:40,082 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-08-01 13:42:40,082 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-08-01 13:42:40,084 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-08-01 13:42:40,113 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 13:42:47,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 13:42:47,586 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 13:43:02,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 13:43:02,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6026 sites
2025-08-01 13:43:28,158 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.285, H2=2.476, H3=-1.019
2025-08-01 13:43:28,194 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5680 sites
2025-08-01 13:43:51,841 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=66.202, H2=-1.164, H3=-0.282
2025-08-01 13:43:51,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7659 sites
2025-08-01 13:44:23,731 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.559, H2=5.023, H3=3.967
2025-08-01 13:44:23,768 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6402 sites
2025-08-01 13:44:50,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=41.915, H2=14.462, H3=2.478
2025-08-01 13:44:50,538 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 5082 sites
2025-08-01 13:45:11,962 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.006, H2=2.432, H3=-1.237
2025-08-01 13:45:11,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2858 sites
2025-08-01 13:45:24,077 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=6.471, H2=0.859, H3=1.396
2025-08-01 13:45:24,105 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1634 sites
2025-08-01 13:45:30,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=7.123, H2=2.801, H3=0.016
2025-08-01 13:45:31,023 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3378 sites
2025-08-01 13:45:45,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=14.379, H2=0.008, H3=-4.611
2025-08-01 13:45:45,295 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6921 sites
2025-08-01 13:46:14,799 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=39.249, H2=13.746, H3=11.371
2025-08-01 13:46:14,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1269 sites
2025-08-01 13:46:20,137 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=10.394, H2=-0.310, H3=-0.508
2025-08-01 13:46:20,163 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-08-01 13:46:20,163 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-08-01 13:46:20,165 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-08-01 13:46:20,193 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 13:46:27,692 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 13:46:27,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 13:46:43,196 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 13:46:43,233 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6026 sites
2025-08-01 13:47:08,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.285, H2=2.476, H3=-1.019
2025-08-01 13:47:08,550 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5680 sites
2025-08-01 13:47:32,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=66.202, H2=-1.164, H3=-0.282
2025-08-01 13:47:32,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7659 sites
2025-08-01 13:48:04,495 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.559, H2=5.023, H3=3.967
2025-08-01 13:48:04,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6402 sites
2025-08-01 13:48:31,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=41.915, H2=14.462, H3=2.478
2025-08-01 13:48:31,501 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 5082 sites
2025-08-01 13:48:52,837 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.006, H2=2.432, H3=-1.237
2025-08-01 13:48:52,868 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2858 sites
2025-08-01 13:49:04,815 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=6.471, H2=0.859, H3=1.396
2025-08-01 13:49:04,844 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1634 sites
2025-08-01 13:49:11,699 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=7.123, H2=2.801, H3=0.016
2025-08-01 13:49:11,726 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 854 sites
2025-08-01 13:49:15,319 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-7.561, H2=-6.538, H3=-7.468
2025-08-01 13:49:15,349 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2524 sites
2025-08-01 13:49:25,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=14.827, H2=1.368, H3=-2.032
2025-08-01 13:49:25,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6921 sites
2025-08-01 13:49:55,041 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=39.742, H2=17.585, H3=10.589
2025-08-01 13:49:55,069 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1269 sites
2025-08-01 13:50:00,390 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.748, H2=-0.593, H3=-0.601
2025-08-01 13:50:00,416 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-08-01 13:50:00,416 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-08-01 13:50:00,418 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-08-01 13:50:00,447 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 13:50:07,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 13:50:07,942 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 13:50:23,301 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 13:50:23,337 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6026 sites
2025-08-01 13:50:48,542 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.285, H2=2.476, H3=-1.019
2025-08-01 13:50:48,578 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5680 sites
2025-08-01 13:51:12,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=66.202, H2=-1.164, H3=-0.282
2025-08-01 13:51:12,318 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7659 sites
2025-08-01 13:51:44,291 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.559, H2=5.023, H3=3.967
2025-08-01 13:51:44,328 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6402 sites
2025-08-01 13:52:11,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=41.915, H2=14.462, H3=2.478
2025-08-01 13:52:11,151 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 5082 sites
2025-08-01 13:52:32,426 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.006, H2=2.432, H3=-1.237
2025-08-01 13:52:32,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2858 sites
2025-08-01 13:52:44,349 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=6.471, H2=0.859, H3=1.396
2025-08-01 13:52:44,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1634 sites
2025-08-01 13:52:51,221 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=7.123, H2=2.801, H3=0.016
2025-08-01 13:52:51,248 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 854 sites
2025-08-01 13:52:54,795 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-7.561, H2=-6.538, H3=-7.468
2025-08-01 13:52:54,836 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2524 sites
2025-08-01 13:53:05,389 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=14.827, H2=1.368, H3=-2.032
2025-08-01 13:53:05,427 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6921 sites
2025-08-01 13:53:34,455 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=39.742, H2=17.585, H3=10.589
2025-08-01 13:53:34,482 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 265 sites
2025-08-01 13:53:35,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=4.471, H2=-6.077, H3=1.552
2025-08-01 13:53:35,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1004 sites
2025-08-01 13:53:39,815 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=4.968, H2=2.112, H3=-1.460
2025-08-01 13:53:39,840 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-08-01 13:53:39,840 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-08-01 13:53:39,842 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-08-01 13:53:39,872 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 13:53:47,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 13:53:47,366 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 13:54:02,743 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 13:54:02,779 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6026 sites
2025-08-01 13:54:28,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.285, H2=2.476, H3=-1.019
2025-08-01 13:54:28,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5680 sites
2025-08-01 13:54:51,810 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=66.202, H2=-1.164, H3=-0.282
2025-08-01 13:54:51,849 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7659 sites
2025-08-01 13:55:23,829 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.559, H2=5.023, H3=3.967
2025-08-01 13:55:23,866 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6402 sites
2025-08-01 13:55:50,529 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=41.915, H2=14.462, H3=2.478
2025-08-01 13:55:50,558 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2281 sites
2025-08-01 13:56:00,053 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=9.411, H2=-1.809, H3=-7.337
2025-08-01 13:56:00,083 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2801 sites
2025-08-01 13:56:11,754 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.362, H2=4.070, H3=2.952
2025-08-01 13:56:11,784 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2858 sites
2025-08-01 13:56:23,668 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.822, H2=0.744, H3=1.316
2025-08-01 13:56:23,696 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1634 sites
2025-08-01 13:56:30,480 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.773, H2=3.130, H3=-0.015
2025-08-01 13:56:30,506 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 854 sites
2025-08-01 13:56:34,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-6.464, H2=-5.074, H3=-5.748
2025-08-01 13:56:34,110 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2524 sites
2025-08-01 13:56:44,659 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=22.197, H2=1.631, H3=-1.677
2025-08-01 13:56:44,697 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 6921 sites
2025-08-01 13:57:13,714 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=32.248, H2=13.513, H3=9.489
2025-08-01 13:57:13,740 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 265 sites
2025-08-01 13:57:14,856 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.070, H2=-5.326, H3=1.590
2025-08-01 13:57:14,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1004 sites
2025-08-01 13:57:19,099 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=5.586, H2=1.921, H3=-1.821
2025-08-01 13:57:19,124 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-08-01 13:57:19,124 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-08-01 13:57:19,126 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-08-01 13:57:19,155 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 13:57:26,613 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 13:57:26,645 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 13:57:42,034 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 13:57:42,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6026 sites
2025-08-01 13:58:07,272 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.285, H2=2.476, H3=-1.019
2025-08-01 13:58:07,308 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5680 sites
2025-08-01 13:58:31,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=66.202, H2=-1.164, H3=-0.282
2025-08-01 13:58:31,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4719 sites
2025-08-01 13:58:50,866 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=9.449, H2=4.728, H3=2.705
2025-08-01 13:58:50,897 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2940 sites
2025-08-01 13:59:03,204 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=1.475, H2=0.645, H3=2.179
2025-08-01 13:59:03,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6402 sites
2025-08-01 13:59:30,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=38.108, H2=16.519, H3=2.407
2025-08-01 13:59:30,130 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2281 sites
2025-08-01 13:59:39,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=10.405, H2=-2.075, H3=-8.437
2025-08-01 13:59:39,766 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2801 sites
2025-08-01 13:59:51,453 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=8.018, H2=4.548, H3=3.160
2025-08-01 13:59:51,483 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2858 sites
2025-08-01 14:00:03,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=7.381, H2=0.859, H3=1.096
2025-08-01 14:00:03,504 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1634 sites
2025-08-01 14:00:10,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.706, H2=3.331, H3=0.396
2025-08-01 14:00:10,434 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 854 sites
2025-08-01 14:00:14,065 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-10.183, H2=-6.223, H3=-7.448
2025-08-01 14:00:14,096 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2524 sites
2025-08-01 14:00:24,733 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=18.079, H2=1.236, H3=-2.145
2025-08-01 14:00:24,770 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 6921 sites
2025-08-01 14:00:54,114 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=33.378, H2=14.742, H3=9.731
2025-08-01 14:00:54,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 265 sites
2025-08-01 14:00:55,265 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=5.527, H2=-5.433, H3=1.664
2025-08-01 14:00:55,292 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1004 sites
2025-08-01 14:00:59,531 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=5.067, H2=2.749, H3=-1.954
2025-08-01 14:00:59,556 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-08-01 14:00:59,556 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-08-01 14:00:59,558 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-08-01 14:00:59,586 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:01:07,087 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:01:07,120 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 14:01:22,559 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 14:01:22,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6026 sites
2025-08-01 14:01:47,897 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.285, H2=2.476, H3=-1.019
2025-08-01 14:01:47,929 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3197 sites
2025-08-01 14:02:01,297 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=28.270, H2=-1.339, H3=-0.326
2025-08-01 14:02:01,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2483 sites
2025-08-01 14:02:11,719 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=31.853, H2=-2.305, H3=-2.158
2025-08-01 14:02:11,752 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4719 sites
2025-08-01 14:02:31,471 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.190, H2=4.759, H3=2.687
2025-08-01 14:02:31,503 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2940 sites
2025-08-01 14:02:43,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=1.364, H2=0.715, H3=2.198
2025-08-01 14:02:43,772 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6402 sites
2025-08-01 14:03:10,409 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=46.205, H2=15.085, H3=2.347
2025-08-01 14:03:10,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2281 sites
2025-08-01 14:03:19,972 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.786, H2=-2.789, H3=-7.191
2025-08-01 14:03:20,003 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2801 sites
2025-08-01 14:03:31,657 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=8.965, H2=4.465, H3=2.876
2025-08-01 14:03:31,688 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2858 sites
2025-08-01 14:03:43,610 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.322, H2=1.025, H3=1.526
2025-08-01 14:03:43,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1634 sites
2025-08-01 14:03:50,460 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.771, H2=3.467, H3=0.266
2025-08-01 14:03:50,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 854 sites
2025-08-01 14:03:54,078 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-7.996, H2=-6.900, H3=-7.289
2025-08-01 14:03:54,108 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2524 sites
2025-08-01 14:04:04,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=17.304, H2=0.947, H3=-2.123
2025-08-01 14:04:04,724 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 6921 sites
2025-08-01 14:04:33,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=37.875, H2=12.978, H3=8.773
2025-08-01 14:04:33,619 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 265 sites
2025-08-01 14:04:34,723 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=5.794, H2=-6.495, H3=1.393
2025-08-01 14:04:34,749 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1004 sites
2025-08-01 14:04:38,920 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=4.659, H2=2.235, H3=-1.910
2025-08-01 14:04:38,945 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-08-01 14:04:38,945 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-08-01 14:04:38,947 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-08-01 14:04:38,975 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:04:46,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:04:46,433 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 14:05:01,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 14:05:01,830 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6026 sites
2025-08-01 14:05:27,169 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=32.285, H2=2.476, H3=-1.019
2025-08-01 14:05:27,201 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3197 sites
2025-08-01 14:05:40,672 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=28.270, H2=-1.339, H3=-0.326
2025-08-01 14:05:40,702 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2483 sites
2025-08-01 14:05:51,195 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=31.853, H2=-2.305, H3=-2.158
2025-08-01 14:05:51,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4719 sites
2025-08-01 14:06:10,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.190, H2=4.759, H3=2.687
2025-08-01 14:06:10,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2940 sites
2025-08-01 14:06:23,288 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=1.364, H2=0.715, H3=2.198
2025-08-01 14:06:23,325 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6402 sites
2025-08-01 14:06:50,120 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=46.205, H2=15.085, H3=2.347
2025-08-01 14:06:50,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2281 sites
2025-08-01 14:06:59,698 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.786, H2=-2.789, H3=-7.191
2025-08-01 14:06:59,728 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2801 sites
2025-08-01 14:07:11,455 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=8.965, H2=4.465, H3=2.876
2025-08-01 14:07:11,486 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2858 sites
2025-08-01 14:07:23,416 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.322, H2=1.025, H3=1.526
2025-08-01 14:07:23,444 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1634 sites
2025-08-01 14:07:30,284 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.771, H2=3.467, H3=0.266
2025-08-01 14:07:30,311 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 854 sites
2025-08-01 14:07:33,894 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-7.996, H2=-6.900, H3=-7.289
2025-08-01 14:07:33,924 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2524 sites
2025-08-01 14:07:44,508 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=17.304, H2=0.947, H3=-2.123
2025-08-01 14:07:44,538 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2969 sites
2025-08-01 14:07:57,061 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.537, H2=4.412, H3=4.725
2025-08-01 14:07:57,094 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3952 sites
2025-08-01 14:08:13,689 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=32.746, H2=13.849, H3=8.625
2025-08-01 14:08:13,715 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 265 sites
2025-08-01 14:08:14,826 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=5.665, H2=-5.482, H3=1.551
2025-08-01 14:08:14,853 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1004 sites
2025-08-01 14:08:19,089 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=5.060, H2=2.201, H3=-1.391
2025-08-01 14:08:19,114 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-08-01 14:08:19,114 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-08-01 14:08:19,116 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-08-01 14:08:19,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:08:26,569 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:08:26,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 14:08:42,022 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 14:08:42,053 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2814 sites
2025-08-01 14:08:53,835 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=15.633, H2=2.824, H3=-2.886
2025-08-01 14:08:53,866 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3212 sites
2025-08-01 14:09:07,256 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=25.574, H2=-0.434, H3=1.096
2025-08-01 14:09:07,287 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3197 sites
2025-08-01 14:09:20,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=32.836, H2=-1.195, H3=-0.225
2025-08-01 14:09:20,665 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2483 sites
2025-08-01 14:09:30,978 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=26.126, H2=-1.761, H3=-1.678
2025-08-01 14:09:31,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4719 sites
2025-08-01 14:09:50,624 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.406, H2=5.679, H3=2.838
2025-08-01 14:09:50,655 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2940 sites
2025-08-01 14:10:02,906 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=1.660, H2=0.660, H3=2.159
2025-08-01 14:10:02,944 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6402 sites
2025-08-01 14:10:29,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=39.434, H2=15.721, H3=2.099
2025-08-01 14:10:29,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2281 sites
2025-08-01 14:10:39,182 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.133, H2=-2.252, H3=-6.429
2025-08-01 14:10:39,212 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2801 sites
2025-08-01 14:10:50,844 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.649, H2=4.523, H3=3.110
2025-08-01 14:10:50,875 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2858 sites
2025-08-01 14:11:02,779 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.225, H2=1.346, H3=1.944
2025-08-01 14:11:02,807 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1634 sites
2025-08-01 14:11:09,648 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=4.906, H2=2.997, H3=-0.020
2025-08-01 14:11:09,675 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 854 sites
2025-08-01 14:11:13,246 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-8.534, H2=-6.794, H3=-6.759
2025-08-01 14:11:13,276 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2524 sites
2025-08-01 14:11:23,823 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=17.870, H2=1.491, H3=-1.800
2025-08-01 14:11:23,853 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2969 sites
2025-08-01 14:11:36,171 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=11.602, H2=4.640, H3=4.482
2025-08-01 14:11:36,203 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3952 sites
2025-08-01 14:11:52,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=35.396, H2=13.188, H3=9.330
2025-08-01 14:11:52,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 265 sites
2025-08-01 14:11:53,699 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=5.393, H2=-5.601, H3=1.608
2025-08-01 14:11:53,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1004 sites
2025-08-01 14:11:57,944 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=4.800, H2=2.305, H3=-2.156
2025-08-01 14:11:57,969 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-08-01 14:11:57,969 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-08-01 14:11:57,970 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-08-01 14:11:57,999 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:12:05,507 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:12:05,539 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3675 sites
2025-08-01 14:12:20,921 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.065, H2=-4.374, H3=0.471
2025-08-01 14:12:20,951 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2814 sites
2025-08-01 14:12:32,737 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=15.633, H2=2.824, H3=-2.886
2025-08-01 14:12:32,767 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3212 sites
2025-08-01 14:12:46,270 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=25.574, H2=-0.434, H3=1.096
2025-08-01 14:12:46,301 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3197 sites
2025-08-01 14:12:59,645 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=32.836, H2=-1.195, H3=-0.225
2025-08-01 14:12:59,675 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2483 sites
2025-08-01 14:13:10,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=26.126, H2=-1.761, H3=-1.678
2025-08-01 14:13:10,132 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4719 sites
2025-08-01 14:13:29,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.406, H2=5.679, H3=2.838
2025-08-01 14:13:29,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2940 sites
2025-08-01 14:13:42,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=1.660, H2=0.660, H3=2.159
2025-08-01 14:13:42,311 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6402 sites
2025-08-01 14:14:09,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=39.434, H2=15.721, H3=2.099
2025-08-01 14:14:09,109 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2281 sites
2025-08-01 14:14:18,694 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.133, H2=-2.252, H3=-6.429
2025-08-01 14:14:18,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2801 sites
2025-08-01 14:14:30,382 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.649, H2=4.523, H3=3.110
2025-08-01 14:14:30,412 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2858 sites
2025-08-01 14:14:42,328 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.225, H2=1.346, H3=1.944
2025-08-01 14:14:42,357 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1634 sites
2025-08-01 14:14:49,145 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=4.906, H2=2.997, H3=-0.020
2025-08-01 14:14:49,171 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 854 sites
2025-08-01 14:14:52,724 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-8.534, H2=-6.794, H3=-6.759
2025-08-01 14:14:52,753 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2524 sites
2025-08-01 14:15:03,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=17.870, H2=1.491, H3=-1.800
2025-08-01 14:15:03,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2969 sites
2025-08-01 14:15:15,698 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=11.602, H2=4.640, H3=4.482
2025-08-01 14:15:15,730 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3952 sites
2025-08-01 14:15:32,224 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=35.396, H2=13.188, H3=9.330
2025-08-01 14:15:32,250 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 265 sites
2025-08-01 14:15:33,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=5.393, H2=-5.601, H3=1.608
2025-08-01 14:15:33,381 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 384 sites
2025-08-01 14:15:34,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=2.796, H2=0.899, H3=-3.102
2025-08-01 14:15:35,005 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 620 sites
2025-08-01 14:15:37,595 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=3.063, H2=-0.705, H3=-0.534
2025-08-01 14:15:37,620 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-08-01 14:15:37,620 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-08-01 14:15:37,622 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-08-01 14:15:37,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:15:45,136 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:15:45,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1112 sites
2025-08-01 14:15:49,813 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=12.511, H2=-1.205, H3=0.708
2025-08-01 14:15:49,843 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2563 sites
2025-08-01 14:16:00,549 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.644, H2=-5.023, H3=-0.066
2025-08-01 14:16:00,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2814 sites
2025-08-01 14:16:12,304 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.621, H2=2.666, H3=-3.074
2025-08-01 14:16:12,335 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3212 sites
2025-08-01 14:16:25,771 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=28.446, H2=-0.548, H3=1.059
2025-08-01 14:16:25,802 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3197 sites
2025-08-01 14:16:39,142 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=26.660, H2=-0.757, H3=-0.203
2025-08-01 14:16:39,172 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2483 sites
2025-08-01 14:16:49,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=24.932, H2=-1.609, H3=-1.998
2025-08-01 14:16:49,611 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4719 sites
2025-08-01 14:17:09,323 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.416, H2=5.498, H3=2.673
2025-08-01 14:17:09,354 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2940 sites
2025-08-01 14:17:21,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=1.754, H2=0.529, H3=2.392
2025-08-01 14:17:21,671 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6402 sites
2025-08-01 14:17:48,452 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=44.755, H2=16.739, H3=1.832
2025-08-01 14:17:48,482 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2281 sites
2025-08-01 14:17:58,009 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.601, H2=-2.505, H3=-6.815
2025-08-01 14:17:58,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2801 sites
2025-08-01 14:18:09,804 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.804, H2=4.430, H3=3.187
2025-08-01 14:18:09,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2858 sites
2025-08-01 14:18:21,834 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.150, H2=0.978, H3=1.054
2025-08-01 14:18:21,863 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1634 sites
2025-08-01 14:18:28,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.904, H2=3.275, H3=0.289
2025-08-01 14:18:28,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 854 sites
2025-08-01 14:18:32,362 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-7.519, H2=-5.922, H3=-7.659
2025-08-01 14:18:32,392 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2524 sites
2025-08-01 14:18:43,034 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=18.172, H2=1.456, H3=-2.030
2025-08-01 14:18:43,064 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2969 sites
2025-08-01 14:18:55,535 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=15.046, H2=4.174, H3=5.168
2025-08-01 14:18:55,566 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3952 sites
2025-08-01 14:19:12,141 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=35.496, H2=14.984, H3=10.013
2025-08-01 14:19:12,166 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 265 sites
2025-08-01 14:19:13,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=5.872, H2=-5.237, H3=1.496
2025-08-01 14:19:13,305 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 384 sites
2025-08-01 14:19:14,918 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=2.500, H2=0.816, H3=-3.215
2025-08-01 14:19:14,945 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 620 sites
2025-08-01 14:19:17,531 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=4.593, H2=-0.892, H3=-0.588
2025-08-01 14:19:17,557 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-08-01 14:19:17,557 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-08-01 14:19:17,558 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-08-01 14:19:17,611 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:19:25,041 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:19:25,069 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1112 sites
2025-08-01 14:19:29,699 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=12.511, H2=-1.205, H3=0.708
2025-08-01 14:19:29,729 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2563 sites
2025-08-01 14:19:40,381 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.644, H2=-5.023, H3=-0.066
2025-08-01 14:19:40,412 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2814 sites
2025-08-01 14:19:52,085 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.621, H2=2.666, H3=-3.074
2025-08-01 14:19:52,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3212 sites
2025-08-01 14:20:05,460 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=28.446, H2=-0.548, H3=1.059
2025-08-01 14:20:05,492 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3197 sites
2025-08-01 14:20:18,767 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=26.660, H2=-0.757, H3=-0.203
2025-08-01 14:20:18,796 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2483 sites
2025-08-01 14:20:29,102 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=24.932, H2=-1.609, H3=-1.998
2025-08-01 14:20:29,136 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4719 sites
2025-08-01 14:20:48,747 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.416, H2=5.498, H3=2.673
2025-08-01 14:20:48,778 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2940 sites
2025-08-01 14:21:01,021 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=1.754, H2=0.529, H3=2.392
2025-08-01 14:21:01,058 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6402 sites
2025-08-01 14:21:27,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=44.755, H2=16.739, H3=1.832
2025-08-01 14:21:27,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2281 sites
2025-08-01 14:21:37,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.601, H2=-2.505, H3=-6.815
2025-08-01 14:21:37,180 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2801 sites
2025-08-01 14:21:48,833 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.804, H2=4.430, H3=3.187
2025-08-01 14:21:48,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2858 sites
2025-08-01 14:22:00,737 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.150, H2=0.978, H3=1.054
2025-08-01 14:22:00,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1634 sites
2025-08-01 14:22:07,535 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.904, H2=3.275, H3=0.289
2025-08-01 14:22:07,562 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 854 sites
2025-08-01 14:22:11,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-7.519, H2=-5.922, H3=-7.659
2025-08-01 14:22:11,145 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1306 sites
2025-08-01 14:22:16,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=9.414, H2=2.263, H3=-2.046
2025-08-01 14:22:16,612 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1218 sites
2025-08-01 14:22:21,727 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-2.235, H2=-3.060, H3=-3.398
2025-08-01 14:22:21,757 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2969 sites
2025-08-01 14:22:34,219 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=12.318, H2=3.630, H3=4.774
2025-08-01 14:22:34,252 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3952 sites
2025-08-01 14:22:50,774 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=28.201, H2=12.132, H3=9.107
2025-08-01 14:22:50,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 265 sites
2025-08-01 14:22:51,907 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.894, H2=-7.088, H3=1.798
2025-08-01 14:22:51,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 384 sites
2025-08-01 14:22:53,533 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=2.726, H2=0.874, H3=-2.839
2025-08-01 14:22:53,560 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 620 sites
2025-08-01 14:22:56,170 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=2.978, H2=-0.654, H3=-0.726
2025-08-01 14:22:56,195 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-08-01 14:22:56,195 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-08-01 14:22:56,197 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-08-01 14:22:56,225 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:23:03,718 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:23:03,746 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1112 sites
2025-08-01 14:23:08,425 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=12.511, H2=-1.205, H3=0.708
2025-08-01 14:23:08,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2563 sites
2025-08-01 14:23:19,164 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.644, H2=-5.023, H3=-0.066
2025-08-01 14:23:19,195 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2814 sites
2025-08-01 14:23:30,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.621, H2=2.666, H3=-3.074
2025-08-01 14:23:30,974 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1076 sites
2025-08-01 14:23:35,490 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.357, H2=-1.393, H3=-0.915
2025-08-01 14:23:35,519 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2136 sites
2025-08-01 14:23:44,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.732, H2=0.051, H3=1.721
2025-08-01 14:23:44,449 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3197 sites
2025-08-01 14:23:57,811 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=26.769, H2=-0.758, H3=-0.310
2025-08-01 14:23:57,841 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2483 sites
2025-08-01 14:24:08,206 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.573, H2=-1.807, H3=-1.959
2025-08-01 14:24:08,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4719 sites
2025-08-01 14:24:27,924 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=8.801, H2=4.936, H3=2.711
2025-08-01 14:24:27,955 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2940 sites
2025-08-01 14:24:40,200 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=1.709, H2=0.624, H3=1.679
2025-08-01 14:24:40,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6402 sites
2025-08-01 14:25:06,943 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=41.577, H2=14.906, H3=2.525
2025-08-01 14:25:06,973 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2281 sites
2025-08-01 14:25:16,462 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=11.472, H2=-2.190, H3=-8.172
2025-08-01 14:25:16,493 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2801 sites
2025-08-01 14:25:28,159 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=7.452, H2=4.143, H3=2.794
2025-08-01 14:25:28,190 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2858 sites
2025-08-01 14:25:40,115 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=6.651, H2=0.695, H3=1.448
2025-08-01 14:25:40,144 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1634 sites
2025-08-01 14:25:46,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=7.009, H2=3.057, H3=0.282
2025-08-01 14:25:46,973 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 854 sites
2025-08-01 14:25:50,536 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.539, H2=-7.568, H3=-7.482
2025-08-01 14:25:50,564 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1306 sites
2025-08-01 14:25:56,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=10.186, H2=1.762, H3=-2.027
2025-08-01 14:25:56,043 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1218 sites
2025-08-01 14:26:01,115 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-2.311, H2=-3.410, H3=-3.173
2025-08-01 14:26:01,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2969 sites
2025-08-01 14:26:13,549 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=12.380, H2=4.244, H3=4.518
2025-08-01 14:26:13,582 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3952 sites
2025-08-01 14:26:30,047 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=33.748, H2=14.788, H3=10.035
2025-08-01 14:26:30,073 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 265 sites
2025-08-01 14:26:31,178 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=6.046, H2=-6.922, H3=1.890
2025-08-01 14:26:31,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 384 sites
2025-08-01 14:26:32,799 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=2.458, H2=1.148, H3=-3.003
2025-08-01 14:26:32,825 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 620 sites
2025-08-01 14:26:35,399 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=3.074, H2=-0.532, H3=-0.374
2025-08-01 14:26:35,423 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-08-01 14:26:35,423 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-08-01 14:26:35,425 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-08-01 14:26:35,455 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:26:42,907 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:26:42,934 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1112 sites
2025-08-01 14:26:47,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=12.511, H2=-1.205, H3=0.708
2025-08-01 14:26:47,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2563 sites
2025-08-01 14:26:58,277 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.644, H2=-5.023, H3=-0.066
2025-08-01 14:26:58,307 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2814 sites
2025-08-01 14:27:09,989 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.621, H2=2.666, H3=-3.074
2025-08-01 14:27:10,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1076 sites
2025-08-01 14:27:14,483 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.357, H2=-1.393, H3=-0.915
2025-08-01 14:27:14,512 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2136 sites
2025-08-01 14:27:23,390 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.732, H2=0.051, H3=1.721
2025-08-01 14:27:23,421 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3197 sites
2025-08-01 14:27:36,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=26.769, H2=-0.758, H3=-0.310
2025-08-01 14:27:36,774 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2483 sites
2025-08-01 14:27:47,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.573, H2=-1.807, H3=-1.959
2025-08-01 14:27:47,092 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4719 sites
2025-08-01 14:28:06,741 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=8.801, H2=4.936, H3=2.711
2025-08-01 14:28:06,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2940 sites
2025-08-01 14:28:19,007 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=1.709, H2=0.624, H3=1.679
2025-08-01 14:28:19,043 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6402 sites
2025-08-01 14:28:45,787 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=41.577, H2=14.906, H3=2.525
2025-08-01 14:28:45,817 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2281 sites
2025-08-01 14:28:55,353 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=11.472, H2=-2.190, H3=-8.172
2025-08-01 14:28:55,383 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2801 sites
2025-08-01 14:29:07,087 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=7.452, H2=4.143, H3=2.794
2025-08-01 14:29:07,117 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2858 sites
2025-08-01 14:29:19,011 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=6.651, H2=0.695, H3=1.448
2025-08-01 14:29:19,037 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 734 sites
2025-08-01 14:29:22,093 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.470, H2=1.517, H3=-1.060
2025-08-01 14:29:22,120 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 900 sites
2025-08-01 14:29:25,898 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=5.361, H2=1.049, H3=0.586
2025-08-01 14:29:25,924 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 854 sites
2025-08-01 14:29:29,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-7.695, H2=-5.615, H3=-6.777
2025-08-01 14:29:29,517 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1306 sites
2025-08-01 14:29:34,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=10.049, H2=1.941, H3=-1.525
2025-08-01 14:29:34,981 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1218 sites
2025-08-01 14:29:40,049 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-2.741, H2=-4.533, H3=-3.595
2025-08-01 14:29:40,080 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2969 sites
2025-08-01 14:29:52,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=13.849, H2=3.927, H3=5.948
2025-08-01 14:29:52,473 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3952 sites
2025-08-01 14:30:08,918 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=36.418, H2=16.581, H3=9.479
2025-08-01 14:30:08,944 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 265 sites
2025-08-01 14:30:10,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=4.976, H2=-5.636, H3=1.351
2025-08-01 14:30:10,076 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 384 sites
2025-08-01 14:30:11,679 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=2.413, H2=0.938, H3=-2.263
2025-08-01 14:30:11,705 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 620 sites
2025-08-01 14:30:14,288 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=3.304, H2=-0.666, H3=-0.455
2025-08-01 14:30:14,313 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-08-01 14:30:14,313 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-08-01 14:30:14,315 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-08-01 14:30:14,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:30:21,743 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:30:21,771 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1112 sites
2025-08-01 14:30:26,390 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=12.511, H2=-1.205, H3=0.708
2025-08-01 14:30:26,420 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2563 sites
2025-08-01 14:30:37,045 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.644, H2=-5.023, H3=-0.066
2025-08-01 14:30:37,076 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2814 sites
2025-08-01 14:30:48,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.621, H2=2.666, H3=-3.074
2025-08-01 14:30:48,763 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1076 sites
2025-08-01 14:30:53,254 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.357, H2=-1.393, H3=-0.915
2025-08-01 14:30:53,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2136 sites
2025-08-01 14:31:02,159 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.732, H2=0.051, H3=1.721
2025-08-01 14:31:02,190 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3197 sites
2025-08-01 14:31:15,475 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=26.769, H2=-0.758, H3=-0.310
2025-08-01 14:31:15,502 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1458 sites
2025-08-01 14:31:21,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=26.428, H2=0.152, H3=-0.017
2025-08-01 14:31:21,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1025 sites
2025-08-01 14:31:25,966 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.153, H2=-3.670, H3=-3.063
2025-08-01 14:31:26,000 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4719 sites
2025-08-01 14:31:45,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.703, H2=5.660, H3=2.460
2025-08-01 14:31:45,696 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2940 sites
2025-08-01 14:31:57,934 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.362, H2=0.720, H3=2.370
2025-08-01 14:31:57,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6402 sites
2025-08-01 14:32:24,536 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=48.236, H2=18.612, H3=3.216
2025-08-01 14:32:24,566 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2281 sites
2025-08-01 14:32:34,034 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=9.746, H2=-2.179, H3=-7.256
2025-08-01 14:32:34,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2801 sites
2025-08-01 14:32:45,701 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=8.302, H2=4.096, H3=3.696
2025-08-01 14:32:45,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2858 sites
2025-08-01 14:32:57,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=7.080, H2=1.017, H3=1.601
2025-08-01 14:32:57,679 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 734 sites
2025-08-01 14:33:00,758 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-0.843, H2=1.738, H3=-1.089
2025-08-01 14:33:00,785 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 900 sites
2025-08-01 14:33:04,529 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=4.801, H2=1.049, H3=0.626
2025-08-01 14:33:04,556 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 854 sites
2025-08-01 14:33:08,111 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-7.199, H2=-5.765, H3=-6.432
2025-08-01 14:33:08,138 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1306 sites
2025-08-01 14:33:13,552 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=9.836, H2=2.295, H3=-2.201
2025-08-01 14:33:13,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1218 sites
2025-08-01 14:33:18,626 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-2.520, H2=-4.693, H3=-4.165
2025-08-01 14:33:18,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2969 sites
2025-08-01 14:33:30,978 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=12.021, H2=4.666, H3=4.680
2025-08-01 14:33:31,009 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3952 sites
2025-08-01 14:33:47,445 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=32.061, H2=15.767, H3=10.068
2025-08-01 14:33:47,471 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 265 sites
2025-08-01 14:33:48,576 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=5.718, H2=-5.407, H3=1.437
2025-08-01 14:33:48,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 384 sites
2025-08-01 14:33:50,204 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=2.575, H2=0.915, H3=-3.143
2025-08-01 14:33:50,229 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 620 sites
2025-08-01 14:33:52,807 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=3.360, H2=-0.917, H3=-0.619
2025-08-01 14:33:52,831 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-08-01 14:33:52,832 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-08-01 14:33:52,833 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-08-01 14:33:52,863 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:34:00,267 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:34:00,294 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1112 sites
2025-08-01 14:34:04,926 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=12.511, H2=-1.205, H3=0.708
2025-08-01 14:34:04,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2563 sites
2025-08-01 14:34:15,599 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.644, H2=-5.023, H3=-0.066
2025-08-01 14:34:15,629 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2814 sites
2025-08-01 14:34:27,305 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.621, H2=2.666, H3=-3.074
2025-08-01 14:34:27,332 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1076 sites
2025-08-01 14:34:31,785 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.357, H2=-1.393, H3=-0.915
2025-08-01 14:34:31,814 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2136 sites
2025-08-01 14:34:40,664 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.732, H2=0.051, H3=1.721
2025-08-01 14:34:40,695 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3197 sites
2025-08-01 14:34:54,004 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=26.769, H2=-0.758, H3=-0.310
2025-08-01 14:34:54,032 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1458 sites
2025-08-01 14:35:00,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=26.428, H2=0.152, H3=-0.017
2025-08-01 14:35:00,128 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1025 sites
2025-08-01 14:35:04,397 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.153, H2=-3.670, H3=-3.063
2025-08-01 14:35:04,431 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4719 sites
2025-08-01 14:35:24,093 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.703, H2=5.660, H3=2.460
2025-08-01 14:35:24,121 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1249 sites
2025-08-01 14:35:29,303 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.501, H2=1.043, H3=3.949
2025-08-01 14:35:29,332 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1691 sites
2025-08-01 14:35:36,363 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.342, H2=-1.048, H3=-1.494
2025-08-01 14:35:36,400 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 6402 sites
2025-08-01 14:36:03,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=40.063, H2=16.269, H3=2.286
2025-08-01 14:36:03,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2281 sites
2025-08-01 14:36:12,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=10.678, H2=-2.518, H3=-6.487
2025-08-01 14:36:12,544 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2801 sites
2025-08-01 14:36:24,280 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=7.420, H2=4.246, H3=3.632
2025-08-01 14:36:24,311 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2858 sites
2025-08-01 14:36:36,152 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=6.181, H2=1.099, H3=1.443
2025-08-01 14:36:36,179 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 734 sites
2025-08-01 14:36:39,235 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.311, H2=1.614, H3=-0.826
2025-08-01 14:36:39,262 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 900 sites
2025-08-01 14:36:43,007 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=5.250, H2=0.975, H3=1.081
2025-08-01 14:36:43,035 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 854 sites
2025-08-01 14:36:46,598 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-6.579, H2=-6.409, H3=-7.537
2025-08-01 14:36:46,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1306 sites
2025-08-01 14:36:52,085 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=9.122, H2=2.119, H3=-2.307
2025-08-01 14:36:52,113 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1218 sites
2025-08-01 14:36:57,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-2.549, H2=-4.446, H3=-3.803
2025-08-01 14:36:57,208 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2969 sites
2025-08-01 14:37:09,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=10.978, H2=4.977, H3=5.563
2025-08-01 14:37:09,584 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3952 sites
2025-08-01 14:37:26,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=30.892, H2=14.238, H3=10.062
2025-08-01 14:37:26,089 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 265 sites
2025-08-01 14:37:27,194 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=5.628, H2=-6.977, H3=1.833
2025-08-01 14:37:27,220 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 384 sites
2025-08-01 14:37:28,811 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=2.453, H2=0.711, H3=-3.249
2025-08-01 14:37:28,838 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 620 sites
2025-08-01 14:37:31,410 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=2.913, H2=-0.927, H3=-0.458
2025-08-01 14:37:31,436 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-08-01 14:37:31,436 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-08-01 14:37:31,438 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-08-01 14:37:31,468 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:37:38,878 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:37:38,906 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1112 sites
2025-08-01 14:37:43,521 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=12.511, H2=-1.205, H3=0.708
2025-08-01 14:37:43,552 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2563 sites
2025-08-01 14:37:54,259 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.644, H2=-5.023, H3=-0.066
2025-08-01 14:37:54,290 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2814 sites
2025-08-01 14:38:06,053 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.621, H2=2.666, H3=-3.074
2025-08-01 14:38:06,081 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1076 sites
2025-08-01 14:38:10,562 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.357, H2=-1.393, H3=-0.915
2025-08-01 14:38:10,592 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2136 sites
2025-08-01 14:38:19,484 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.732, H2=0.051, H3=1.721
2025-08-01 14:38:19,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3197 sites
2025-08-01 14:38:32,858 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=26.769, H2=-0.758, H3=-0.310
2025-08-01 14:38:32,886 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1458 sites
2025-08-01 14:38:38,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=26.428, H2=0.152, H3=-0.017
2025-08-01 14:38:39,003 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1025 sites
2025-08-01 14:38:43,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.153, H2=-3.670, H3=-3.063
2025-08-01 14:38:43,319 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4719 sites
2025-08-01 14:39:02,871 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.703, H2=5.660, H3=2.460
2025-08-01 14:39:02,899 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1249 sites
2025-08-01 14:39:08,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.501, H2=1.043, H3=3.949
2025-08-01 14:39:08,114 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1691 sites
2025-08-01 14:39:15,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.342, H2=-1.048, H3=-1.494
2025-08-01 14:39:15,236 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 6402 sites
2025-08-01 14:39:42,089 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=40.063, H2=16.269, H3=2.286
2025-08-01 14:39:42,119 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2281 sites
2025-08-01 14:39:51,669 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=10.678, H2=-2.518, H3=-6.487
2025-08-01 14:39:51,699 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2801 sites
2025-08-01 14:40:03,497 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=7.420, H2=4.246, H3=3.632
2025-08-01 14:40:03,528 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2858 sites
2025-08-01 14:40:15,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=6.181, H2=1.099, H3=1.443
2025-08-01 14:40:15,644 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 734 sites
2025-08-01 14:40:18,722 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.311, H2=1.614, H3=-0.826
2025-08-01 14:40:18,749 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 900 sites
2025-08-01 14:40:22,507 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=5.250, H2=0.975, H3=1.081
2025-08-01 14:40:22,535 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 854 sites
2025-08-01 14:40:26,095 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-6.579, H2=-6.409, H3=-7.537
2025-08-01 14:40:26,123 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1306 sites
2025-08-01 14:40:31,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=9.122, H2=2.119, H3=-2.307
2025-08-01 14:40:31,622 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1218 sites
2025-08-01 14:40:36,712 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-2.549, H2=-4.446, H3=-3.803
2025-08-01 14:40:36,742 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2969 sites
2025-08-01 14:40:49,183 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=10.978, H2=4.977, H3=5.563
2025-08-01 14:40:49,212 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1733 sites
2025-08-01 14:40:56,457 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=13.010, H2=5.823, H3=0.499
2025-08-01 14:40:56,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2219 sites
2025-08-01 14:41:05,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=2.548, H2=5.774, H3=9.329
2025-08-01 14:41:05,761 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 265 sites
2025-08-01 14:41:06,865 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=5.264, H2=-6.746, H3=1.771
2025-08-01 14:41:06,890 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 384 sites
2025-08-01 14:41:08,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=2.119, H2=0.830, H3=-3.108
2025-08-01 14:41:08,509 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 620 sites
2025-08-01 14:41:11,090 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=3.345, H2=-0.720, H3=-0.590
2025-08-01 14:41:11,115 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-08-01 14:41:11,116 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-08-01 14:41:11,117 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-08-01 14:41:11,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1775 sites
2025-08-01 14:41:18,537 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=42.187, H2=2.218, H3=-3.398
2025-08-01 14:41:18,565 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1112 sites
2025-08-01 14:41:23,197 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=12.511, H2=-1.205, H3=0.708
2025-08-01 14:41:23,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2563 sites
2025-08-01 14:41:33,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.644, H2=-5.023, H3=-0.066
2025-08-01 14:41:33,922 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2814 sites
2025-08-01 14:41:45,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.621, H2=2.666, H3=-3.074
2025-08-01 14:41:45,663 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1076 sites
2025-08-01 14:41:50,133 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.357, H2=-1.393, H3=-0.915
2025-08-01 14:41:50,162 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2136 sites
2025-08-01 14:41:59,035 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.732, H2=0.051, H3=1.721
2025-08-01 14:41:59,066 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3197 sites
2025-08-01 14:42:12,331 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=26.769, H2=-0.758, H3=-0.310
2025-08-01 14:42:12,359 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1458 sites
2025-08-01 14:42:18,413 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=26.428, H2=0.152, H3=-0.017
2025-08-01 14:42:18,440 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1025 sites
2025-08-01 14:42:22,701 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.153, H2=-3.670, H3=-3.063
2025-08-01 14:42:22,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2988 sites
2025-08-01 14:42:35,112 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=11.587, H2=2.531, H3=1.136
2025-08-01 14:42:35,141 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1731 sites
2025-08-01 14:42:42,360 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.451, H2=2.289, H3=1.479
2025-08-01 14:42:42,388 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1249 sites
2025-08-01 14:42:47,578 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.241, H2=1.257, H3=4.128
2025-08-01 14:42:47,607 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1691 sites
2025-08-01 14:42:54,663 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-0.313, H2=-1.341, H3=-1.603
2025-08-01 14:42:54,699 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 6402 sites
2025-08-01 14:43:21,243 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=45.846, H2=17.724, H3=2.666
2025-08-01 14:43:21,272 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2281 sites
2025-08-01 14:43:30,734 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=9.514, H2=-1.954, H3=-7.643
2025-08-01 14:43:30,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2801 sites
2025-08-01 14:43:42,338 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=7.294, H2=4.977, H3=3.222
2025-08-01 14:43:42,369 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2858 sites
2025-08-01 14:43:54,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=6.878, H2=0.914, H3=1.635
2025-08-01 14:43:54,234 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 734 sites
2025-08-01 14:43:57,281 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-0.600, H2=1.910, H3=-0.620
2025-08-01 14:43:57,308 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 900 sites
2025-08-01 14:44:01,047 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=4.899, H2=0.803, H3=0.561
2025-08-01 14:44:01,074 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 854 sites
2025-08-01 14:44:04,615 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-7.604, H2=-6.916, H3=-7.641
2025-08-01 14:44:04,643 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1306 sites
2025-08-01 14:44:10,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=10.751, H2=2.372, H3=-2.129
2025-08-01 14:44:10,097 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1218 sites
2025-08-01 14:44:15,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-2.626, H2=-3.965, H3=-3.333
2025-08-01 14:44:15,174 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2969 sites
2025-08-01 14:44:27,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=13.861, H2=4.477, H3=4.867
2025-08-01 14:44:27,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1733 sites
2025-08-01 14:44:34,727 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=15.700, H2=5.849, H3=0.801
2025-08-01 14:44:34,756 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2219 sites
2025-08-01 14:44:43,965 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=2.576, H2=5.383, H3=9.946
2025-08-01 14:44:43,991 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 265 sites
2025-08-01 14:44:45,089 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=4.356, H2=-6.087, H3=1.852
2025-08-01 14:44:45,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 384 sites
2025-08-01 14:44:46,709 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=2.721, H2=0.841, H3=-3.066
2025-08-01 14:44:46,736 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 620 sites
2025-08-01 14:44:49,309 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=3.141, H2=-0.697, H3=-0.373
2025-08-01 14:44:49,334 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-08-01 14:44:49,334 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-08-01 14:44:49,336 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-08-01 14:44:49,362 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 14:44:51,269 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 14:44:51,297 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 14:44:56,760 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 14:44:56,787 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 14:45:01,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 14:45:01,466 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2563 sites
2025-08-01 14:45:12,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=29.894, H2=-5.144, H3=-0.238
2025-08-01 14:45:12,243 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2814 sites
2025-08-01 14:45:24,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.076, H2=2.118, H3=-2.894
2025-08-01 14:45:24,069 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1076 sites
2025-08-01 14:45:28,562 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.704, H2=-0.952, H3=-0.748
2025-08-01 14:45:28,592 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2136 sites
2025-08-01 14:45:37,520 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.368, H2=0.361, H3=2.118
2025-08-01 14:45:37,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3197 sites
2025-08-01 14:45:50,897 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.064, H2=-0.888, H3=-0.320
2025-08-01 14:45:50,924 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1458 sites
2025-08-01 14:45:57,011 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=27.165, H2=0.086, H3=-0.037
2025-08-01 14:45:57,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1025 sites
2025-08-01 14:46:01,332 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.135, H2=-3.335, H3=-2.498
2025-08-01 14:46:01,363 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2988 sites
2025-08-01 14:46:13,815 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.486, H2=2.929, H3=1.540
2025-08-01 14:46:13,843 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1731 sites
2025-08-01 14:46:21,078 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=2.613, H2=2.391, H3=1.677
2025-08-01 14:46:21,106 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1249 sites
2025-08-01 14:46:26,330 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.525, H2=1.052, H3=3.306
2025-08-01 14:46:26,358 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1691 sites
2025-08-01 14:46:33,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.690, H2=-1.424, H3=-1.549
2025-08-01 14:46:33,503 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 6402 sites
2025-08-01 14:47:00,408 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=42.762, H2=14.485, H3=2.246
2025-08-01 14:47:00,438 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2281 sites
2025-08-01 14:47:10,045 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=9.295, H2=-2.262, H3=-8.030
2025-08-01 14:47:10,075 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2801 sites
2025-08-01 14:47:21,914 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=8.286, H2=3.630, H3=3.123
2025-08-01 14:47:21,944 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2858 sites
2025-08-01 14:47:33,939 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.178, H2=0.682, H3=1.623
2025-08-01 14:47:33,966 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 734 sites
2025-08-01 14:47:37,053 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-0.570, H2=1.747, H3=-1.105
2025-08-01 14:47:37,080 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 900 sites
2025-08-01 14:47:40,905 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.395, H2=0.915, H3=0.903
2025-08-01 14:47:40,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 854 sites
2025-08-01 14:47:44,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-9.560, H2=-6.427, H3=-7.847
2025-08-01 14:47:44,541 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1306 sites
2025-08-01 14:47:49,997 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=11.297, H2=2.786, H3=-2.056
2025-08-01 14:47:50,024 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1218 sites
2025-08-01 14:47:55,105 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-2.170, H2=-3.502, H3=-2.820
2025-08-01 14:47:55,135 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2969 sites
2025-08-01 14:48:07,503 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=13.664, H2=4.775, H3=5.284
2025-08-01 14:48:07,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1733 sites
2025-08-01 14:48:14,787 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=13.695, H2=5.218, H3=0.483
2025-08-01 14:48:14,816 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2219 sites
2025-08-01 14:48:23,995 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=2.563, H2=5.100, H3=9.514
2025-08-01 14:48:24,021 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 265 sites
2025-08-01 14:48:25,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=6.105, H2=-5.337, H3=1.591
2025-08-01 14:48:25,142 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 384 sites
2025-08-01 14:48:26,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=2.251, H2=0.823, H3=-2.883
2025-08-01 14:48:26,755 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 620 sites
2025-08-01 14:48:29,364 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=3.318, H2=-0.947, H3=-0.557
2025-08-01 14:48:29,389 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-08-01 14:48:29,389 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-08-01 14:48:29,391 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-08-01 14:48:29,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 14:48:31,326 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 14:48:31,354 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 14:48:36,815 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 14:48:36,842 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 14:48:41,450 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 14:48:41,480 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2563 sites
2025-08-01 14:48:52,056 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=29.894, H2=-5.144, H3=-0.238
2025-08-01 14:48:52,086 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2814 sites
2025-08-01 14:49:03,746 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.076, H2=2.118, H3=-2.894
2025-08-01 14:49:03,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1076 sites
2025-08-01 14:49:08,228 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.704, H2=-0.952, H3=-0.748
2025-08-01 14:49:08,258 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2136 sites
2025-08-01 14:49:17,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.368, H2=0.361, H3=2.118
2025-08-01 14:49:17,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3197 sites
2025-08-01 14:49:30,376 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.064, H2=-0.888, H3=-0.320
2025-08-01 14:49:30,404 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1458 sites
2025-08-01 14:49:36,474 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=27.165, H2=0.086, H3=-0.037
2025-08-01 14:49:36,501 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1025 sites
2025-08-01 14:49:40,742 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.135, H2=-3.335, H3=-2.498
2025-08-01 14:49:40,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2988 sites
2025-08-01 14:49:53,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.486, H2=2.929, H3=1.540
2025-08-01 14:49:53,164 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1731 sites
2025-08-01 14:50:00,355 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=2.613, H2=2.391, H3=1.677
2025-08-01 14:50:00,383 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1249 sites
2025-08-01 14:50:05,570 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.525, H2=1.052, H3=3.306
2025-08-01 14:50:05,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1691 sites
2025-08-01 14:50:12,623 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.690, H2=-1.424, H3=-1.549
2025-08-01 14:50:12,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1867 sites
2025-08-01 14:50:20,426 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-4.432, H2=-2.764, H3=1.152
2025-08-01 14:50:20,460 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4535 sites
2025-08-01 14:50:39,357 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=41.980, H2=19.247, H3=1.786
2025-08-01 14:50:39,387 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2281 sites
2025-08-01 14:50:48,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=10.869, H2=-1.816, H3=-6.816
2025-08-01 14:50:48,894 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2801 sites
2025-08-01 14:51:00,483 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=7.485, H2=4.339, H3=3.486
2025-08-01 14:51:00,514 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2858 sites
2025-08-01 14:51:12,397 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=5.689, H2=0.905, H3=1.691
2025-08-01 14:51:12,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 734 sites
2025-08-01 14:51:15,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-0.425, H2=2.104, H3=-0.996
2025-08-01 14:51:15,493 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 900 sites
2025-08-01 14:51:19,221 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=6.870, H2=0.899, H3=0.837
2025-08-01 14:51:19,248 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 854 sites
2025-08-01 14:51:22,788 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-7.515, H2=-6.891, H3=-7.976
2025-08-01 14:51:22,815 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1306 sites
2025-08-01 14:51:28,243 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=9.168, H2=2.611, H3=-1.421
2025-08-01 14:51:28,271 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1218 sites
2025-08-01 14:51:33,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-2.322, H2=-3.739, H3=-3.349
2025-08-01 14:51:33,366 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2969 sites
2025-08-01 14:51:45,666 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=13.065, H2=4.420, H3=5.335
2025-08-01 14:51:45,695 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1733 sites
2025-08-01 14:51:52,916 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=15.924, H2=5.990, H3=0.512
2025-08-01 14:51:52,945 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2219 sites
2025-08-01 14:52:02,149 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=2.520, H2=5.584, H3=8.180
2025-08-01 14:52:02,175 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 265 sites
2025-08-01 14:52:03,269 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=4.720, H2=-6.453, H3=1.355
2025-08-01 14:52:03,295 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 384 sites
2025-08-01 14:52:04,890 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=2.653, H2=0.675, H3=-3.114
2025-08-01 14:52:04,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 620 sites
2025-08-01 14:52:07,492 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=3.175, H2=-0.557, H3=-0.230
2025-08-01 14:52:07,516 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=30 has insufficient data
2025-08-01 14:52:07,517 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=31
2025-08-01 14:52:07,518 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31 -9223372036854775808]
2025-08-01 14:52:07,545 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 14:52:09,449 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 14:52:09,478 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 14:52:14,955 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 14:52:14,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 14:52:19,589 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 14:52:19,619 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2563 sites
2025-08-01 14:52:30,251 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=29.894, H2=-5.144, H3=-0.238
2025-08-01 14:52:30,281 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2814 sites
2025-08-01 14:52:41,887 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.076, H2=2.118, H3=-2.894
2025-08-01 14:52:41,914 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1076 sites
2025-08-01 14:52:46,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.704, H2=-0.952, H3=-0.748
2025-08-01 14:52:46,403 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2136 sites
2025-08-01 14:52:55,243 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.368, H2=0.361, H3=2.118
2025-08-01 14:52:55,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3197 sites
2025-08-01 14:53:08,547 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.064, H2=-0.888, H3=-0.320
2025-08-01 14:53:08,574 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1458 sites
2025-08-01 14:53:14,613 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=27.165, H2=0.086, H3=-0.037
2025-08-01 14:53:14,640 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1025 sites
2025-08-01 14:53:18,885 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.135, H2=-3.335, H3=-2.498
2025-08-01 14:53:18,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2988 sites
2025-08-01 14:53:31,308 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.486, H2=2.929, H3=1.540
2025-08-01 14:53:31,335 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1247 sites
2025-08-01 14:53:36,512 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.270, H2=-1.909, H3=0.694
2025-08-01 14:53:36,538 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 484 sites
2025-08-01 14:53:38,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=0.807, H2=6.238, H3=1.236
2025-08-01 14:53:38,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1249 sites
2025-08-01 14:53:43,832 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.582, H2=1.078, H3=3.657
2025-08-01 14:53:43,860 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1691 sites
2025-08-01 14:53:50,888 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.531, H2=-1.024, H3=-1.177
2025-08-01 14:53:50,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1867 sites
2025-08-01 14:53:58,767 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-3.353, H2=-2.908, H3=0.932
2025-08-01 14:53:58,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4535 sites
2025-08-01 14:54:17,779 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=48.248, H2=16.456, H3=1.651
2025-08-01 14:54:17,809 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2281 sites
2025-08-01 14:54:27,300 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=10.462, H2=-2.005, H3=-7.895
2025-08-01 14:54:27,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2801 sites
2025-08-01 14:54:39,040 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.410, H2=4.967, H3=3.925
2025-08-01 14:54:39,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2858 sites
2025-08-01 14:54:50,990 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=6.495, H2=0.540, H3=1.794
2025-08-01 14:54:51,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 734 sites
2025-08-01 14:54:54,102 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-0.491, H2=1.816, H3=-1.073
2025-08-01 14:54:54,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 900 sites
2025-08-01 14:54:57,919 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.406, H2=1.310, H3=0.739
2025-08-01 14:54:57,946 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 854 sites
2025-08-01 14:55:01,532 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-7.217, H2=-6.303, H3=-6.140
2025-08-01 14:55:01,560 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1306 sites
2025-08-01 14:55:07,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=10.800, H2=3.008, H3=-1.813
2025-08-01 14:55:07,031 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1218 sites
2025-08-01 14:55:12,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-2.862, H2=-3.594, H3=-3.345
2025-08-01 14:55:12,089 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2969 sites
2025-08-01 14:55:24,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=14.497, H2=4.747, H3=4.939
2025-08-01 14:55:24,446 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1733 sites
2025-08-01 14:55:31,628 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=14.485, H2=5.026, H3=0.594
2025-08-01 14:55:31,658 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2219 sites
2025-08-01 14:55:40,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=2.916, H2=5.243, H3=8.951
2025-08-01 14:55:40,910 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 265 sites
2025-08-01 14:55:42,005 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=6.133, H2=-6.622, H3=1.551
2025-08-01 14:55:42,030 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 384 sites
2025-08-01 14:55:43,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=2.379, H2=0.941, H3=-2.494
2025-08-01 14:55:43,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 620 sites
2025-08-01 14:55:46,225 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=3.022, H2=-0.848, H3=-0.111
2025-08-01 14:55:46,250 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=31 has insufficient data
2025-08-01 14:55:46,251 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=32
2025-08-01 14:55:46,252 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32 -9223372036854775808]
2025-08-01 14:55:46,278 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 14:55:48,191 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 14:55:48,219 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 14:55:53,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 14:55:53,715 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 14:55:58,328 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 14:55:58,359 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2563 sites
2025-08-01 14:56:09,035 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=29.894, H2=-5.144, H3=-0.238
2025-08-01 14:56:09,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2814 sites
2025-08-01 14:56:20,682 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.076, H2=2.118, H3=-2.894
2025-08-01 14:56:20,709 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1076 sites
2025-08-01 14:56:25,161 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.704, H2=-0.952, H3=-0.748
2025-08-01 14:56:25,190 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2136 sites
2025-08-01 14:56:34,039 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.368, H2=0.361, H3=2.118
2025-08-01 14:56:34,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3197 sites
2025-08-01 14:56:47,345 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.064, H2=-0.888, H3=-0.320
2025-08-01 14:56:47,373 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1458 sites
2025-08-01 14:56:53,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=27.165, H2=0.086, H3=-0.037
2025-08-01 14:56:53,428 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1025 sites
2025-08-01 14:56:57,685 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.135, H2=-3.335, H3=-2.498
2025-08-01 14:56:57,715 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2988 sites
2025-08-01 14:57:10,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.486, H2=2.929, H3=1.540
2025-08-01 14:57:10,108 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1247 sites
2025-08-01 14:57:15,269 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.270, H2=-1.909, H3=0.694
2025-08-01 14:57:15,295 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 484 sites
2025-08-01 14:57:17,306 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=0.807, H2=6.238, H3=1.236
2025-08-01 14:57:17,333 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1249 sites
2025-08-01 14:57:22,538 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.582, H2=1.078, H3=3.657
2025-08-01 14:57:22,566 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1691 sites
2025-08-01 14:57:29,581 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.531, H2=-1.024, H3=-1.177
2025-08-01 14:57:29,610 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1867 sites
2025-08-01 14:57:37,360 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-3.353, H2=-2.908, H3=0.932
2025-08-01 14:57:37,393 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4535 sites
2025-08-01 14:57:56,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=48.248, H2=16.456, H3=1.651
2025-08-01 14:57:56,243 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2281 sites
2025-08-01 14:58:05,707 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=10.462, H2=-2.005, H3=-7.895
2025-08-01 14:58:05,738 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2801 sites
2025-08-01 14:58:17,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.410, H2=4.967, H3=3.925
2025-08-01 14:58:17,364 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2858 sites
2025-08-01 14:58:29,237 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=6.495, H2=0.540, H3=1.794
2025-08-01 14:58:29,263 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 734 sites
2025-08-01 14:58:32,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-0.491, H2=1.816, H3=-1.073
2025-08-01 14:58:32,335 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 900 sites
2025-08-01 14:58:36,090 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.406, H2=1.310, H3=0.739
2025-08-01 14:58:36,117 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 854 sites
2025-08-01 14:58:39,681 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-7.217, H2=-6.303, H3=-6.140
2025-08-01 14:58:39,709 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1306 sites
2025-08-01 14:58:45,182 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=10.800, H2=3.008, H3=-1.813
2025-08-01 14:58:45,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1218 sites
2025-08-01 14:58:50,272 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-2.862, H2=-3.594, H3=-3.345
2025-08-01 14:58:50,302 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2969 sites
2025-08-01 14:59:02,649 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=14.497, H2=4.747, H3=4.939
2025-08-01 14:59:02,678 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1733 sites
2025-08-01 14:59:09,835 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=14.485, H2=5.026, H3=0.594
2025-08-01 14:59:09,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2219 sites
2025-08-01 14:59:19,113 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=2.916, H2=5.243, H3=8.951
2025-08-01 14:59:19,139 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 265 sites
2025-08-01 14:59:20,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=6.133, H2=-6.622, H3=1.551
2025-08-01 14:59:20,264 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 384 sites
2025-08-01 14:59:21,851 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=2.379, H2=0.941, H3=-2.494
2025-08-01 14:59:21,877 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 341 sites
2025-08-01 14:59:23,285 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-0.133, H2=1.405, H3=-1.409
2025-08-01 14:59:23,310 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 279 sites
2025-08-01 14:59:24,465 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-0.324, H2=-2.930, H3=1.197
2025-08-01 14:59:24,489 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=32 has insufficient data
2025-08-01 14:59:24,489 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=33
2025-08-01 14:59:24,491 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
 -9223372036854775808]
2025-08-01 14:59:24,517 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 14:59:26,428 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 14:59:26,457 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 14:59:31,959 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 14:59:31,986 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 14:59:36,596 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 14:59:36,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2563 sites
2025-08-01 14:59:47,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=29.894, H2=-5.144, H3=-0.238
2025-08-01 14:59:47,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2814 sites
2025-08-01 14:59:58,965 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.076, H2=2.118, H3=-2.894
2025-08-01 14:59:58,992 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1076 sites
2025-08-01 15:00:03,459 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.704, H2=-0.952, H3=-0.748
2025-08-01 15:00:03,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2136 sites
2025-08-01 15:00:12,343 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.368, H2=0.361, H3=2.118
2025-08-01 15:00:12,375 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3197 sites
2025-08-01 15:00:25,707 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.064, H2=-0.888, H3=-0.320
2025-08-01 15:00:25,735 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1458 sites
2025-08-01 15:00:31,809 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=27.165, H2=0.086, H3=-0.037
2025-08-01 15:00:31,837 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1025 sites
2025-08-01 15:00:36,127 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.135, H2=-3.335, H3=-2.498
2025-08-01 15:00:36,157 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2988 sites
2025-08-01 15:00:48,645 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.486, H2=2.929, H3=1.540
2025-08-01 15:00:48,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1247 sites
2025-08-01 15:00:53,841 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.270, H2=-1.909, H3=0.694
2025-08-01 15:00:53,868 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 484 sites
2025-08-01 15:00:55,894 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=0.807, H2=6.238, H3=1.236
2025-08-01 15:00:55,921 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1249 sites
2025-08-01 15:01:01,125 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.582, H2=1.078, H3=3.657
2025-08-01 15:01:01,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1691 sites
2025-08-01 15:01:08,198 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.531, H2=-1.024, H3=-1.177
2025-08-01 15:01:08,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1867 sites
2025-08-01 15:01:15,974 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-3.353, H2=-2.908, H3=0.932
2025-08-01 15:01:16,002 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1658 sites
2025-08-01 15:01:22,944 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=20.905, H2=8.339, H3=-2.021
2025-08-01 15:01:22,975 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2877 sites
2025-08-01 15:01:34,967 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.793, H2=5.965, H3=3.876
2025-08-01 15:01:34,996 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2281 sites
2025-08-01 15:01:44,432 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=10.498, H2=-2.161, H3=-8.194
2025-08-01 15:01:44,462 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2801 sites
2025-08-01 15:01:56,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=7.571, H2=3.692, H3=3.569
2025-08-01 15:01:56,165 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2858 sites
2025-08-01 15:02:07,950 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=6.992, H2=1.017, H3=1.659
2025-08-01 15:02:07,977 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 734 sites
2025-08-01 15:02:11,014 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-0.499, H2=2.504, H3=-1.153
2025-08-01 15:02:11,041 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 900 sites
2025-08-01 15:02:14,769 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=5.002, H2=1.197, H3=0.833
2025-08-01 15:02:14,796 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 854 sites
2025-08-01 15:02:18,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-7.842, H2=-7.217, H3=-7.911
2025-08-01 15:02:18,363 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1306 sites
2025-08-01 15:02:23,782 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=11.379, H2=1.999, H3=-1.917
2025-08-01 15:02:23,810 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1218 sites
2025-08-01 15:02:28,853 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-3.022, H2=-3.911, H3=-3.288
2025-08-01 15:02:28,883 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2969 sites
2025-08-01 15:02:41,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=12.797, H2=4.836, H3=4.289
2025-08-01 15:02:41,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1733 sites
2025-08-01 15:02:48,433 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=14.506, H2=6.161, H3=0.408
2025-08-01 15:02:48,462 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2219 sites
2025-08-01 15:02:57,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=2.659, H2=6.158, H3=8.573
2025-08-01 15:02:57,717 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 265 sites
2025-08-01 15:02:58,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=5.261, H2=-5.294, H3=1.496
2025-08-01 15:02:58,838 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 384 sites
2025-08-01 15:03:00,424 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=2.508, H2=0.993, H3=-2.262
2025-08-01 15:03:00,450 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 341 sites
2025-08-01 15:03:01,856 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-0.170, H2=0.749, H3=-1.824
2025-08-01 15:03:01,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 279 sites
2025-08-01 15:03:03,035 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=-0.516, H2=-2.697, H3=1.115
2025-08-01 15:03:03,061 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=33 has insufficient data
2025-08-01 15:03:03,061 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=34
2025-08-01 15:03:03,062 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34 -9223372036854775808]
2025-08-01 15:03:03,088 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:03:04,999 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:03:05,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:03:10,538 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:03:10,566 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 15:03:15,200 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 15:03:15,230 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2563 sites
2025-08-01 15:03:25,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=29.894, H2=-5.144, H3=-0.238
2025-08-01 15:03:25,915 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2814 sites
2025-08-01 15:03:37,593 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.076, H2=2.118, H3=-2.894
2025-08-01 15:03:37,621 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1076 sites
2025-08-01 15:03:42,085 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.704, H2=-0.952, H3=-0.748
2025-08-01 15:03:42,114 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2136 sites
2025-08-01 15:03:50,985 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.368, H2=0.361, H3=2.118
2025-08-01 15:03:51,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3197 sites
2025-08-01 15:04:04,241 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.064, H2=-0.888, H3=-0.320
2025-08-01 15:04:04,269 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1458 sites
2025-08-01 15:04:10,333 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=27.165, H2=0.086, H3=-0.037
2025-08-01 15:04:10,360 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1025 sites
2025-08-01 15:04:14,619 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.135, H2=-3.335, H3=-2.498
2025-08-01 15:04:14,649 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2988 sites
2025-08-01 15:04:27,096 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.486, H2=2.929, H3=1.540
2025-08-01 15:04:27,124 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1247 sites
2025-08-01 15:04:32,301 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.270, H2=-1.909, H3=0.694
2025-08-01 15:04:32,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 484 sites
2025-08-01 15:04:34,329 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=0.807, H2=6.238, H3=1.236
2025-08-01 15:04:34,356 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1249 sites
2025-08-01 15:04:39,517 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.582, H2=1.078, H3=3.657
2025-08-01 15:04:39,546 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1691 sites
2025-08-01 15:04:46,530 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.531, H2=-1.024, H3=-1.177
2025-08-01 15:04:46,559 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1867 sites
2025-08-01 15:04:54,278 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-3.353, H2=-2.908, H3=0.932
2025-08-01 15:04:54,306 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1658 sites
2025-08-01 15:05:01,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=20.905, H2=8.339, H3=-2.021
2025-08-01 15:05:01,206 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2877 sites
2025-08-01 15:05:13,111 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.793, H2=5.965, H3=3.876
2025-08-01 15:05:13,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2281 sites
2025-08-01 15:05:22,606 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=10.498, H2=-2.161, H3=-8.194
2025-08-01 15:05:22,636 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2801 sites
2025-08-01 15:05:34,316 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=7.571, H2=3.692, H3=3.569
2025-08-01 15:05:34,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1003 sites
2025-08-01 15:05:38,485 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=0.770, H2=2.861, H3=2.526
2025-08-01 15:05:38,513 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1855 sites
2025-08-01 15:05:46,181 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.218, H2=-2.111, H3=0.251
2025-08-01 15:05:46,207 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 734 sites
2025-08-01 15:05:49,246 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-0.316, H2=2.142, H3=-0.699
2025-08-01 15:05:49,272 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 900 sites
2025-08-01 15:05:52,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=5.052, H2=1.242, H3=0.916
2025-08-01 15:05:53,013 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 854 sites
2025-08-01 15:05:56,550 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-8.351, H2=-5.831, H3=-6.973
2025-08-01 15:05:56,578 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1306 sites
2025-08-01 15:06:02,001 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=9.098, H2=1.927, H3=-1.765
2025-08-01 15:06:02,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1218 sites
2025-08-01 15:06:07,078 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-2.358, H2=-3.569, H3=-3.076
2025-08-01 15:06:07,108 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2969 sites
2025-08-01 15:06:19,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=13.551, H2=5.245, H3=4.327
2025-08-01 15:06:19,440 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1733 sites
2025-08-01 15:06:26,625 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=14.677, H2=6.223, H3=0.570
2025-08-01 15:06:26,654 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2219 sites
2025-08-01 15:06:35,876 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=2.936, H2=5.725, H3=9.492
2025-08-01 15:06:35,902 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 265 sites
2025-08-01 15:06:36,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=6.262, H2=-5.652, H3=2.134
2025-08-01 15:06:37,022 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 384 sites
2025-08-01 15:06:38,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=2.867, H2=0.670, H3=-2.845
2025-08-01 15:06:38,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 341 sites
2025-08-01 15:06:40,065 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=-0.415, H2=0.642, H3=-2.118
2025-08-01 15:06:40,091 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 279 sites
2025-08-01 15:06:41,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=-0.404, H2=-2.506, H3=1.566
2025-08-01 15:06:41,283 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=34 has insufficient data
2025-08-01 15:06:41,283 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=35
2025-08-01 15:06:41,284 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35 -9223372036854775808]
2025-08-01 15:06:41,311 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:06:43,240 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:06:43,267 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:06:48,826 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:06:48,854 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 15:06:53,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 15:06:53,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2563 sites
2025-08-01 15:07:04,226 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=29.894, H2=-5.144, H3=-0.238
2025-08-01 15:07:04,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2814 sites
2025-08-01 15:07:15,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.076, H2=2.118, H3=-2.894
2025-08-01 15:07:16,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1076 sites
2025-08-01 15:07:20,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.704, H2=-0.952, H3=-0.748
2025-08-01 15:07:20,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2136 sites
2025-08-01 15:07:29,425 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.368, H2=0.361, H3=2.118
2025-08-01 15:07:29,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3197 sites
2025-08-01 15:07:42,813 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.064, H2=-0.888, H3=-0.320
2025-08-01 15:07:42,840 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1458 sites
2025-08-01 15:07:48,962 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=27.165, H2=0.086, H3=-0.037
2025-08-01 15:07:48,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1025 sites
2025-08-01 15:07:53,250 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.135, H2=-3.335, H3=-2.498
2025-08-01 15:07:53,280 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2988 sites
2025-08-01 15:08:05,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.486, H2=2.929, H3=1.540
2025-08-01 15:08:05,674 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1247 sites
2025-08-01 15:08:10,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.270, H2=-1.909, H3=0.694
2025-08-01 15:08:10,890 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 484 sites
2025-08-01 15:08:12,888 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=0.807, H2=6.238, H3=1.236
2025-08-01 15:08:12,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1249 sites
2025-08-01 15:08:18,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.582, H2=1.078, H3=3.657
2025-08-01 15:08:18,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1691 sites
2025-08-01 15:08:25,159 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.531, H2=-1.024, H3=-1.177
2025-08-01 15:08:25,188 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1867 sites
2025-08-01 15:08:32,908 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-3.353, H2=-2.908, H3=0.932
2025-08-01 15:08:32,937 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1658 sites
2025-08-01 15:08:39,814 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=20.905, H2=8.339, H3=-2.021
2025-08-01 15:08:39,844 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2877 sites
2025-08-01 15:08:51,765 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.793, H2=5.965, H3=3.876
2025-08-01 15:08:51,795 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2281 sites
2025-08-01 15:09:01,241 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=10.498, H2=-2.161, H3=-8.194
2025-08-01 15:09:01,272 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2801 sites
2025-08-01 15:09:12,872 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=7.571, H2=3.692, H3=3.569
2025-08-01 15:09:12,899 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1003 sites
2025-08-01 15:09:17,064 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=0.770, H2=2.861, H3=2.526
2025-08-01 15:09:17,093 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1855 sites
2025-08-01 15:09:24,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.218, H2=-2.111, H3=0.251
2025-08-01 15:09:24,807 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 734 sites
2025-08-01 15:09:27,843 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-0.316, H2=2.142, H3=-0.699
2025-08-01 15:09:27,870 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 900 sites
2025-08-01 15:09:31,597 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=5.052, H2=1.242, H3=0.916
2025-08-01 15:09:31,625 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 854 sites
2025-08-01 15:09:35,151 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-8.351, H2=-5.831, H3=-6.973
2025-08-01 15:09:35,179 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1306 sites
2025-08-01 15:09:40,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=9.098, H2=1.927, H3=-1.765
2025-08-01 15:09:40,621 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1218 sites
2025-08-01 15:09:45,676 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-2.358, H2=-3.569, H3=-3.076
2025-08-01 15:09:45,703 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1092 sites
2025-08-01 15:09:50,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=4.504, H2=2.833, H3=3.117
2025-08-01 15:09:50,270 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1877 sites
2025-08-01 15:09:58,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=3.866, H2=2.644, H3=3.794
2025-08-01 15:09:58,086 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1733 sites
2025-08-01 15:10:05,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=13.993, H2=5.457, H3=0.650
2025-08-01 15:10:05,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 2219 sites
2025-08-01 15:10:14,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=2.549, H2=5.844, H3=9.174
2025-08-01 15:10:14,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 265 sites
2025-08-01 15:10:15,656 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=6.806, H2=-6.836, H3=1.365
2025-08-01 15:10:15,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 384 sites
2025-08-01 15:10:17,271 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=2.031, H2=0.770, H3=-3.252
2025-08-01 15:10:17,296 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 341 sites
2025-08-01 15:10:18,718 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=-0.233, H2=0.967, H3=-2.021
2025-08-01 15:10:18,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 279 sites
2025-08-01 15:10:19,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=-0.468, H2=-2.426, H3=1.242
2025-08-01 15:10:19,920 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=35 has insufficient data
2025-08-01 15:10:19,920 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=36
2025-08-01 15:10:19,922 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
 -9223372036854775808]
2025-08-01 15:10:19,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:10:21,852 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:10:21,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:10:27,349 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:10:27,376 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 15:10:31,992 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 15:10:32,019 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1081 sites
2025-08-01 15:10:36,524 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=14.183, H2=-8.252, H3=-1.470
2025-08-01 15:10:36,552 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1482 sites
2025-08-01 15:10:42,712 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.970, H2=-0.843, H3=0.674
2025-08-01 15:10:42,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2814 sites
2025-08-01 15:10:54,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.781, H2=2.323, H3=-2.847
2025-08-01 15:10:54,489 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1076 sites
2025-08-01 15:10:58,955 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.279, H2=-0.832, H3=-0.856
2025-08-01 15:10:58,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2136 sites
2025-08-01 15:11:07,857 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=10.257, H2=-0.013, H3=1.721
2025-08-01 15:11:07,889 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3197 sites
2025-08-01 15:11:21,105 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.441, H2=-1.318, H3=-0.199
2025-08-01 15:11:21,133 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1458 sites
2025-08-01 15:11:27,154 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=28.608, H2=0.241, H3=-0.074
2025-08-01 15:11:27,182 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1025 sites
2025-08-01 15:11:31,432 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.904, H2=-3.083, H3=-2.848
2025-08-01 15:11:31,462 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2988 sites
2025-08-01 15:11:43,908 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.082, H2=2.991, H3=1.906
2025-08-01 15:11:43,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1247 sites
2025-08-01 15:11:49,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-0.325, H2=-2.142, H3=0.491
2025-08-01 15:11:49,144 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 484 sites
2025-08-01 15:11:51,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=0.897, H2=5.603, H3=1.514
2025-08-01 15:11:51,170 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1249 sites
2025-08-01 15:11:56,351 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.218, H2=1.021, H3=3.335
2025-08-01 15:11:56,380 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1691 sites
2025-08-01 15:12:03,430 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-0.576, H2=-1.267, H3=-1.305
2025-08-01 15:12:03,459 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1867 sites
2025-08-01 15:12:11,198 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-3.692, H2=-2.274, H3=1.106
2025-08-01 15:12:11,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1658 sites
2025-08-01 15:12:18,091 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=18.052, H2=8.430, H3=-1.627
2025-08-01 15:12:18,122 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2877 sites
2025-08-01 15:12:30,102 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=8.765, H2=6.368, H3=3.341
2025-08-01 15:12:30,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2281 sites
2025-08-01 15:12:39,679 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=9.260, H2=-2.233, H3=-7.477
2025-08-01 15:12:39,710 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2801 sites
2025-08-01 15:12:51,332 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=7.597, H2=4.853, H3=3.684
2025-08-01 15:12:51,360 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1003 sites
2025-08-01 15:12:55,541 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=0.664, H2=3.309, H3=2.335
2025-08-01 15:12:55,570 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1855 sites
2025-08-01 15:13:03,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=1.384, H2=-2.089, H3=0.217
2025-08-01 15:13:03,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 734 sites
2025-08-01 15:13:06,446 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-0.418, H2=2.058, H3=-0.775
2025-08-01 15:13:06,472 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 900 sites
2025-08-01 15:13:10,253 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=6.107, H2=0.853, H3=0.811
2025-08-01 15:13:10,280 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 854 sites
2025-08-01 15:13:13,864 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-8.028, H2=-6.342, H3=-6.952
2025-08-01 15:13:13,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1306 sites
2025-08-01 15:13:19,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=10.430, H2=2.111, H3=-1.938
2025-08-01 15:13:19,382 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1218 sites
2025-08-01 15:13:24,499 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-2.339, H2=-3.682, H3=-2.543
2025-08-01 15:13:24,526 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1092 sites
2025-08-01 15:13:29,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=4.408, H2=2.629, H3=3.113
2025-08-01 15:13:29,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1877 sites
2025-08-01 15:13:36,937 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=4.345, H2=2.656, H3=3.373
2025-08-01 15:13:36,966 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1733 sites
2025-08-01 15:13:44,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=15.364, H2=5.504, H3=0.562
2025-08-01 15:13:44,267 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 2219 sites
2025-08-01 15:13:53,450 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=2.483, H2=4.975, H3=8.274
2025-08-01 15:13:53,476 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 265 sites
2025-08-01 15:13:54,576 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=4.771, H2=-6.018, H3=1.363
2025-08-01 15:13:54,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 384 sites
2025-08-01 15:13:56,198 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=2.631, H2=1.009, H3=-3.262
2025-08-01 15:13:56,224 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 341 sites
2025-08-01 15:13:57,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=-0.349, H2=0.886, H3=-2.491
2025-08-01 15:13:57,661 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 279 sites
2025-08-01 15:13:58,817 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=-0.405, H2=-2.269, H3=1.673
2025-08-01 15:13:58,842 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=36 has insufficient data
2025-08-01 15:13:58,842 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=37
2025-08-01 15:13:58,844 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37 -9223372036854775808]
2025-08-01 15:13:58,871 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:14:00,779 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:14:00,807 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:14:06,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:14:06,307 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 15:14:10,893 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 15:14:10,921 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1081 sites
2025-08-01 15:14:15,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=14.183, H2=-8.252, H3=-1.470
2025-08-01 15:14:15,469 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1482 sites
2025-08-01 15:14:21,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.970, H2=-0.843, H3=0.674
2025-08-01 15:14:21,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1804 sites
2025-08-01 15:14:29,112 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.802, H2=3.778, H3=-4.301
2025-08-01 15:14:29,139 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1010 sites
2025-08-01 15:14:33,324 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.366, H2=-0.643, H3=0.551
2025-08-01 15:14:33,352 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1076 sites
2025-08-01 15:14:37,830 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=13.505, H2=-0.945, H3=-1.204
2025-08-01 15:14:37,859 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2136 sites
2025-08-01 15:14:46,709 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=9.816, H2=-0.139, H3=1.636
2025-08-01 15:14:46,741 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3197 sites
2025-08-01 15:14:59,992 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=30.763, H2=-0.980, H3=-0.424
2025-08-01 15:15:00,020 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1458 sites
2025-08-01 15:15:06,073 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=24.774, H2=0.178, H3=0.317
2025-08-01 15:15:06,100 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1025 sites
2025-08-01 15:15:10,376 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.024, H2=-3.109, H3=-3.382
2025-08-01 15:15:10,407 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2988 sites
2025-08-01 15:15:22,803 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=9.102, H2=3.043, H3=1.268
2025-08-01 15:15:22,831 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1247 sites
2025-08-01 15:15:28,011 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.652, H2=-2.173, H3=0.801
2025-08-01 15:15:28,037 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 484 sites
2025-08-01 15:15:30,040 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=0.933, H2=4.650, H3=1.254
2025-08-01 15:15:30,067 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1249 sites
2025-08-01 15:15:35,266 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.377, H2=1.194, H3=3.374
2025-08-01 15:15:35,295 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1691 sites
2025-08-01 15:15:42,319 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.474, H2=-1.162, H3=-1.619
2025-08-01 15:15:42,348 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1867 sites
2025-08-01 15:15:50,076 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-4.169, H2=-2.385, H3=1.145
2025-08-01 15:15:50,104 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1658 sites
2025-08-01 15:15:56,999 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=19.131, H2=9.890, H3=-2.095
2025-08-01 15:15:57,030 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2877 sites
2025-08-01 15:16:09,008 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=10.982, H2=6.385, H3=4.693
2025-08-01 15:16:09,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2281 sites
2025-08-01 15:16:18,488 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=11.176, H2=-2.489, H3=-8.328
2025-08-01 15:16:18,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2801 sites
2025-08-01 15:16:30,157 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=7.335, H2=5.425, H3=3.639
2025-08-01 15:16:30,184 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1003 sites
2025-08-01 15:16:34,355 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=0.748, H2=3.185, H3=2.347
2025-08-01 15:16:34,384 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1855 sites
2025-08-01 15:16:42,099 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=1.685, H2=-1.913, H3=0.381
2025-08-01 15:16:42,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 734 sites
2025-08-01 15:16:45,180 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-0.651, H2=1.834, H3=-0.859
2025-08-01 15:16:45,206 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 900 sites
2025-08-01 15:16:48,943 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=4.802, H2=0.923, H3=0.907
2025-08-01 15:16:48,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 854 sites
2025-08-01 15:16:52,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-7.260, H2=-5.650, H3=-7.279
2025-08-01 15:16:52,542 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1306 sites
2025-08-01 15:16:57,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=9.511, H2=2.422, H3=-1.649
2025-08-01 15:16:57,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1218 sites
2025-08-01 15:17:03,054 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-2.781, H2=-4.001, H3=-3.725
2025-08-01 15:17:03,081 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1092 sites
2025-08-01 15:17:07,613 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=4.846, H2=2.749, H3=3.787
2025-08-01 15:17:07,642 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1877 sites
2025-08-01 15:17:15,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=3.997, H2=2.586, H3=3.077
2025-08-01 15:17:15,446 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1733 sites
2025-08-01 15:17:22,639 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=15.201, H2=5.154, H3=0.484
2025-08-01 15:17:22,668 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 2219 sites
2025-08-01 15:17:31,849 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=2.761, H2=5.711, H3=7.316
2025-08-01 15:17:31,874 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 265 sites
2025-08-01 15:17:32,969 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=5.446, H2=-5.565, H3=1.828
2025-08-01 15:17:32,995 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 384 sites
2025-08-01 15:17:34,574 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=2.928, H2=0.809, H3=-3.394
2025-08-01 15:17:34,599 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 341 sites
2025-08-01 15:17:36,002 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=-0.233, H2=0.903, H3=-1.624
2025-08-01 15:17:36,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 279 sites
2025-08-01 15:17:37,181 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=-0.161, H2=-2.653, H3=1.484
2025-08-01 15:17:37,205 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=37 has insufficient data
2025-08-01 15:17:37,206 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=38
2025-08-01 15:17:37,207 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38 -9223372036854775808]
2025-08-01 15:17:37,234 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:17:39,139 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:17:39,166 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:17:44,638 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:17:44,666 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 15:17:49,285 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 15:17:49,312 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1081 sites
2025-08-01 15:17:53,783 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=14.183, H2=-8.252, H3=-1.470
2025-08-01 15:17:53,812 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1482 sites
2025-08-01 15:18:00,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.970, H2=-0.843, H3=0.674
2025-08-01 15:18:00,032 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1804 sites
2025-08-01 15:18:07,606 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.802, H2=3.778, H3=-4.301
2025-08-01 15:18:07,633 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1010 sites
2025-08-01 15:18:11,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.366, H2=-0.643, H3=0.551
2025-08-01 15:18:11,865 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1076 sites
2025-08-01 15:18:16,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=13.505, H2=-0.945, H3=-1.204
2025-08-01 15:18:16,403 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2136 sites
2025-08-01 15:18:25,321 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=9.816, H2=-0.139, H3=1.636
2025-08-01 15:18:25,352 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3197 sites
2025-08-01 15:18:38,711 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=30.763, H2=-0.980, H3=-0.424
2025-08-01 15:18:38,740 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1458 sites
2025-08-01 15:18:44,815 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=24.774, H2=0.178, H3=0.317
2025-08-01 15:18:44,842 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1025 sites
2025-08-01 15:18:49,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.024, H2=-3.109, H3=-3.382
2025-08-01 15:18:49,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2988 sites
2025-08-01 15:19:01,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=9.102, H2=3.043, H3=1.268
2025-08-01 15:19:01,590 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1247 sites
2025-08-01 15:19:06,798 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.652, H2=-2.173, H3=0.801
2025-08-01 15:19:06,824 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 484 sites
2025-08-01 15:19:08,830 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=0.933, H2=4.650, H3=1.254
2025-08-01 15:19:08,858 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1249 sites
2025-08-01 15:19:14,059 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.377, H2=1.194, H3=3.374
2025-08-01 15:19:14,087 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1691 sites
2025-08-01 15:19:21,077 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.474, H2=-1.162, H3=-1.619
2025-08-01 15:19:21,106 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1867 sites
2025-08-01 15:19:28,815 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-4.169, H2=-2.385, H3=1.145
2025-08-01 15:19:28,843 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1658 sites
2025-08-01 15:19:35,677 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=19.131, H2=9.890, H3=-2.095
2025-08-01 15:19:35,708 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2877 sites
2025-08-01 15:19:47,675 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=10.982, H2=6.385, H3=4.693
2025-08-01 15:19:47,705 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2281 sites
2025-08-01 15:19:57,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=11.176, H2=-2.489, H3=-8.328
2025-08-01 15:19:57,169 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2801 sites
2025-08-01 15:20:08,800 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=7.335, H2=5.425, H3=3.639
2025-08-01 15:20:08,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1003 sites
2025-08-01 15:20:12,994 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=0.748, H2=3.185, H3=2.347
2025-08-01 15:20:13,019 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 558 sites
2025-08-01 15:20:15,346 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=4.872, H2=0.691, H3=1.733
2025-08-01 15:20:15,373 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1297 sites
2025-08-01 15:20:20,773 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-3.143, H2=-3.379, H3=-0.962
2025-08-01 15:20:20,799 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 734 sites
2025-08-01 15:20:23,824 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-0.690, H2=2.295, H3=-1.067
2025-08-01 15:20:23,851 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 900 sites
2025-08-01 15:20:27,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=5.460, H2=0.961, H3=0.801
2025-08-01 15:20:27,611 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 854 sites
2025-08-01 15:20:31,144 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-7.910, H2=-6.687, H3=-6.505
2025-08-01 15:20:31,172 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1306 sites
2025-08-01 15:20:36,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=11.831, H2=2.385, H3=-1.903
2025-08-01 15:20:36,589 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1218 sites
2025-08-01 15:20:41,624 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-2.688, H2=-4.615, H3=-3.064
2025-08-01 15:20:41,651 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1092 sites
2025-08-01 15:20:46,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=4.677, H2=2.552, H3=3.278
2025-08-01 15:20:46,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1877 sites
2025-08-01 15:20:53,987 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=3.843, H2=2.287, H3=2.872
2025-08-01 15:20:54,015 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1733 sites
2025-08-01 15:21:01,182 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=14.665, H2=6.307, H3=0.167
2025-08-01 15:21:01,212 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 2219 sites
2025-08-01 15:21:10,404 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=2.406, H2=6.665, H3=9.613
2025-08-01 15:21:10,430 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 265 sites
2025-08-01 15:21:11,526 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=6.667, H2=-6.345, H3=1.415
2025-08-01 15:21:11,552 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 384 sites
2025-08-01 15:21:13,137 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=2.474, H2=0.862, H3=-2.602
2025-08-01 15:21:13,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 341 sites
2025-08-01 15:21:14,573 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=0.072, H2=0.773, H3=-2.365
2025-08-01 15:21:14,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 279 sites
2025-08-01 15:21:15,748 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=-0.089, H2=-2.745, H3=1.643
2025-08-01 15:21:15,773 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=38 has insufficient data
2025-08-01 15:21:15,773 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=39
2025-08-01 15:21:15,775 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
 -9223372036854775808]
2025-08-01 15:21:15,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:21:17,708 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:21:17,736 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:21:23,204 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:21:23,231 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1112 sites
2025-08-01 15:21:27,873 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=13.562, H2=-1.821, H3=0.881
2025-08-01 15:21:27,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1081 sites
2025-08-01 15:21:32,385 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=14.183, H2=-8.252, H3=-1.470
2025-08-01 15:21:32,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1482 sites
2025-08-01 15:21:38,567 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.970, H2=-0.843, H3=0.674
2025-08-01 15:21:38,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1804 sites
2025-08-01 15:21:46,099 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.802, H2=3.778, H3=-4.301
2025-08-01 15:21:46,127 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1010 sites
2025-08-01 15:21:50,324 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.366, H2=-0.643, H3=0.551
2025-08-01 15:21:50,352 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1076 sites
2025-08-01 15:21:54,834 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=13.505, H2=-0.945, H3=-1.204
2025-08-01 15:21:54,863 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2136 sites
2025-08-01 15:22:03,715 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=9.816, H2=-0.139, H3=1.636
2025-08-01 15:22:03,746 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3197 sites
2025-08-01 15:22:16,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=30.763, H2=-0.980, H3=-0.424
2025-08-01 15:22:17,011 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1458 sites
2025-08-01 15:22:23,034 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=24.774, H2=0.178, H3=0.317
2025-08-01 15:22:23,061 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1025 sites
2025-08-01 15:22:27,298 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.024, H2=-3.109, H3=-3.382
2025-08-01 15:22:27,329 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2988 sites
2025-08-01 15:22:39,745 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=9.102, H2=3.043, H3=1.268
2025-08-01 15:22:39,772 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1247 sites
2025-08-01 15:22:44,970 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.652, H2=-2.173, H3=0.801
2025-08-01 15:22:44,996 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 484 sites
2025-08-01 15:22:46,999 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=0.933, H2=4.650, H3=1.254
2025-08-01 15:22:47,026 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1249 sites
2025-08-01 15:22:52,208 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.377, H2=1.194, H3=3.374
2025-08-01 15:22:52,236 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1691 sites
2025-08-01 15:22:59,272 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.474, H2=-1.162, H3=-1.619
2025-08-01 15:22:59,300 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1867 sites
2025-08-01 15:23:07,030 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-4.169, H2=-2.385, H3=1.145
2025-08-01 15:23:07,058 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1658 sites
2025-08-01 15:23:13,968 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=19.131, H2=9.890, H3=-2.095
2025-08-01 15:23:13,999 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2877 sites
2025-08-01 15:23:25,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=10.982, H2=6.385, H3=4.693
2025-08-01 15:23:26,025 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2281 sites
2025-08-01 15:23:35,490 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=11.176, H2=-2.489, H3=-8.328
2025-08-01 15:23:35,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1471 sites
2025-08-01 15:23:41,642 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.926, H2=2.435, H3=1.384
2025-08-01 15:23:41,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1330 sites
2025-08-01 15:23:47,187 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=1.569, H2=3.204, H3=1.433
2025-08-01 15:23:47,214 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1003 sites
2025-08-01 15:23:51,422 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=0.711, H2=2.786, H3=2.952
2025-08-01 15:23:51,448 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 558 sites
2025-08-01 15:23:53,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=5.228, H2=0.266, H3=1.533
2025-08-01 15:23:53,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1297 sites
2025-08-01 15:23:59,277 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-3.334, H2=-3.256, H3=-1.026
2025-08-01 15:23:59,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 734 sites
2025-08-01 15:24:02,382 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.325, H2=1.825, H3=-0.881
2025-08-01 15:24:02,409 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 900 sites
2025-08-01 15:24:06,206 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=5.151, H2=1.047, H3=0.782
2025-08-01 15:24:06,232 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 854 sites
2025-08-01 15:24:09,794 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-7.766, H2=-6.615, H3=-7.560
2025-08-01 15:24:09,822 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1306 sites
2025-08-01 15:24:15,258 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=10.551, H2=2.149, H3=-1.681
2025-08-01 15:24:15,285 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1218 sites
2025-08-01 15:24:20,364 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-2.865, H2=-3.997, H3=-2.696
2025-08-01 15:24:20,391 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1092 sites
2025-08-01 15:24:24,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=5.049, H2=2.144, H3=2.720
2025-08-01 15:24:25,008 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1877 sites
2025-08-01 15:24:32,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=3.939, H2=3.145, H3=2.479
2025-08-01 15:24:32,821 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1733 sites
2025-08-01 15:24:40,011 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=12.980, H2=5.498, H3=0.208
2025-08-01 15:24:40,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 2219 sites
2025-08-01 15:24:49,270 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=2.658, H2=6.209, H3=11.039
2025-08-01 15:24:49,296 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 265 sites
2025-08-01 15:24:50,405 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=5.457, H2=-5.332, H3=1.893
2025-08-01 15:24:50,430 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 384 sites
2025-08-01 15:24:52,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=2.355, H2=0.711, H3=-3.346
2025-08-01 15:24:52,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 341 sites
2025-08-01 15:24:53,460 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=0.038, H2=0.954, H3=-1.931
2025-08-01 15:24:53,486 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 279 sites
2025-08-01 15:24:54,641 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=-0.137, H2=-2.380, H3=1.469
2025-08-01 15:24:54,667 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=39 has insufficient data
2025-08-01 15:24:54,667 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=40
2025-08-01 15:24:54,669 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40 -9223372036854775808]
2025-08-01 15:24:54,695 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:24:56,602 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:24:56,630 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:25:02,075 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:25:02,101 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 358 sites
2025-08-01 15:25:03,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=1.558, H2=-2.692, H3=-2.648
2025-08-01 15:25:03,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 754 sites
2025-08-01 15:25:06,755 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.298, H2=-1.490, H3=0.814
2025-08-01 15:25:06,782 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1081 sites
2025-08-01 15:25:11,278 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.007, H2=-7.243, H3=-1.358
2025-08-01 15:25:11,307 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1482 sites
2025-08-01 15:25:17,479 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.358, H2=-0.335, H3=0.878
2025-08-01 15:25:17,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1804 sites
2025-08-01 15:25:25,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=9.439, H2=3.805, H3=-4.612
2025-08-01 15:25:25,044 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1010 sites
2025-08-01 15:25:29,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.080, H2=-0.782, H3=0.178
2025-08-01 15:25:29,265 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1076 sites
2025-08-01 15:25:33,713 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.475, H2=-1.334, H3=-1.139
2025-08-01 15:25:33,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2136 sites
2025-08-01 15:25:42,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.441, H2=0.038, H3=1.623
2025-08-01 15:25:42,616 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3197 sites
2025-08-01 15:25:55,896 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=27.421, H2=-1.034, H3=-0.133
2025-08-01 15:25:55,924 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1458 sites
2025-08-01 15:26:01,956 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=28.732, H2=0.264, H3=0.168
2025-08-01 15:26:01,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1025 sites
2025-08-01 15:26:06,228 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.517, H2=-3.934, H3=-3.177
2025-08-01 15:26:06,258 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2988 sites
2025-08-01 15:26:18,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=10.992, H2=2.456, H3=1.754
2025-08-01 15:26:18,673 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1247 sites
2025-08-01 15:26:23,857 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.550, H2=-1.924, H3=0.635
2025-08-01 15:26:23,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 484 sites
2025-08-01 15:26:25,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=0.685, H2=7.271, H3=1.156
2025-08-01 15:26:25,910 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1249 sites
2025-08-01 15:26:31,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.271, H2=0.956, H3=3.664
2025-08-01 15:26:31,117 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1691 sites
2025-08-01 15:26:38,121 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-0.840, H2=-1.050, H3=-1.202
2025-08-01 15:26:38,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1867 sites
2025-08-01 15:26:45,902 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-3.684, H2=-2.904, H3=0.997
2025-08-01 15:26:45,929 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1658 sites
2025-08-01 15:26:52,825 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=20.717, H2=9.051, H3=-2.007
2025-08-01 15:26:52,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2877 sites
2025-08-01 15:27:04,810 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=9.820, H2=7.181, H3=3.860
2025-08-01 15:27:04,840 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2281 sites
2025-08-01 15:27:14,299 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=11.603, H2=-2.343, H3=-7.018
2025-08-01 15:27:14,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1471 sites
2025-08-01 15:27:20,409 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=5.359, H2=2.567, H3=1.139
2025-08-01 15:27:20,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1330 sites
2025-08-01 15:27:25,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=1.805, H2=3.205, H3=2.062
2025-08-01 15:27:25,986 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1003 sites
2025-08-01 15:27:30,131 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=0.691, H2=2.553, H3=2.182
2025-08-01 15:27:30,157 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 558 sites
2025-08-01 15:27:32,459 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=4.221, H2=0.467, H3=1.825
2025-08-01 15:27:32,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1297 sites
2025-08-01 15:27:37,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-2.794, H2=-2.855, H3=-0.914
2025-08-01 15:27:37,910 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 734 sites
2025-08-01 15:27:40,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-0.266, H2=2.191, H3=-0.701
2025-08-01 15:27:40,984 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 900 sites
2025-08-01 15:27:44,716 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=5.365, H2=0.805, H3=0.767
2025-08-01 15:27:44,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 854 sites
2025-08-01 15:27:48,274 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-7.397, H2=-5.877, H3=-6.369
2025-08-01 15:27:48,301 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1306 sites
2025-08-01 15:27:53,732 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=11.054, H2=2.558, H3=-1.500
2025-08-01 15:27:53,759 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1218 sites
2025-08-01 15:27:58,841 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-2.871, H2=-3.667, H3=-3.034
2025-08-01 15:27:58,867 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1092 sites
2025-08-01 15:28:03,404 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=5.168, H2=2.458, H3=2.504
2025-08-01 15:28:03,432 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1877 sites
2025-08-01 15:28:11,225 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=3.812, H2=3.082, H3=2.943
2025-08-01 15:28:11,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1733 sites
2025-08-01 15:28:18,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=13.429, H2=5.628, H3=0.225
2025-08-01 15:28:18,533 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 2219 sites
2025-08-01 15:28:27,732 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=2.249, H2=4.653, H3=7.864
2025-08-01 15:28:27,757 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 265 sites
2025-08-01 15:28:28,862 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=5.167, H2=-6.009, H3=1.499
2025-08-01 15:28:28,888 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 384 sites
2025-08-01 15:28:30,484 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=2.864, H2=0.897, H3=-2.947
2025-08-01 15:28:30,510 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 341 sites
2025-08-01 15:28:31,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=0.023, H2=1.250, H3=-1.713
2025-08-01 15:28:31,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 40 with 279 sites
2025-08-01 15:28:33,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 40: H1=-0.330, H2=-2.674, H3=1.644
2025-08-01 15:28:33,122 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=40 has insufficient data
2025-08-01 15:28:33,122 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=41
2025-08-01 15:28:33,124 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40                   41 -9223372036854775808]
2025-08-01 15:28:33,151 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:28:35,060 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:28:35,088 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:28:40,616 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:28:40,642 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 358 sites
2025-08-01 15:28:42,152 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=1.558, H2=-2.692, H3=-2.648
2025-08-01 15:28:42,179 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 754 sites
2025-08-01 15:28:45,357 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.298, H2=-1.490, H3=0.814
2025-08-01 15:28:45,385 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1081 sites
2025-08-01 15:28:49,932 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.007, H2=-7.243, H3=-1.358
2025-08-01 15:28:49,960 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1482 sites
2025-08-01 15:28:56,186 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.358, H2=-0.335, H3=0.878
2025-08-01 15:28:56,215 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1804 sites
2025-08-01 15:29:03,739 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=9.439, H2=3.805, H3=-4.612
2025-08-01 15:29:03,767 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1010 sites
2025-08-01 15:29:08,029 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.080, H2=-0.782, H3=0.178
2025-08-01 15:29:08,056 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1076 sites
2025-08-01 15:29:12,587 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.475, H2=-1.334, H3=-1.139
2025-08-01 15:29:12,616 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2136 sites
2025-08-01 15:29:21,575 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.441, H2=0.038, H3=1.623
2025-08-01 15:29:21,606 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3197 sites
2025-08-01 15:29:34,966 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=27.421, H2=-1.034, H3=-0.133
2025-08-01 15:29:34,994 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1458 sites
2025-08-01 15:29:41,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=28.732, H2=0.264, H3=0.168
2025-08-01 15:29:41,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1025 sites
2025-08-01 15:29:45,353 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.517, H2=-3.934, H3=-3.177
2025-08-01 15:29:45,383 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2988 sites
2025-08-01 15:29:57,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=10.992, H2=2.456, H3=1.754
2025-08-01 15:29:57,818 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1247 sites
2025-08-01 15:30:02,993 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.550, H2=-1.924, H3=0.635
2025-08-01 15:30:03,019 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 484 sites
2025-08-01 15:30:05,025 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=0.685, H2=7.271, H3=1.156
2025-08-01 15:30:05,053 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1249 sites
2025-08-01 15:30:10,251 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.271, H2=0.956, H3=3.664
2025-08-01 15:30:10,279 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1691 sites
2025-08-01 15:30:17,254 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-0.840, H2=-1.050, H3=-1.202
2025-08-01 15:30:17,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1867 sites
2025-08-01 15:30:25,021 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-3.684, H2=-2.904, H3=0.997
2025-08-01 15:30:25,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1658 sites
2025-08-01 15:30:31,917 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=20.717, H2=9.051, H3=-2.007
2025-08-01 15:30:31,946 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1715 sites
2025-08-01 15:30:39,073 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=14.476, H2=6.174, H3=3.910
2025-08-01 15:30:39,101 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1162 sites
2025-08-01 15:30:43,920 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-3.151, H2=-0.156, H3=-1.796
2025-08-01 15:30:43,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2281 sites
2025-08-01 15:30:53,438 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=10.682, H2=-1.983, H3=-7.304
2025-08-01 15:30:53,466 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1471 sites
2025-08-01 15:30:59,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=6.662, H2=2.757, H3=1.627
2025-08-01 15:30:59,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1330 sites
2025-08-01 15:31:05,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=1.493, H2=2.798, H3=1.467
2025-08-01 15:31:05,125 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1003 sites
2025-08-01 15:31:09,281 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=0.457, H2=2.774, H3=2.411
2025-08-01 15:31:09,307 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 558 sites
2025-08-01 15:31:11,624 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=5.145, H2=0.516, H3=1.528
2025-08-01 15:31:11,651 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1297 sites
2025-08-01 15:31:17,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-2.442, H2=-3.356, H3=-0.726
2025-08-01 15:31:17,076 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 734 sites
2025-08-01 15:31:20,127 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-0.543, H2=1.770, H3=-0.967
2025-08-01 15:31:20,154 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 900 sites
2025-08-01 15:31:23,887 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=5.163, H2=0.871, H3=0.842
2025-08-01 15:31:23,913 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 854 sites
2025-08-01 15:31:27,465 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-7.079, H2=-6.258, H3=-5.930
2025-08-01 15:31:27,492 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1306 sites
2025-08-01 15:31:32,945 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=13.167, H2=2.025, H3=-1.633
2025-08-01 15:31:32,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1218 sites
2025-08-01 15:31:38,047 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=-2.613, H2=-4.657, H3=-2.877
2025-08-01 15:31:38,074 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1092 sites
2025-08-01 15:31:42,624 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=5.241, H2=2.556, H3=3.418
2025-08-01 15:31:42,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1877 sites
2025-08-01 15:31:50,446 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=3.735, H2=2.635, H3=4.165
2025-08-01 15:31:50,474 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1733 sites
2025-08-01 15:31:57,709 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=11.566, H2=5.410, H3=0.528
2025-08-01 15:31:57,738 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 2219 sites
2025-08-01 15:32:06,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=3.407, H2=6.074, H3=10.522
2025-08-01 15:32:07,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 265 sites
2025-08-01 15:32:08,109 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=6.922, H2=-6.172, H3=2.253
2025-08-01 15:32:08,134 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 384 sites
2025-08-01 15:32:09,728 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=2.792, H2=1.218, H3=-2.683
2025-08-01 15:32:09,754 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 40 with 341 sites
2025-08-01 15:32:11,171 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 40: H1=-0.132, H2=1.022, H3=-1.947
2025-08-01 15:32:11,196 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 41 with 279 sites
2025-08-01 15:32:12,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 41: H1=-0.272, H2=-2.177, H3=1.476
2025-08-01 15:32:12,380 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=41 has insufficient data
2025-08-01 15:32:12,380 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=42
2025-08-01 15:32:12,381 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40                   41                   42
 -9223372036854775808]
2025-08-01 15:32:12,408 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 460 sites
2025-08-01 15:32:14,320 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=9.079, H2=-5.697, H3=-4.578
2025-08-01 15:32:14,348 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1315 sites
2025-08-01 15:32:19,827 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.803, H2=1.359, H3=-1.424
2025-08-01 15:32:19,853 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 358 sites
2025-08-01 15:32:21,345 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=1.558, H2=-2.692, H3=-2.648
2025-08-01 15:32:21,371 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 754 sites
2025-08-01 15:32:24,507 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.298, H2=-1.490, H3=0.814
2025-08-01 15:32:24,535 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1081 sites
2025-08-01 15:32:29,021 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.007, H2=-7.243, H3=-1.358
2025-08-01 15:32:29,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1482 sites
2025-08-01 15:32:35,211 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.358, H2=-0.335, H3=0.878
2025-08-01 15:32:35,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1804 sites
2025-08-01 15:32:42,753 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=9.439, H2=3.805, H3=-4.612
2025-08-01 15:32:42,781 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1010 sites
2025-08-01 15:32:47,006 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.080, H2=-0.782, H3=0.178
2025-08-01 15:32:47,033 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1076 sites
2025-08-01 15:32:51,570 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.475, H2=-1.334, H3=-1.139
2025-08-01 15:32:51,597 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 667 sites
2025-08-01 15:32:54,398 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.788, H2=0.159, H3=-0.076
2025-08-01 15:32:54,426 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1469 sites
2025-08-01 15:33:00,596 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=9.088, H2=-0.827, H3=1.898
2025-08-01 15:33:00,627 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3197 sites
2025-08-01 15:33:14,051 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=33.614, H2=-0.559, H3=0.127
2025-08-01 15:33:14,079 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1458 sites
2025-08-01 15:33:20,173 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=22.993, H2=0.177, H3=-0.185
2025-08-01 15:33:20,201 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1025 sites
