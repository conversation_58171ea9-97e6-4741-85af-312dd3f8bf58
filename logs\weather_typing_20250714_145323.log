2025-07-14 14:53:23,104 - __main__ - INFO - run_heterogeneity_analysis.py:525 - Starting heterogeneity analysis
2025-07-14 14:53:23,104 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-14 14:53:23,104 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Cluster data: regionalization_w_WT_3_hierarchical/CI_results_hierarchical.nc
2025-07-14 14:53:23,104 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Output directory: heterogeneity_results/regionalization_w_WT_3_hierarchical
2025-07-14 14:53:23,105 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Maximum clusters: 125
2025-07-14 14:53:23,105 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Number of simulations: 50
2025-07-14 14:53:23,105 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Remap clusters: True
2025-07-14 14:53:23,106 - __main__ - INFO - run_heterogeneity_analysis.py:540 - Remapping clusters from 1D to 2D grid format
2025-07-14 14:53:23,110 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_3_hierarchical/CI_results_hierarchical.nc
2025-07-14 14:53:23,111 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-14 14:53:25,073 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-14 14:53:26,615 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-14 14:53:26,694 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-14 14:53:26,731 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-14 14:53:26,734 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-14 14:53:26,777 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-14 14:53:26,855 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-14 14:53:26,906 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-14 14:53:26,944 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-14 14:53:27,116 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-14 14:53:27,156 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-14 14:53:27,159 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-14 14:53:27,304 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-14 14:53:27,306 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-14 14:53:27,308 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-14 14:53:27,311 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-14 14:53:27,529 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-14 14:53:27,531 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-14 14:53:27,533 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-14 14:53:27,539 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-14 14:53:27,539 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-14 14:53:27,541 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-14 14:53:27,542 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-14 14:53:27,542 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-14 14:53:27,545 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-14 14:53:27,546 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-14 14:53:27,548 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-14 14:53:27,550 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-14 14:53:27,552 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-14 14:53:27,554 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-14 14:53:27,556 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-14 14:53:27,559 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-14 14:53:27,561 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-14 14:53:27,563 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-14 14:53:27,564 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-14 14:53:27,566 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-14 14:53:27,568 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-14 14:53:27,571 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-14 14:53:27,573 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-14 14:53:27,573 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-14 14:53:27,573 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-14 14:53:27,574 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-07-14 14:53:27,575 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-14 14:53:27,592 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-14 14:53:27,592 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-14 14:53:27,773 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-14 14:53:27,773 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-14 14:53:27,776 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-14 14:53:27,776 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-14 14:53:27,779 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-14 14:53:27,779 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-14 14:53:27,781 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-14 14:53:27,782 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-14 14:53:27,785 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-14 14:53:27,785 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-14 14:53:27,788 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-14 14:53:27,788 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-14 14:53:27,791 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-14 14:53:27,791 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-14 14:53:27,794 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-14 14:53:27,794 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-14 14:53:27,797 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-14 14:53:27,797 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-14 14:53:27,800 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-14 14:53:27,801 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-14 14:53:27,804 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-14 14:53:27,804 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-14 14:53:27,806 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-14 14:53:27,807 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-14 14:53:27,810 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-14 14:53:27,810 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-14 14:53:27,813 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-14 14:53:27,813 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-14 14:53:27,815 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-14 14:53:27,815 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-14 14:53:27,818 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-14 14:53:27,819 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-14 14:53:27,822 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-14 14:53:27,822 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-14 14:53:27,825 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-14 14:53:27,825 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-14 14:53:27,828 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-14 14:53:27,828 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-14 14:53:27,830 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-14 14:53:27,831 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-14 14:53:27,834 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-14 14:53:27,834 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-14 14:53:27,837 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-14 14:53:27,837 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-14 14:53:27,840 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-14 14:53:27,840 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-14 14:53:27,843 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-14 14:53:27,843 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-14 14:53:27,846 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-14 14:53:27,847 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-14 14:53:27,849 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-14 14:53:27,850 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-14 14:53:27,853 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-14 14:53:27,853 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-14 14:53:27,855 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-14 14:53:27,855 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-14 14:53:27,858 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-14 14:53:27,858 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-14 14:53:27,861 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-14 14:53:27,862 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-14 14:53:27,865 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-14 14:53:27,865 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-14 14:53:27,868 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-14 14:53:27,868 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-14 14:53:27,870 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-14 14:53:27,871 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-14 14:53:27,874 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-14 14:53:27,874 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-14 14:53:27,877 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-14 14:53:27,877 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-14 14:53:27,879 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-14 14:53:27,880 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-14 14:53:27,883 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-14 14:53:27,883 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-14 14:53:27,886 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-14 14:53:27,886 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-14 14:53:28,059 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-14 14:53:28,059 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-14 14:53:28,062 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-14 14:53:28,062 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-14 14:53:28,065 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-14 14:53:28,065 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-14 14:53:28,068 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-14 14:53:28,068 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-14 14:53:28,071 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-14 14:53:28,071 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-14 14:53:28,074 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-14 14:53:28,074 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-14 14:53:28,077 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-14 14:53:28,077 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-14 14:53:28,080 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-14 14:53:28,081 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-14 14:53:28,084 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-14 14:53:28,084 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-14 14:53:28,087 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-14 14:53:28,087 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-14 14:53:28,089 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-14 14:53:28,090 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-14 14:53:28,092 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-14 14:53:28,093 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-14 14:53:28,096 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-14 14:53:28,096 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-14 14:53:28,099 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-14 14:53:28,099 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-14 14:53:28,102 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-14 14:53:28,103 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-14 14:53:28,106 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-14 14:53:28,106 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-14 14:53:28,109 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-14 14:53:28,109 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-14 14:53:28,112 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-14 14:53:28,112 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-14 14:53:28,114 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-14 14:53:28,114 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-14 14:53:28,117 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-14 14:53:28,118 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-14 14:53:28,121 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-14 14:53:28,121 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-14 14:53:28,123 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-14 14:53:28,123 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-14 14:53:28,126 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-14 14:53:28,127 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-14 14:53:28,130 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-14 14:53:28,130 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-07-14 14:53:28,133 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-07-14 14:53:28,134 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-07-14 14:53:28,137 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-07-14 14:53:28,137 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-07-14 14:53:28,139 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-07-14 14:53:28,139 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-07-14 14:53:28,142 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-07-14 14:53:28,143 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-07-14 14:53:28,146 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-07-14 14:53:28,146 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-07-14 14:53:28,148 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-07-14 14:53:28,149 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-07-14 14:53:28,152 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-07-14 14:53:28,152 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-07-14 14:53:28,156 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-07-14 14:53:28,156 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-07-14 14:53:28,159 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-07-14 14:53:28,159 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-07-14 14:53:28,163 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-07-14 14:53:28,163 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-07-14 14:53:28,166 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-07-14 14:53:28,166 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-07-14 14:53:28,170 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-07-14 14:53:28,170 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-07-14 14:53:28,173 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-07-14 14:53:28,173 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-07-14 14:53:28,176 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-07-14 14:53:28,176 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-07-14 14:53:28,180 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-07-14 14:53:28,180 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-07-14 14:53:28,183 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-07-14 14:53:28,183 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-07-14 14:53:28,494 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-07-14 14:53:28,521 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-07-14 14:53:28,525 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-07-14 14:53:28,525 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-07-14 14:53:28,527 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-07-14 14:53:28,527 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-07-14 14:53:28,530 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-07-14 14:53:28,530 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-07-14 14:53:28,533 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-07-14 14:53:28,533 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-07-14 14:53:28,536 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-07-14 14:53:28,536 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-07-14 14:53:28,539 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-07-14 14:53:28,539 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-07-14 14:53:28,542 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-07-14 14:53:28,542 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-07-14 14:53:28,545 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-07-14 14:53:28,545 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-07-14 14:53:28,548 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-07-14 14:53:28,548 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-07-14 14:53:28,550 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-07-14 14:53:28,550 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-07-14 14:53:28,553 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-07-14 14:53:28,553 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-07-14 14:53:28,556 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-07-14 14:53:28,556 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-07-14 14:53:28,559 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-07-14 14:53:28,559 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-07-14 14:53:28,562 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-07-14 14:53:28,562 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-07-14 14:53:28,565 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-07-14 14:53:28,565 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-07-14 14:53:28,568 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-07-14 14:53:28,569 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-07-14 14:53:28,571 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-07-14 14:53:28,572 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-07-14 14:53:28,574 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-07-14 14:53:28,574 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-07-14 14:53:28,577 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-07-14 14:53:28,577 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-07-14 14:53:28,580 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-07-14 14:53:28,580 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-07-14 14:53:28,583 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-07-14 14:53:28,583 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-07-14 14:53:28,586 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-07-14 14:53:28,586 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-07-14 14:53:28,589 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-07-14 14:53:28,589 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-07-14 14:53:28,592 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-07-14 14:53:28,592 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-07-14 14:53:28,595 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-07-14 14:53:28,595 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-07-14 14:53:28,598 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-07-14 14:53:28,598 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-07-14 14:53:28,600 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-07-14 14:53:28,601 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-07-14 14:53:28,603 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-07-14 14:53:28,604 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-07-14 14:53:28,606 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-07-14 14:53:28,607 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-07-14 14:53:28,609 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-07-14 14:53:28,610 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-07-14 14:53:28,613 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-07-14 14:53:28,613 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-07-14 14:53:28,616 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-07-14 14:53:28,616 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-07-14 14:53:28,619 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-07-14 14:53:28,619 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-07-14 14:53:28,621 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-07-14 14:53:28,621 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-07-14 14:53:28,624 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-07-14 14:53:28,624 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-07-14 14:53:28,627 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-07-14 14:53:28,627 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-07-14 14:53:28,630 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-07-14 14:53:28,630 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-07-14 14:53:28,633 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-07-14 14:53:28,633 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-07-14 14:53:28,635 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-07-14 14:53:28,635 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-07-14 14:53:28,638 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-07-14 14:53:28,638 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-07-14 14:53:28,641 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-07-14 14:53:28,641 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-07-14 14:53:28,644 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-07-14 14:53:28,644 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-07-14 14:53:28,647 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-07-14 14:53:28,647 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-07-14 14:53:28,650 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-07-14 14:53:28,650 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-07-14 14:53:28,653 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-07-14 14:53:28,653 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_3_hierarchical/CI_results_hierarchical_remap.nc
