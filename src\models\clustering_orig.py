# -*- coding: utf-8 -*-
import os
import numpy as np
import xarray as xr
import pandas as pd
import matplotlib.pyplot as plt
import logging
from .kmeans_utils import kmeans_ci, ar1rand
from .hierarchical_utils import hierarchical_ci, plot_dendrogram, run_hierarchical_with_linkage_matrix
from joblib import Parallel, delayed


logger = logging.getLogger(__name__)

class EOFClusteringAnalyzer:
    """
    Class for analyzing EOF-based clustering results with support for both K-means and hierarchical clustering.
    """
    def __init__(self, tmpu, outdir, maxclust=40, nsim=100, method='kmeans'):
        """
        Initialize the EOF clustering analyzer.

        Parameters:
        -----------
        tmpu : numpy.ndarray
            Input data matrix
        outdir : str
            Output directory
        maxclust : int, optional
            Maximum number of clusters to consider
        nsim : int, optional
            Number of simulations
        method : str, optional
            Clustering method ('kmeans' or 'hierarchical')
        """
        self.tmpu = tmpu
        self.outdir = outdir
        self.maxclust = maxclust
        self.nsim = nsim
        self.method = method.lower()
        self.nr, self.nc = tmpu.shape
        self.CI = None
        self.rsims = None
        self.CIcis = None
        self.citop = None
        self.cibot = None

        # Validate clustering method
        if self.method not in ['kmeans', 'hierarchical']:
            raise ValueError("Method must be either 'kmeans' or 'hierarchical'")

        if not os.path.exists(self.outdir):
            os.makedirs(self.outdir)
            logger.info(f"Created output directory: {self.outdir}")

        logger.info(f"Initialized clustering analyzer with method: {self.method}")

    def load_or_compute_ci(self):
        """
        Load or compute the classifiability index for different cluster counts.
        """
        ci_path = os.path.join(self.outdir, f'CI_results_{self.method}.nc')
        if os.path.exists(ci_path):
            logger.info(f"Loading existing CI results from {ci_path}")
            tmp = xr.open_dataset(ci_path)
            self.CI = tmp.CI
        else:
            logger.info(f"Computing CI for different cluster counts using {self.method}")
            k_over = np.zeros((self.nr, self.maxclust))
            ci_over = np.zeros(self.maxclust)

            # Choose clustering function based on method
            if self.method == 'kmeans':
                clustering_func = kmeans_ci
            else:  # hierarchical
                clustering_func = hierarchical_ci

            for n in range(2, self.maxclust + 1):
                logger.info(f'Computing k={n} using {self.method}')
                try:
                    # Use appropriate n_jobs based on method
                    n_jobs = 64 if self.method == 'kmeans' else 8
                    k, ci = clustering_func(self.tmpu, None, None, None, n, 100, n_jobs)
                    k_over[:, n - 1] = np.squeeze(k)
                    ci_over[n - 1] = ci
                except Exception as e:
                    logger.error(f'Clustering failed for k={n} using {self.method}: {e}')
                    if self.method == 'hierarchical':
                        logger.info(f'Falling back to K-means for k={n}')
                        k, ci = kmeans_ci(self.tmpu, None, None, None, n, 100, 64)
                        k_over[:, n - 1] = np.squeeze(k)
                        ci_over[n - 1] = ci
                    else:
                        # If K-means also fails, use random assignment
                        logger.warning(f'Using random assignment for k={n}')
                        k = np.random.randint(0, n, size=self.tmpu.shape[0])
                        ci = 0.0
                        k_over[:, n - 1] = k
                        ci_over[n - 1] = ci

            colnames = [f'k={i}' for i in range(1, self.maxclust + 1)]
            k_df = pd.DataFrame(k_over, columns=colnames)
            ci_df = pd.DataFrame(ci_over, columns=['CI'])
            k_df['CI'] = ci_df
            k_xr = k_df.to_xarray()
            k_xr.to_netcdf(ci_path)
            logger.info(f"Saved CI results to {ci_path}")
            self.CI = ci_df

            # Generate dendrogram for hierarchical clustering
            if self.method == 'hierarchical':
                self._generate_dendrogram()

    def _generate_dendrogram(self):
        """
        Generate dendrogram for hierarchical clustering.
        """
        try:
            logger.info("Generating dendrogram for hierarchical clustering")
            # Use a representative clustering run to get linkage matrix
            _, _, linkage_matrix = run_hierarchical_with_linkage_matrix(
                self.tmpu, self.tmpu, 2, self.nc, 0, return_linkage=True
            )
            dendrogram_path = os.path.join(self.outdir, 'dendrogram.png')
            plot_dendrogram(linkage_matrix, dendrogram_path, self.maxclust)
        except Exception as e:
            logger.warning(f"Could not generate dendrogram: {e}")

    def plot_ci(self):
        """
        Plot the classifiability index values.
        """
        fig, ax = plt.subplots()
        ax.plot(self.CI)
        ax.set_ylabel('CI')
        ax.set_xlabel('Cluster')
        ax.set_title(f'CI Values ({self.method.title()} Clustering)')
        ax.set_xlim(1, self.maxclust)
        output_path = os.path.join(self.outdir, f'plot_ci_{self.method}.png')
        plt.savefig(output_path)
        plt.close(fig)
        logger.info(f"Saved CI plot to {output_path}")

    def generate_red_noise(self):
        """
        Generate red noise simulations.
        """
        random_path = os.path.join(self.outdir, 'random.nc')
        if os.path.exists(random_path):
            logger.info(f"Loading existing red noise simulations from {random_path}")
            tmp = xr.open_dataset(random_path)
            self.rsims = tmp.rsims.data
        else:
            logger.info('Creating random red noise series')
            self.rsims = np.zeros((self.nr, self.nc, self.nsim))
            for i in range(self.nc):
                p = ar1rand(self.tmpu[:, i], self.nsim)
                self.rsims[:, i, :] = p
            rsims_xr = xr.DataArray(self.rsims, dims=('days', 'variables', 'nsims'), name='rsims')
            rsims_xr.to_netcdf(random_path)
            logger.info(f"Saved red noise simulations to {random_path}")

    def ci_red_noise_test(self):
        """
        Perform red noise test for classifiability index.
        """
        logger.info(f"Performing red noise test for CI using {self.method}")
        self.CIcis = np.zeros((self.maxclust, self.nsim))
        CIci = np.zeros((1, self.nsim))
        self.citop = np.zeros((self.maxclust, 1))
        self.cibot = np.zeros((self.maxclust, 1))

        # Choose clustering function based on method
        if self.method == 'kmeans':
            clustering_func = kmeans_ci
        else:  # hierarchical
            clustering_func = hierarchical_ci

        for i in range(1, self.maxclust + 1):
            ci_file = os.path.join(self.outdir, f'CIci_{i}_{self.method}.nc')
            if not os.path.exists(ci_file):
                for j in range(self.nsim):
                    logger.info(f'Cluster {i} Simulation {j} ({self.method})')
                    sim = np.squeeze(self.rsims[:, :, j])
                    # Use appropriate n_jobs based on method
                    n_jobs = 64 if self.method == 'kmeans' else 8
                    _, CIci[0, j] = clustering_func(sim, None, None, None, i, 100, n_jobs)
                cici_xr = xr.DataArray(CIci, dims=('CI', 'nsim'), name='CIci')
                cici_xr.to_netcdf(ci_file)
                logger.info(f"Saved CI confidence intervals for k={i} ({self.method}) to {ci_file}")
            tmp = xr.open_dataset(ci_file)
            self.CIcis[i - 1, :] = tmp.CIci
            cisort = np.sort(self.CIcis[i - 1, :])
            self.citop[i - 1, 0] = cisort[int(.90 * self.nsim)]
            self.cibot[i - 1, 0] = cisort[0]

    def plot_ci_ci(self):
        """
        Plot the classifiability index with confidence intervals.
        """
        x = np.arange(1, self.maxclust + 1)
        fig, ax = plt.subplots()
        ax.plot(self.CI)
        ax.set_ylabel('CI')
        ax.set_xlabel('Cluster')
        ax.set_title(f'Classifiability Index ({self.method.title()} Clustering)')
        ax.set_xlim(1, self.maxclust)
        for a in range(self.maxclust):
            ax.plot((a + 1, a + 1), (self.citop[a, 0], self.cibot[a, 0]), 'red')
        output_path = os.path.join(self.outdir, f'plot_CIci_{self.method}.png')
        plt.savefig(output_path)
        plt.close(fig)
        logger.info(f"Saved CI with confidence intervals plot to {output_path}")

        fig, ax = plt.subplots()
        ax.plot(self.CI)
        ax.set_ylabel('CI')
        ax.set_xlabel('Cluster')
        ax.set_title(f'Classifiability Index ({self.method.title()} Clustering)')
        ax.set_xlim(1, self.maxclust)
        for a in range(self.maxclust):
            ax.plot((a + 1, a + 1), (self.citop[a, 0], self.cibot[a, 0]), 'red')
        ax.fill_between(x, np.squeeze(self.citop), np.squeeze(self.cibot), color='silver')
        output_path = os.path.join(self.outdir, f'plot_CIci_shaded_{self.method}.png')
        plt.savefig(output_path)
        plt.close(fig)
        logger.info(f"Saved CI with shaded confidence intervals plot to {output_path}")
