2025-07-12 09:25:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:525 - Starting heterogeneity analysis
2025-07-12 09:25:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-12 09:25:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Cluster data: regionalization_w_WT_6_hierarchical/CI_results_hierarchical.nc
2025-07-12 09:25:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Output directory: heterogeneity_results/regionalization_w_WT_6_hierarchical
2025-07-12 09:25:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Maximum clusters: 125
2025-07-12 09:25:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Number of simulations: 50
2025-07-12 09:25:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Remap clusters: True
2025-07-12 09:25:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:540 - Remapping clusters from 1D to 2D grid format
2025-07-12 09:25:13,311 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_6_hierarchical/CI_results_hierarchical.nc
2025-07-12 09:25:13,311 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-12 09:25:15,559 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-12 09:25:15,979 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-12 09:25:16,019 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-12 09:25:16,036 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-12 09:25:16,038 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-12 09:25:16,060 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-12 09:25:16,099 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-12 09:25:16,131 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-12 09:25:16,151 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-12 09:25:16,196 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-12 09:25:16,198 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-12 09:25:16,200 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-12 09:25:16,266 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-12 09:25:16,267 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-12 09:25:16,269 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-12 09:25:16,271 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-12 09:25:16,332 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-12 09:25:16,333 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-12 09:25:16,335 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-12 09:25:16,339 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-12 09:25:16,340 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-12 09:25:16,342 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-12 09:25:16,342 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-12 09:25:16,343 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-12 09:25:16,345 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-12 09:25:16,346 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-12 09:25:16,348 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-12 09:25:16,349 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-12 09:25:16,351 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-12 09:25:16,353 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-12 09:25:16,355 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-12 09:25:16,356 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-12 09:25:16,358 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-12 09:25:16,360 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-12 09:25:16,361 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-12 09:25:16,363 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-12 09:25:16,365 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-12 09:25:16,367 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-12 09:25:16,368 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-12 09:25:16,369 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-12 09:25:16,369 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-12 09:25:16,370 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-07-12 09:25:16,370 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-12 09:25:16,389 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-12 09:25:16,389 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-12 09:25:16,422 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-12 09:25:16,422 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-12 09:25:16,425 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-12 09:25:16,425 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-12 09:25:16,428 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-12 09:25:16,429 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-12 09:25:16,432 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-12 09:25:16,432 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-12 09:25:16,435 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-12 09:25:16,435 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-12 09:25:16,438 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-12 09:25:16,439 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-12 09:25:16,441 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-12 09:25:16,441 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-12 09:25:16,444 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-12 09:25:16,445 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-12 09:25:16,448 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-12 09:25:16,448 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-12 09:25:16,450 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-12 09:25:16,451 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-12 09:25:16,454 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-12 09:25:16,454 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-12 09:25:16,457 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-12 09:25:16,457 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-12 09:25:16,460 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-12 09:25:16,460 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-12 09:25:16,464 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-12 09:25:16,464 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-12 09:25:16,466 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-12 09:25:16,466 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-12 09:25:16,470 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-12 09:25:16,470 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-12 09:25:16,473 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-12 09:25:16,473 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-12 09:25:16,475 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-12 09:25:16,476 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-12 09:25:16,479 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-12 09:25:16,479 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-12 09:25:16,482 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-12 09:25:16,482 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-12 09:25:16,485 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-12 09:25:16,485 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-12 09:25:16,488 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-12 09:25:16,488 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-12 09:25:16,491 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-12 09:25:16,491 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-12 09:25:16,494 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-12 09:25:16,494 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-12 09:25:16,497 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-12 09:25:16,498 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-12 09:25:16,500 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-12 09:25:16,500 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-12 09:25:16,503 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-12 09:25:16,503 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-12 09:25:16,507 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-12 09:25:16,507 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-12 09:25:16,510 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-12 09:25:16,510 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-12 09:25:16,513 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-12 09:25:16,513 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-12 09:25:16,516 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-12 09:25:16,516 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-12 09:25:16,519 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-12 09:25:16,519 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-12 09:25:16,522 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-12 09:25:16,522 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-12 09:25:16,525 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-12 09:25:16,525 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-12 09:25:16,528 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-12 09:25:16,528 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-12 09:25:16,531 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-12 09:25:16,531 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-12 09:25:16,534 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-12 09:25:16,534 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-12 09:25:16,537 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-12 09:25:16,537 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-12 09:25:16,540 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-12 09:25:16,540 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-12 09:25:16,543 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-12 09:25:16,543 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-12 09:25:16,546 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-12 09:25:16,546 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-12 09:25:16,549 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-12 09:25:16,549 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-12 09:25:16,552 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-12 09:25:16,552 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-12 09:25:16,555 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-12 09:25:16,555 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-12 09:25:16,558 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-12 09:25:16,559 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-12 09:25:16,562 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-12 09:25:16,562 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-12 09:25:16,564 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-12 09:25:16,564 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-12 09:25:16,567 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-12 09:25:16,568 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-12 09:25:16,571 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-12 09:25:16,571 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-12 09:25:16,573 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-12 09:25:16,573 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-12 09:25:16,576 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-12 09:25:16,577 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-12 09:25:16,580 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-12 09:25:16,580 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-12 09:25:16,583 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-12 09:25:16,583 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-12 09:25:16,586 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-12 09:25:16,586 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-12 09:25:16,588 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-12 09:25:16,589 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-12 09:25:16,592 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-12 09:25:16,592 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-12 09:25:16,595 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-12 09:25:16,595 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-12 09:25:16,597 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-12 09:25:16,597 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-12 09:25:16,600 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-12 09:25:16,601 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-12 09:25:16,604 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-12 09:25:16,604 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-12 09:25:16,607 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-12 09:25:16,607 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-12 09:25:16,610 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-12 09:25:16,610 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
