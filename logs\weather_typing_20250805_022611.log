2025-08-05 02:26:11,842 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-08-05 02:26:11,842 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-05 02:26:11,842 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_15_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 02:26:11,842 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_15_hierarchical_singlevariable
2025-08-05 02:26:11,842 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-08-05 02:26:11,842 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-08-05 02:26:11,842 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-08-05 02:26:11,844 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-08-05 02:26:11,845 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_15_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 02:26:11,845 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-08-05 02:26:12,712 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-08-05 02:26:12,849 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-08-05 02:26:12,881 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-08-05 02:26:12,884 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-08-05 02:26:12,886 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-08-05 02:26:12,888 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-08-05 02:26:12,889 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-08-05 02:26:12,891 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-08-05 02:26:12,912 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-08-05 02:26:12,966 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-08-05 02:26:12,968 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-08-05 02:26:12,970 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-08-05 02:26:12,972 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-08-05 02:26:12,973 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-08-05 02:26:12,976 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-08-05 02:26:12,978 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-08-05 02:26:12,979 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-08-05 02:26:12,980 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-08-05 02:26:12,981 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-08-05 02:26:12,985 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-08-05 02:26:12,985 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-08-05 02:26:12,988 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-08-05 02:26:12,988 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-08-05 02:26:12,988 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-08-05 02:26:12,990 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-08-05 02:26:12,992 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-08-05 02:26:12,993 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-08-05 02:26:12,996 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-08-05 02:26:12,997 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-08-05 02:26:13,000 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-08-05 02:26:13,002 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-08-05 02:26:13,004 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-08-05 02:26:13,007 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-08-05 02:26:13,008 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-08-05 02:26:13,010 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-08-05 02:26:13,012 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-08-05 02:26:13,014 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-08-05 02:26:13,016 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-08-05 02:26:13,018 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-08-05 02:26:13,029 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-08-05 02:26:13,029 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-08-05 02:26:13,029 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-08-05 02:26:13,030 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-08-05 02:26:13,031 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-08-05 02:26:13,035 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-08-05 02:26:13,035 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-08-05 02:26:13,037 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-08-05 02:26:13,037 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-08-05 02:26:13,039 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-08-05 02:26:13,039 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-08-05 02:26:13,041 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-08-05 02:26:13,041 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-08-05 02:26:13,043 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-08-05 02:26:13,043 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-08-05 02:26:13,046 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-08-05 02:26:13,046 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-08-05 02:26:13,048 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-08-05 02:26:13,048 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-08-05 02:26:13,051 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-08-05 02:26:13,052 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-08-05 02:26:13,055 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-08-05 02:26:13,055 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-08-05 02:26:13,057 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-08-05 02:26:13,057 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-08-05 02:26:13,060 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-08-05 02:26:13,060 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-08-05 02:26:13,063 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-08-05 02:26:13,064 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-08-05 02:26:13,067 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-08-05 02:26:13,067 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-08-05 02:26:13,070 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-08-05 02:26:13,070 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-08-05 02:26:13,072 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-08-05 02:26:13,072 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-08-05 02:26:13,075 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-08-05 02:26:13,075 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-08-05 02:26:13,078 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-08-05 02:26:13,078 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-08-05 02:26:13,081 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-08-05 02:26:13,081 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-08-05 02:26:13,084 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-08-05 02:26:13,084 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-08-05 02:26:13,087 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-08-05 02:26:13,087 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-08-05 02:26:13,090 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-08-05 02:26:13,090 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-08-05 02:26:13,093 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-08-05 02:26:13,093 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-08-05 02:26:13,096 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-08-05 02:26:13,096 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-08-05 02:26:13,099 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-08-05 02:26:13,099 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-08-05 02:26:13,102 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-08-05 02:26:13,102 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-08-05 02:26:13,104 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-08-05 02:26:13,105 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-08-05 02:26:13,107 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-08-05 02:26:13,108 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-08-05 02:26:13,111 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-08-05 02:26:13,111 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-08-05 02:26:13,114 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-08-05 02:26:13,114 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-08-05 02:26:13,117 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-08-05 02:26:13,117 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-08-05 02:26:13,119 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-08-05 02:26:13,119 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-08-05 02:26:13,122 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-08-05 02:26:13,122 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-08-05 02:26:13,125 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-08-05 02:26:13,125 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-08-05 02:26:13,128 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-08-05 02:26:13,128 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-08-05 02:26:13,131 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-08-05 02:26:13,131 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-08-05 02:26:13,134 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-08-05 02:26:13,134 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-08-05 02:26:13,137 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-08-05 02:26:13,137 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-08-05 02:26:13,140 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-08-05 02:26:13,140 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-08-05 02:26:13,142 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-08-05 02:26:13,143 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-08-05 02:26:13,146 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-08-05 02:26:13,146 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-08-05 02:26:13,149 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-08-05 02:26:13,149 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-08-05 02:26:13,151 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-08-05 02:26:13,151 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-08-05 02:26:13,154 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-08-05 02:26:13,154 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-08-05 02:26:13,157 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-08-05 02:26:13,157 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-08-05 02:26:13,160 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-08-05 02:26:13,160 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-08-05 02:26:13,163 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-08-05 02:26:13,163 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-08-05 02:26:13,166 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-08-05 02:26:13,166 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-08-05 02:26:13,169 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-08-05 02:26:13,169 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-08-05 02:26:13,172 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-08-05 02:26:13,172 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-08-05 02:26:13,175 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-08-05 02:26:13,175 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-08-05 02:26:13,178 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-08-05 02:26:13,178 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-08-05 02:26:13,180 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-08-05 02:26:13,180 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-08-05 02:26:13,183 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-08-05 02:26:13,183 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-08-05 02:26:13,186 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-08-05 02:26:13,186 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-08-05 02:26:13,189 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-08-05 02:26:13,189 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-08-05 02:26:13,192 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-08-05 02:26:13,192 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-08-05 02:26:13,195 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-08-05 02:26:13,195 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-08-05 02:26:13,198 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-08-05 02:26:13,198 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-08-05 02:26:13,201 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-08-05 02:26:13,201 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-08-05 02:26:13,203 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-08-05 02:26:13,203 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-08-05 02:26:13,206 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-08-05 02:26:13,206 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-08-05 02:26:13,209 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-08-05 02:26:13,209 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-08-05 02:26:13,211 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-08-05 02:26:13,212 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-08-05 02:26:13,214 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-08-05 02:26:13,215 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-08-05 02:26:13,217 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-08-05 02:26:13,218 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-08-05 02:26:13,220 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-08-05 02:26:13,221 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-08-05 02:26:13,223 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-08-05 02:26:13,224 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-08-05 02:26:13,226 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-08-05 02:26:13,226 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-08-05 02:26:13,229 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-08-05 02:26:13,229 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-08-05 02:26:13,232 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-08-05 02:26:13,232 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-08-05 02:26:13,234 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-08-05 02:26:13,234 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-08-05 02:26:13,237 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-08-05 02:26:13,237 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-08-05 02:26:13,240 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-08-05 02:26:13,240 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-08-05 02:26:13,243 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-08-05 02:26:13,243 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-08-05 02:26:13,246 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-08-05 02:26:13,246 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-08-05 02:26:13,248 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-08-05 02:26:13,249 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-08-05 02:26:13,251 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-08-05 02:26:13,252 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-08-05 02:26:13,254 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-08-05 02:26:13,254 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-08-05 02:26:13,257 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-08-05 02:26:13,257 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-08-05 02:26:13,279 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-08-05 02:26:13,280 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-08-05 02:26:13,651 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-08-05 02:26:13,651 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-08-05 02:26:13,654 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-08-05 02:26:13,654 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-08-05 02:26:13,657 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-08-05 02:26:13,657 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-08-05 02:26:13,659 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-08-05 02:26:13,660 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-08-05 02:26:13,662 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-08-05 02:26:13,662 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-08-05 02:26:13,665 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-08-05 02:26:13,665 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-08-05 02:26:13,668 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-08-05 02:26:13,668 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-08-05 02:26:13,671 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-08-05 02:26:13,671 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-08-05 02:26:13,674 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-08-05 02:26:13,674 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-08-05 02:26:13,676 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-08-05 02:26:13,677 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-08-05 02:26:13,679 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-08-05 02:26:13,679 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-08-05 02:26:13,682 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-08-05 02:26:13,682 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-08-05 02:26:13,685 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-08-05 02:26:13,685 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-08-05 02:26:13,688 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-08-05 02:26:13,688 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-08-05 02:26:13,690 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-08-05 02:26:13,690 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-08-05 02:26:13,693 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-08-05 02:26:13,693 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-08-05 02:26:13,696 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-08-05 02:26:13,696 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-08-05 02:26:13,699 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-08-05 02:26:13,699 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-08-05 02:26:13,702 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-08-05 02:26:13,702 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-08-05 02:26:13,704 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-08-05 02:26:13,704 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-08-05 02:26:13,707 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-08-05 02:26:13,707 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-08-05 02:26:13,710 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-08-05 02:26:13,710 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-08-05 02:26:13,712 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-08-05 02:26:13,712 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-08-05 02:26:13,715 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-08-05 02:26:13,715 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-08-05 02:26:13,718 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-08-05 02:26:13,718 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-08-05 02:26:13,721 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-08-05 02:26:13,721 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-08-05 02:26:13,724 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-08-05 02:26:13,724 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-08-05 02:26:13,726 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-08-05 02:26:13,726 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-08-05 02:26:13,729 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-08-05 02:26:13,729 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-08-05 02:26:13,732 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-08-05 02:26:13,732 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-08-05 02:26:13,734 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-08-05 02:26:13,734 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-08-05 02:26:13,737 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-08-05 02:26:13,737 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-08-05 02:26:13,740 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-08-05 02:26:13,740 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-08-05 02:26:13,743 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-08-05 02:26:13,743 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-08-05 02:26:13,745 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-08-05 02:26:13,746 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-08-05 02:26:13,748 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-08-05 02:26:13,748 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-08-05 02:26:13,751 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-08-05 02:26:13,751 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-08-05 02:26:13,754 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-08-05 02:26:13,754 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-08-05 02:26:13,756 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-08-05 02:26:13,756 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-08-05 02:26:13,759 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-08-05 02:26:13,759 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-08-05 02:26:13,762 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-08-05 02:26:13,762 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-08-05 02:26:13,765 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-08-05 02:26:13,765 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-08-05 02:26:13,767 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-08-05 02:26:13,768 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-08-05 02:26:13,770 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-08-05 02:26:13,770 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-08-05 02:26:13,773 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-08-05 02:26:13,773 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_15_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-05 02:26:14,440 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-08-05 02:26:14,440 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-08-05 02:26:14,440 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-08-05 02:26:14,440 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_15_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 02:26:14,440 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_15_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-05 02:26:14,441 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 125 variables
2025-08-05 02:26:14,441 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125
2025-08-05 02:26:14,441 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-08-05 02:26:14,443 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_15_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-05 02:26:14,443 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-08-05 02:26:14,444 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-05 02:26:14,576 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-08-05 02:26:20,952 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_15_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-05 02:26:21,112 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-08-05 02:26:21,112 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-08-05 02:26:21,112 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-08-05 02:26:21,114 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-08-05 02:26:21,170 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17110 sites
2025-08-05 02:26:51,348 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=123.502, H2=0.747, H3=-0.681
2025-08-05 02:26:51,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 35249 sites
2025-08-05 02:27:53,805 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=87.635, H2=26.195, H3=8.826
2025-08-05 02:27:53,832 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-08-05 02:27:53,833 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-08-05 02:27:53,834 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-08-05 02:27:53,891 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17110 sites
2025-08-05 02:28:24,028 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=123.502, H2=0.747, H3=-0.681
2025-08-05 02:28:24,089 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 19315 sites
2025-08-05 02:28:58,189 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=51.565, H2=15.279, H3=3.445
2025-08-05 02:28:58,255 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 15934 sites
2025-08-05 02:29:26,202 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=78.440, H2=30.141, H3=8.637
2025-08-05 02:29:26,228 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-08-05 02:29:26,228 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-08-05 02:29:26,229 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-08-05 02:29:26,285 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17110 sites
2025-08-05 02:29:56,344 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=123.502, H2=0.747, H3=-0.681
2025-08-05 02:29:56,407 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 19315 sites
2025-08-05 02:30:30,396 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=51.565, H2=15.279, H3=3.445
2025-08-05 02:30:30,426 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1717 sites
2025-08-05 02:30:33,430 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=21.677, H2=0.667, H3=-2.380
2025-08-05 02:30:33,481 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 14217 sites
2025-08-05 02:30:58,526 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=45.425, H2=17.624, H3=7.241
2025-08-05 02:30:58,552 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-08-05 02:30:58,552 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-08-05 02:30:58,554 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-08-05 02:30:58,610 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17110 sites
2025-08-05 02:31:28,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=123.502, H2=0.747, H3=-0.681
2025-08-05 02:31:28,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8020 sites
2025-08-05 02:31:42,980 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.296, H2=5.882, H3=2.799
2025-08-05 02:31:43,026 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 11295 sites
2025-08-05 02:32:02,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=36.311, H2=17.199, H3=3.485
2025-08-05 02:32:03,026 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1717 sites
2025-08-05 02:32:06,101 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=23.110, H2=0.925, H3=-2.530
2025-08-05 02:32:06,152 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 14217 sites
2025-08-05 02:32:31,384 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=56.202, H2=11.270, H3=5.743
2025-08-05 02:32:31,410 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-08-05 02:32:31,410 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-08-05 02:32:31,412 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-08-05 02:32:31,467 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17110 sites
2025-08-05 02:33:02,007 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=123.502, H2=0.747, H3=-0.681
2025-08-05 02:33:02,048 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8020 sites
2025-08-05 02:33:16,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.296, H2=5.882, H3=2.799
2025-08-05 02:33:16,325 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 11295 sites
2025-08-05 02:33:36,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=36.311, H2=17.199, H3=3.485
2025-08-05 02:33:36,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1717 sites
2025-08-05 02:33:39,201 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=23.110, H2=0.925, H3=-2.530
2025-08-05 02:33:39,242 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8592 sites
2025-08-05 02:33:54,469 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=53.548, H2=9.500, H3=6.265
2025-08-05 02:33:54,505 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 5625 sites
2025-08-05 02:34:04,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=22.349, H2=5.711, H3=0.698
2025-08-05 02:34:04,432 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-08-05 02:34:04,432 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-08-05 02:34:04,434 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-08-05 02:34:04,465 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2934 sites
2025-08-05 02:34:09,659 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=36.884, H2=1.176, H3=0.115
2025-08-05 02:34:09,710 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 14176 sites
2025-08-05 02:34:34,771 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=87.334, H2=0.440, H3=-1.279
2025-08-05 02:34:34,811 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 8020 sites
2025-08-05 02:34:49,031 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=19.476, H2=5.954, H3=3.823
2025-08-05 02:34:49,077 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 11295 sites
2025-08-05 02:35:08,905 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=43.738, H2=17.368, H3=2.151
2025-08-05 02:35:08,934 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1717 sites
2025-08-05 02:35:11,955 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=24.269, H2=0.619, H3=-2.173
2025-08-05 02:35:11,996 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8592 sites
2025-08-05 02:35:27,123 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=33.743, H2=11.260, H3=6.238
2025-08-05 02:35:27,159 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 5625 sites
2025-08-05 02:35:36,974 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.340, H2=7.891, H3=0.715
2025-08-05 02:35:37,000 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-08-05 02:35:37,000 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-08-05 02:35:37,001 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-08-05 02:35:37,032 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2934 sites
2025-08-05 02:35:42,197 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=36.884, H2=1.176, H3=0.115
2025-08-05 02:35:42,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 14176 sites
2025-08-05 02:36:07,136 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=87.334, H2=0.440, H3=-1.279
2025-08-05 02:36:07,177 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 8020 sites
2025-08-05 02:36:21,294 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=19.476, H2=5.954, H3=3.823
2025-08-05 02:36:21,340 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 11295 sites
2025-08-05 02:36:41,247 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=43.738, H2=17.368, H3=2.151
2025-08-05 02:36:41,276 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1717 sites
2025-08-05 02:36:44,303 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=24.269, H2=0.619, H3=-2.173
2025-08-05 02:36:44,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8592 sites
2025-08-05 02:36:59,481 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=33.743, H2=11.260, H3=6.238
2025-08-05 02:36:59,514 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3905 sites
2025-08-05 02:37:06,392 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=10.162, H2=1.945, H3=2.379
2025-08-05 02:37:06,421 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1720 sites
2025-08-05 02:37:09,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.431, H2=1.655, H3=-3.846
2025-08-05 02:37:09,465 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-08-05 02:37:09,465 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-08-05 02:37:09,467 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-08-05 02:37:09,512 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2934 sites
2025-08-05 02:37:14,697 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=36.884, H2=1.176, H3=0.115
2025-08-05 02:37:14,748 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 14176 sites
2025-08-05 02:37:39,785 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=87.334, H2=0.440, H3=-1.279
2025-08-05 02:37:39,825 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 8020 sites
2025-08-05 02:37:53,846 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=19.476, H2=5.954, H3=3.823
2025-08-05 02:37:53,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 11295 sites
2025-08-05 02:38:13,738 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=43.738, H2=17.368, H3=2.151
2025-08-05 02:38:13,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 487 sites
2025-08-05 02:38:14,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=2.598, H2=-8.215, H3=3.183
2025-08-05 02:38:14,648 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1230 sites
2025-08-05 02:38:16,808 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=11.540, H2=4.457, H3=-4.256
2025-08-05 02:38:16,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8592 sites
2025-08-05 02:38:31,980 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=28.990, H2=11.498, H3=6.703
2025-08-05 02:38:32,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3905 sites
2025-08-05 02:38:38,878 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=12.148, H2=1.424, H3=1.694
2025-08-05 02:38:38,907 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1720 sites
2025-08-05 02:38:41,952 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=0.634, H2=1.710, H3=-3.359
2025-08-05 02:38:41,978 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-08-05 02:38:41,978 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-08-05 02:38:41,980 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-08-05 02:38:42,011 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2934 sites
2025-08-05 02:38:47,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=36.884, H2=1.176, H3=0.115
2025-08-05 02:38:47,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3685 sites
2025-08-05 02:38:53,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.877, H2=2.759, H3=3.305
2025-08-05 02:38:53,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 10491 sites
2025-08-05 02:39:12,297 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=68.571, H2=-1.394, H3=-4.881
2025-08-05 02:39:12,338 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8020 sites
2025-08-05 02:39:26,397 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.816, H2=4.368, H3=2.901
2025-08-05 02:39:26,444 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 11295 sites
2025-08-05 02:39:46,251 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=47.662, H2=11.689, H3=1.864
2025-08-05 02:39:46,277 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 487 sites
2025-08-05 02:39:47,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.941, H2=-7.087, H3=2.460
2025-08-05 02:39:47,159 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1230 sites
2025-08-05 02:39:49,316 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=10.652, H2=4.244, H3=-4.537
2025-08-05 02:39:49,357 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8592 sites
2025-08-05 02:40:04,455 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=39.713, H2=11.375, H3=7.067
2025-08-05 02:40:04,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3905 sites
2025-08-05 02:40:11,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=13.595, H2=1.383, H3=1.613
2025-08-05 02:40:11,364 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1720 sites
2025-08-05 02:40:14,386 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=0.291, H2=1.652, H3=-3.280
2025-08-05 02:40:14,412 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-08-05 02:40:14,412 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-08-05 02:40:14,414 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-08-05 02:40:14,444 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2934 sites
2025-08-05 02:40:19,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=36.884, H2=1.176, H3=0.115
2025-08-05 02:40:19,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3685 sites
2025-08-05 02:40:26,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.877, H2=2.759, H3=3.305
2025-08-05 02:40:26,191 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 10491 sites
2025-08-05 02:40:44,550 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=68.571, H2=-1.394, H3=-4.881
2025-08-05 02:40:44,590 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8020 sites
2025-08-05 02:40:58,675 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.816, H2=4.368, H3=2.901
2025-08-05 02:40:58,705 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3001 sites
2025-08-05 02:41:03,989 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=5.575, H2=-1.566, H3=-0.780
2025-08-05 02:41:04,030 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8294 sites
2025-08-05 02:41:18,559 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.085, H2=15.554, H3=2.336
2025-08-05 02:41:18,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 487 sites
2025-08-05 02:41:19,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=3.574, H2=-5.391, H3=3.727
2025-08-05 02:41:19,463 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1230 sites
2025-08-05 02:41:21,608 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=11.399, H2=4.020, H3=-4.200
2025-08-05 02:41:21,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8592 sites
2025-08-05 02:41:36,660 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=33.154, H2=12.866, H3=8.474
2025-08-05 02:41:36,693 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3905 sites
2025-08-05 02:41:43,585 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=14.390, H2=2.125, H3=1.447
2025-08-05 02:41:43,614 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1720 sites
2025-08-05 02:41:46,630 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=0.523, H2=1.610, H3=-2.570
2025-08-05 02:41:46,655 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-08-05 02:41:46,655 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-08-05 02:41:46,657 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-08-05 02:41:46,683 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:41:48,608 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:41:48,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:41:51,848 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:41:51,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:41:58,333 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:41:58,378 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 10491 sites
2025-08-05 02:42:16,861 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=63.839, H2=-1.900, H3=-4.603
2025-08-05 02:42:16,902 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8020 sites
2025-08-05 02:42:30,966 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.141, H2=3.879, H3=2.674
2025-08-05 02:42:30,997 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3001 sites
2025-08-05 02:42:36,312 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.242, H2=-0.871, H3=-0.546
2025-08-05 02:42:36,371 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8294 sites
2025-08-05 02:42:51,229 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=29.629, H2=15.105, H3=1.916
2025-08-05 02:42:51,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 487 sites
2025-08-05 02:42:52,128 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.064, H2=-9.509, H3=2.456
2025-08-05 02:42:52,155 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1230 sites
2025-08-05 02:42:54,361 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.748, H2=4.442, H3=-4.193
2025-08-05 02:42:54,402 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 8592 sites
2025-08-05 02:43:09,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=36.765, H2=12.547, H3=5.828
2025-08-05 02:43:09,989 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3905 sites
2025-08-05 02:43:16,933 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.173, H2=2.579, H3=2.134
2025-08-05 02:43:16,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1720 sites
2025-08-05 02:43:20,029 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=0.842, H2=1.656, H3=-3.294
2025-08-05 02:43:20,055 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-08-05 02:43:20,055 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-08-05 02:43:20,057 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-08-05 02:43:20,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:43:22,044 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:43:22,072 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:43:25,346 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:43:25,378 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:43:31,965 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:43:31,995 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:43:36,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:43:36,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8120 sites
2025-08-05 02:43:50,647 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=46.354, H2=-3.677, H3=-4.843
2025-08-05 02:43:50,687 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8020 sites
2025-08-05 02:44:04,919 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.557, H2=3.627, H3=2.404
2025-08-05 02:44:04,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3001 sites
2025-08-05 02:44:10,262 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.597, H2=-0.884, H3=-0.802
2025-08-05 02:44:10,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8294 sites
2025-08-05 02:44:24,842 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=42.785, H2=16.121, H3=2.059
2025-08-05 02:44:24,870 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 487 sites
2025-08-05 02:44:25,739 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.310, H2=-7.555, H3=2.620
2025-08-05 02:44:25,766 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1230 sites
2025-08-05 02:44:27,945 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=11.274, H2=4.397, H3=-3.879
2025-08-05 02:44:27,987 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 8592 sites
2025-08-05 02:44:43,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=32.346, H2=12.372, H3=7.100
2025-08-05 02:44:43,183 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3905 sites
2025-08-05 02:44:50,023 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=13.618, H2=1.810, H3=1.925
2025-08-05 02:44:50,052 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1720 sites
2025-08-05 02:44:53,095 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=0.390, H2=1.358, H3=-3.302
2025-08-05 02:44:53,120 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-08-05 02:44:53,120 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-08-05 02:44:53,122 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-08-05 02:44:53,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:44:55,085 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:44:55,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:44:58,380 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:44:58,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:45:04,876 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:45:04,906 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:45:09,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:45:09,141 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8120 sites
2025-08-05 02:45:23,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=46.354, H2=-3.677, H3=-4.843
2025-08-05 02:45:23,453 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8020 sites
2025-08-05 02:45:37,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.557, H2=3.627, H3=2.404
2025-08-05 02:45:37,545 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3001 sites
2025-08-05 02:45:42,814 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.597, H2=-0.884, H3=-0.802
2025-08-05 02:45:42,855 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8294 sites
2025-08-05 02:45:57,456 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=42.785, H2=16.121, H3=2.059
2025-08-05 02:45:57,483 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 487 sites
2025-08-05 02:45:58,339 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.310, H2=-7.555, H3=2.620
2025-08-05 02:45:58,367 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1230 sites
2025-08-05 02:46:00,531 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=11.274, H2=4.397, H3=-3.879
2025-08-05 02:46:00,583 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 8592 sites
2025-08-05 02:46:15,680 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=32.346, H2=12.372, H3=7.100
2025-08-05 02:46:15,713 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3905 sites
2025-08-05 02:46:22,664 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=13.618, H2=1.810, H3=1.925
2025-08-05 02:46:22,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 571 sites
2025-08-05 02:46:23,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-2.201, H2=-1.204, H3=-2.640
2025-08-05 02:46:23,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1149 sites
2025-08-05 02:46:25,747 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.544, H2=1.505, H3=-1.430
2025-08-05 02:46:25,772 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-08-05 02:46:25,772 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-08-05 02:46:25,774 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-08-05 02:46:25,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:46:27,734 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:46:27,762 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:46:31,008 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:46:31,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:46:37,518 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:46:37,548 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:46:41,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:46:41,775 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8120 sites
2025-08-05 02:46:56,108 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=46.354, H2=-3.677, H3=-4.843
2025-08-05 02:46:56,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8020 sites
2025-08-05 02:47:10,320 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.557, H2=3.627, H3=2.404
2025-08-05 02:47:10,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3001 sites
2025-08-05 02:47:15,650 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.597, H2=-0.884, H3=-0.802
2025-08-05 02:47:15,680 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2402 sites
2025-08-05 02:47:19,921 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.768, H2=0.922, H3=-0.976
2025-08-05 02:47:19,957 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 5892 sites
2025-08-05 02:47:30,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=42.093, H2=18.623, H3=3.844
2025-08-05 02:47:30,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 487 sites
2025-08-05 02:47:31,274 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.303, H2=-7.545, H3=2.500
2025-08-05 02:47:31,302 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1230 sites
2025-08-05 02:47:33,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=12.373, H2=3.283, H3=-6.640
2025-08-05 02:47:33,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 8592 sites
2025-08-05 02:47:48,823 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=35.750, H2=12.951, H3=6.271
2025-08-05 02:47:48,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3905 sites
2025-08-05 02:47:55,723 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=9.691, H2=1.536, H3=1.573
2025-08-05 02:47:55,751 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 571 sites
2025-08-05 02:47:56,760 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-2.717, H2=-1.614, H3=-3.786
2025-08-05 02:47:56,787 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1149 sites
2025-08-05 02:47:58,827 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.949, H2=1.344, H3=-1.646
2025-08-05 02:47:58,852 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-08-05 02:47:58,853 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-08-05 02:47:58,854 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-08-05 02:47:58,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:48:00,813 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:48:00,843 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:48:04,075 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:48:04,107 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:48:10,616 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:48:10,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:48:14,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:48:14,863 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8120 sites
2025-08-05 02:48:29,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=46.354, H2=-3.677, H3=-4.843
2025-08-05 02:48:29,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8020 sites
2025-08-05 02:48:43,349 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.557, H2=3.627, H3=2.404
2025-08-05 02:48:43,381 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3001 sites
2025-08-05 02:48:48,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.597, H2=-0.884, H3=-0.802
2025-08-05 02:48:48,659 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2402 sites
2025-08-05 02:48:52,875 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.768, H2=0.922, H3=-0.976
2025-08-05 02:48:52,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 5892 sites
2025-08-05 02:49:03,251 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=42.093, H2=18.623, H3=3.844
2025-08-05 02:49:03,279 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 487 sites
2025-08-05 02:49:04,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.303, H2=-7.545, H3=2.500
2025-08-05 02:49:04,171 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1230 sites
2025-08-05 02:49:06,350 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=12.373, H2=3.283, H3=-6.640
2025-08-05 02:49:06,384 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4617 sites
2025-08-05 02:49:14,591 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=46.286, H2=20.029, H3=9.096
2025-08-05 02:49:14,624 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3975 sites
2025-08-05 02:49:21,716 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=14.479, H2=0.331, H3=1.673
2025-08-05 02:49:21,749 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3905 sites
2025-08-05 02:49:28,740 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=13.875, H2=1.439, H3=2.344
2025-08-05 02:49:28,767 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 571 sites
2025-08-05 02:49:29,785 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-2.720, H2=-1.222, H3=-3.448
2025-08-05 02:49:29,813 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1149 sites
2025-08-05 02:49:31,856 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=1.439, H2=2.207, H3=-2.075
2025-08-05 02:49:31,881 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-08-05 02:49:31,881 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-08-05 02:49:31,883 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-08-05 02:49:31,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:49:33,850 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:49:33,879 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:49:37,122 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:49:37,154 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:49:43,695 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:49:43,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:49:47,914 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:49:47,954 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8120 sites
2025-08-05 02:50:02,339 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=46.354, H2=-3.677, H3=-4.843
2025-08-05 02:50:02,380 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8020 sites
2025-08-05 02:50:16,507 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.557, H2=3.627, H3=2.404
2025-08-05 02:50:16,539 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3001 sites
2025-08-05 02:50:21,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.597, H2=-0.884, H3=-0.802
2025-08-05 02:50:21,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2402 sites
2025-08-05 02:50:26,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.768, H2=0.922, H3=-0.976
2025-08-05 02:50:26,154 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 5892 sites
2025-08-05 02:50:36,618 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=42.093, H2=18.623, H3=3.844
2025-08-05 02:50:36,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 487 sites
2025-08-05 02:50:37,498 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.303, H2=-7.545, H3=2.500
2025-08-05 02:50:37,524 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 378 sites
2025-08-05 02:50:38,188 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-4.941, H2=0.289, H3=-1.056
2025-08-05 02:50:38,215 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 852 sites
2025-08-05 02:50:39,711 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=21.106, H2=-3.063, H3=-5.942
2025-08-05 02:50:39,744 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4617 sites
2025-08-05 02:50:47,856 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=29.185, H2=16.152, H3=7.305
2025-08-05 02:50:47,889 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3975 sites
2025-08-05 02:50:54,938 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=17.560, H2=0.104, H3=1.937
2025-08-05 02:50:54,971 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3905 sites
2025-08-05 02:51:01,837 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=17.232, H2=1.952, H3=1.748
2025-08-05 02:51:01,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 571 sites
2025-08-05 02:51:02,869 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-3.414, H2=-1.807, H3=-3.884
2025-08-05 02:51:02,896 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1149 sites
2025-08-05 02:51:04,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.624, H2=1.911, H3=-2.070
2025-08-05 02:51:04,971 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-08-05 02:51:04,972 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-08-05 02:51:04,973 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-08-05 02:51:05,002 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:51:06,931 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:51:06,960 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:51:10,194 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:51:10,226 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:51:16,707 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:51:16,737 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:51:20,907 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:51:20,947 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8120 sites
2025-08-05 02:51:35,209 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=46.354, H2=-3.677, H3=-4.843
2025-08-05 02:51:35,260 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8020 sites
2025-08-05 02:51:49,464 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.557, H2=3.627, H3=2.404
2025-08-05 02:51:49,495 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3001 sites
2025-08-05 02:51:54,846 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.597, H2=-0.884, H3=-0.802
2025-08-05 02:51:54,877 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2402 sites
2025-08-05 02:51:59,147 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.768, H2=0.922, H3=-0.976
2025-08-05 02:51:59,184 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 5892 sites
2025-08-05 02:52:09,557 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=42.093, H2=18.623, H3=3.844
2025-08-05 02:52:09,583 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 487 sites
2025-08-05 02:52:10,439 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.303, H2=-7.545, H3=2.500
2025-08-05 02:52:10,465 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 378 sites
2025-08-05 02:52:11,128 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-4.941, H2=0.289, H3=-1.056
2025-08-05 02:52:11,155 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 852 sites
2025-08-05 02:52:12,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=21.106, H2=-3.063, H3=-5.942
2025-08-05 02:52:12,685 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4617 sites
2025-08-05 02:52:20,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=29.185, H2=16.152, H3=7.305
2025-08-05 02:52:20,924 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3975 sites
2025-08-05 02:52:27,932 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=17.560, H2=0.104, H3=1.937
2025-08-05 02:52:27,960 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1300 sites
2025-08-05 02:52:30,245 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-2.045, H2=2.307, H3=2.418
2025-08-05 02:52:30,276 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2605 sites
2025-08-05 02:52:34,878 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=6.336, H2=-0.681, H3=0.034
2025-08-05 02:52:34,904 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 571 sites
2025-08-05 02:52:35,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-2.900, H2=-1.108, H3=-3.123
2025-08-05 02:52:35,937 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1149 sites
2025-08-05 02:52:37,971 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=1.350, H2=1.563, H3=-1.543
2025-08-05 02:52:37,996 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-08-05 02:52:37,996 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-08-05 02:52:37,998 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-08-05 02:52:38,025 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:52:39,956 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:52:39,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:52:43,233 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:52:43,265 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:52:49,865 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:52:49,894 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:52:54,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:52:54,138 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8120 sites
2025-08-05 02:53:08,537 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=46.354, H2=-3.677, H3=-4.843
2025-08-05 02:53:08,566 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1880 sites
2025-08-05 02:53:11,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.065, H2=-1.100, H3=-0.459
2025-08-05 02:53:11,931 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6140 sites
2025-08-05 02:53:22,780 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=12.535, H2=4.205, H3=2.571
2025-08-05 02:53:22,810 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3001 sites
2025-08-05 02:53:28,087 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.260, H2=-0.900, H3=-0.941
2025-08-05 02:53:28,117 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2402 sites
2025-08-05 02:53:32,368 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.157, H2=0.853, H3=-0.680
2025-08-05 02:53:32,404 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 5892 sites
2025-08-05 02:53:42,732 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=55.410, H2=17.629, H3=2.177
2025-08-05 02:53:42,758 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 487 sites
2025-08-05 02:53:43,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.562, H2=-5.550, H3=2.670
2025-08-05 02:53:43,648 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 378 sites
2025-08-05 02:53:44,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-4.750, H2=0.193, H3=-1.194
2025-08-05 02:53:44,346 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 852 sites
2025-08-05 02:53:45,852 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=15.099, H2=-3.050, H3=-6.443
2025-08-05 02:53:45,885 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4617 sites
2025-08-05 02:53:54,113 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=32.252, H2=13.368, H3=7.288
2025-08-05 02:53:54,145 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3975 sites
2025-08-05 02:54:01,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=16.279, H2=0.627, H3=1.761
2025-08-05 02:54:01,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1300 sites
2025-08-05 02:54:03,537 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-1.959, H2=4.851, H3=2.292
2025-08-05 02:54:03,567 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2605 sites
2025-08-05 02:54:08,203 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=6.676, H2=-0.402, H3=0.250
2025-08-05 02:54:08,230 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 571 sites
2025-08-05 02:54:09,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-2.519, H2=-1.295, H3=-2.867
2025-08-05 02:54:09,271 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1149 sites
2025-08-05 02:54:11,329 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=2.802, H2=1.986, H3=-2.340
2025-08-05 02:54:11,354 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-08-05 02:54:11,354 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-08-05 02:54:11,356 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-08-05 02:54:11,383 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:54:13,328 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:54:13,357 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:54:16,616 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:54:16,649 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:54:23,174 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:54:23,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:54:27,388 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:54:27,429 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8120 sites
2025-08-05 02:54:41,728 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=46.354, H2=-3.677, H3=-4.843
2025-08-05 02:54:41,757 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1880 sites
2025-08-05 02:54:45,079 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.065, H2=-1.100, H3=-0.459
2025-08-05 02:54:45,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6140 sites
2025-08-05 02:54:55,886 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=12.535, H2=4.205, H3=2.571
2025-08-05 02:54:55,918 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3001 sites
2025-08-05 02:55:01,212 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.260, H2=-0.900, H3=-0.941
2025-08-05 02:55:01,242 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2402 sites
2025-08-05 02:55:05,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.157, H2=0.853, H3=-0.680
2025-08-05 02:55:05,504 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 5892 sites
2025-08-05 02:55:15,854 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=55.410, H2=17.629, H3=2.177
2025-08-05 02:55:15,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 487 sites
2025-08-05 02:55:16,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.562, H2=-5.550, H3=2.670
2025-08-05 02:55:16,761 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 378 sites
2025-08-05 02:55:17,425 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-4.750, H2=0.193, H3=-1.194
2025-08-05 02:55:17,452 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 852 sites
2025-08-05 02:55:18,938 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=15.099, H2=-3.050, H3=-6.443
2025-08-05 02:55:18,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4617 sites
2025-08-05 02:55:27,076 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=32.252, H2=13.368, H3=7.288
2025-08-05 02:55:27,105 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1876 sites
2025-08-05 02:55:30,433 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-3.863, H2=-1.538, H3=2.413
2025-08-05 02:55:30,462 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2099 sites
2025-08-05 02:55:34,162 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=9.456, H2=0.251, H3=-1.634
2025-08-05 02:55:34,190 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1300 sites
2025-08-05 02:55:36,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-1.158, H2=2.960, H3=3.155
2025-08-05 02:55:36,520 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2605 sites
2025-08-05 02:55:41,133 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=7.689, H2=-0.505, H3=0.744
2025-08-05 02:55:41,159 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 571 sites
2025-08-05 02:55:42,164 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-4.566, H2=-1.546, H3=-4.221
2025-08-05 02:55:42,191 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1149 sites
2025-08-05 02:55:44,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.568, H2=1.834, H3=-1.852
2025-08-05 02:55:44,240 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-08-05 02:55:44,240 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-08-05 02:55:44,242 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-08-05 02:55:44,268 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:55:46,203 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:55:46,233 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:55:49,480 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:55:49,512 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:55:55,985 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:55:56,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:56:00,194 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:56:00,224 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2134 sites
2025-08-05 02:56:03,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=11.943, H2=1.178, H3=-4.857
2025-08-05 02:56:04,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 5986 sites
2025-08-05 02:56:14,613 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=40.736, H2=-3.539, H3=-1.760
2025-08-05 02:56:14,641 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1880 sites
2025-08-05 02:56:17,943 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.141, H2=-1.121, H3=-0.821
2025-08-05 02:56:17,980 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6140 sites
2025-08-05 02:56:28,788 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.832, H2=4.639, H3=3.067
2025-08-05 02:56:28,818 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3001 sites
2025-08-05 02:56:34,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.039, H2=-2.021, H3=-1.124
2025-08-05 02:56:34,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2402 sites
2025-08-05 02:56:38,383 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.377, H2=0.833, H3=-0.891
2025-08-05 02:56:38,419 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 5892 sites
2025-08-05 02:56:48,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=37.704, H2=26.097, H3=3.780
2025-08-05 02:56:48,803 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 487 sites
2025-08-05 02:56:49,660 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.376, H2=-7.462, H3=2.961
2025-08-05 02:56:49,686 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 378 sites
2025-08-05 02:56:50,352 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-3.202, H2=0.226, H3=-1.067
2025-08-05 02:56:50,379 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 852 sites
2025-08-05 02:56:51,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=14.480, H2=-2.484, H3=-5.600
2025-08-05 02:56:51,918 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4617 sites
2025-08-05 02:57:00,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=41.391, H2=13.990, H3=7.630
2025-08-05 02:57:00,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1876 sites
2025-08-05 02:57:03,408 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-3.388, H2=-1.219, H3=2.420
2025-08-05 02:57:03,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2099 sites
2025-08-05 02:57:07,129 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=10.524, H2=0.471, H3=-1.034
2025-08-05 02:57:07,157 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1300 sites
2025-08-05 02:57:09,434 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-1.483, H2=3.008, H3=2.774
2025-08-05 02:57:09,464 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2605 sites
2025-08-05 02:57:14,053 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=5.981, H2=-0.879, H3=-0.166
2025-08-05 02:57:14,079 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 571 sites
2025-08-05 02:57:15,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-2.806, H2=-1.523, H3=-3.995
2025-08-05 02:57:15,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1149 sites
2025-08-05 02:57:17,145 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=2.824, H2=2.076, H3=-1.832
2025-08-05 02:57:17,170 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-08-05 02:57:17,170 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-08-05 02:57:17,172 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-08-05 02:57:17,242 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:57:19,174 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:57:19,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:57:22,424 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:57:22,457 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:57:28,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:57:28,988 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:57:33,141 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:57:33,170 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2134 sites
2025-08-05 02:57:36,903 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=11.943, H2=1.178, H3=-4.857
2025-08-05 02:57:36,940 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 5986 sites
2025-08-05 02:57:47,425 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=40.736, H2=-3.539, H3=-1.760
2025-08-05 02:57:47,454 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1880 sites
2025-08-05 02:57:50,753 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.141, H2=-1.121, H3=-0.821
2025-08-05 02:57:50,791 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6140 sites
2025-08-05 02:58:01,576 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.832, H2=4.639, H3=3.067
2025-08-05 02:58:01,607 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3001 sites
2025-08-05 02:58:06,898 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.039, H2=-2.021, H3=-1.124
2025-08-05 02:58:06,929 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2402 sites
2025-08-05 02:58:11,158 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.377, H2=0.833, H3=-0.891
2025-08-05 02:58:11,195 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 5892 sites
2025-08-05 02:58:21,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=37.704, H2=26.097, H3=3.780
2025-08-05 02:58:21,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 487 sites
2025-08-05 02:58:22,435 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.376, H2=-7.462, H3=2.961
2025-08-05 02:58:22,461 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 378 sites
2025-08-05 02:58:23,123 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-3.202, H2=0.226, H3=-1.067
2025-08-05 02:58:23,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 852 sites
2025-08-05 02:58:24,647 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=14.480, H2=-2.484, H3=-5.600
2025-08-05 02:58:24,676 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2313 sites
2025-08-05 02:58:28,745 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=7.869, H2=2.651, H3=6.368
2025-08-05 02:58:28,774 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2304 sites
2025-08-05 02:58:32,862 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=8.179, H2=3.800, H3=-1.283
2025-08-05 02:58:32,891 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1876 sites
2025-08-05 02:58:36,219 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-3.158, H2=-0.957, H3=2.690
2025-08-05 02:58:36,248 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2099 sites
2025-08-05 02:58:39,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=11.585, H2=0.529, H3=-0.496
2025-08-05 02:58:40,011 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1300 sites
2025-08-05 02:58:42,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-1.705, H2=3.197, H3=2.284
2025-08-05 02:58:42,348 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2605 sites
2025-08-05 02:58:46,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.035, H2=-0.686, H3=0.431
2025-08-05 02:58:47,006 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 571 sites
2025-08-05 02:58:48,011 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-3.753, H2=-1.888, H3=-4.635
2025-08-05 02:58:48,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1149 sites
2025-08-05 02:58:50,064 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=2.684, H2=3.290, H3=-1.733
2025-08-05 02:58:50,089 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-08-05 02:58:50,089 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-08-05 02:58:50,091 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-08-05 02:58:50,118 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 02:58:52,062 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 02:58:52,090 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 02:58:55,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 02:58:55,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 02:59:01,907 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 02:59:01,938 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 02:59:06,106 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 02:59:06,136 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2134 sites
2025-08-05 02:59:09,918 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=11.943, H2=1.178, H3=-4.857
2025-08-05 02:59:09,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 5986 sites
2025-08-05 02:59:20,602 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=40.736, H2=-3.539, H3=-1.760
2025-08-05 02:59:20,632 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1880 sites
2025-08-05 02:59:23,952 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.141, H2=-1.121, H3=-0.821
2025-08-05 02:59:23,989 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6140 sites
2025-08-05 02:59:34,867 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.832, H2=4.639, H3=3.067
2025-08-05 02:59:34,898 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3001 sites
2025-08-05 02:59:40,206 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.039, H2=-2.021, H3=-1.124
2025-08-05 02:59:40,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2402 sites
2025-08-05 02:59:44,475 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.377, H2=0.833, H3=-0.891
2025-08-05 02:59:44,504 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2117 sites
2025-08-05 02:59:48,254 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=21.078, H2=7.690, H3=-3.184
2025-08-05 02:59:48,288 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3775 sites
2025-08-05 02:59:54,967 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.813, H2=7.305, H3=9.763
2025-08-05 02:59:54,994 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 487 sites
2025-08-05 02:59:55,858 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.954, H2=-8.745, H3=3.001
2025-08-05 02:59:55,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 378 sites
2025-08-05 02:59:56,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.544, H2=-0.413, H3=-1.662
2025-08-05 02:59:56,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 852 sites
2025-08-05 02:59:58,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=18.564, H2=-1.831, H3=-7.556
2025-08-05 02:59:58,113 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2313 sites
2025-08-05 03:00:02,197 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=5.140, H2=3.965, H3=7.628
2025-08-05 03:00:02,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2304 sites
2025-08-05 03:00:06,285 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=11.600, H2=3.983, H3=-0.894
2025-08-05 03:00:06,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1876 sites
2025-08-05 03:00:09,611 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-3.031, H2=-0.961, H3=3.949
2025-08-05 03:00:09,640 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2099 sites
2025-08-05 03:00:13,338 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=9.093, H2=0.131, H3=-1.339
2025-08-05 03:00:13,365 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1300 sites
2025-08-05 03:00:15,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-1.182, H2=3.754, H3=2.803
2025-08-05 03:00:15,681 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2605 sites
2025-08-05 03:00:20,291 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=6.370, H2=-0.900, H3=0.309
2025-08-05 03:00:20,318 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 571 sites
2025-08-05 03:00:21,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-2.340, H2=-1.404, H3=-4.246
2025-08-05 03:00:21,354 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1149 sites
2025-08-05 03:00:23,373 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=1.813, H2=1.885, H3=-1.081
2025-08-05 03:00:23,397 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-08-05 03:00:23,397 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-08-05 03:00:23,399 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-08-05 03:00:23,442 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 03:00:25,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 03:00:25,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 03:00:28,668 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 03:00:28,702 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 03:00:35,219 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 03:00:35,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2371 sites
2025-08-05 03:00:39,432 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.400, H2=-2.241, H3=-1.893
2025-08-05 03:00:39,462 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2134 sites
2025-08-05 03:00:43,249 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=11.943, H2=1.178, H3=-4.857
2025-08-05 03:00:43,280 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3070 sites
2025-08-05 03:00:48,703 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=26.826, H2=-0.440, H3=-0.303
2025-08-05 03:00:48,734 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2916 sites
2025-08-05 03:00:53,885 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=23.614, H2=-8.837, H3=-3.410
2025-08-05 03:00:53,914 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1880 sites
2025-08-05 03:00:57,248 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.293, H2=-1.328, H3=-1.341
2025-08-05 03:00:57,284 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6140 sites
2025-08-05 03:01:08,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=19.526, H2=4.846, H3=3.938
2025-08-05 03:01:08,111 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3001 sites
2025-08-05 03:01:13,419 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.470, H2=-0.873, H3=-0.673
2025-08-05 03:01:13,450 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2402 sites
2025-08-05 03:01:17,662 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.752, H2=1.054, H3=-0.364
2025-08-05 03:01:17,691 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2117 sites
2025-08-05 03:01:21,427 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.513, H2=9.903, H3=-4.525
2025-08-05 03:01:21,459 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3775 sites
2025-08-05 03:01:28,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=11.270, H2=5.418, H3=5.089
2025-08-05 03:01:28,142 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 487 sites
2025-08-05 03:01:29,001 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.462, H2=-6.461, H3=2.398
2025-08-05 03:01:29,029 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 378 sites
2025-08-05 03:01:29,697 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-4.188, H2=0.279, H3=-1.372
2025-08-05 03:01:29,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 852 sites
2025-08-05 03:01:31,223 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=19.073, H2=-2.982, H3=-5.758
2025-08-05 03:01:31,252 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2313 sites
2025-08-05 03:01:35,366 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=6.199, H2=3.756, H3=7.550
2025-08-05 03:01:35,404 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2304 sites
2025-08-05 03:01:39,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.629, H2=3.224, H3=-0.338
2025-08-05 03:01:39,495 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1876 sites
2025-08-05 03:01:42,806 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-3.312, H2=-1.192, H3=3.030
2025-08-05 03:01:42,836 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2099 sites
2025-08-05 03:01:46,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=10.970, H2=0.432, H3=-1.137
2025-08-05 03:01:46,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1300 sites
2025-08-05 03:01:48,864 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-1.442, H2=3.331, H3=2.756
2025-08-05 03:01:48,894 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2605 sites
2025-08-05 03:01:53,522 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=6.676, H2=-0.497, H3=0.336
2025-08-05 03:01:53,548 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 571 sites
2025-08-05 03:01:54,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-2.709, H2=-1.236, H3=-3.001
2025-08-05 03:01:54,583 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1149 sites
2025-08-05 03:01:56,597 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=2.192, H2=3.071, H3=-1.621
2025-08-05 03:01:56,623 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-08-05 03:01:56,623 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-08-05 03:01:56,625 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-08-05 03:01:56,653 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 03:01:58,596 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 03:01:58,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 03:02:01,868 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 03:02:01,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 03:02:08,377 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 03:02:08,405 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1272 sites
2025-08-05 03:02:10,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=27.650, H2=0.324, H3=0.076
2025-08-05 03:02:10,674 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1099 sites
2025-08-05 03:02:12,633 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=13.508, H2=-3.254, H3=-3.222
2025-08-05 03:02:12,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2134 sites
2025-08-05 03:02:16,469 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.548, H2=2.615, H3=-5.574
2025-08-05 03:02:16,500 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3070 sites
2025-08-05 03:02:21,968 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=30.527, H2=-0.394, H3=-0.594
2025-08-05 03:02:21,999 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2916 sites
2025-08-05 03:02:27,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=21.629, H2=-6.993, H3=-3.981
2025-08-05 03:02:27,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1880 sites
2025-08-05 03:02:30,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=5.306, H2=-2.475, H3=-1.059
2025-08-05 03:02:30,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6140 sites
2025-08-05 03:02:41,413 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=18.928, H2=6.143, H3=2.800
2025-08-05 03:02:41,445 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3001 sites
2025-08-05 03:02:46,720 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.579, H2=-0.945, H3=-0.485
2025-08-05 03:02:46,750 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2402 sites
2025-08-05 03:02:50,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.663, H2=1.228, H3=-0.774
2025-08-05 03:02:51,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2117 sites
2025-08-05 03:02:54,753 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=23.911, H2=9.910, H3=-4.083
2025-08-05 03:02:54,786 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3775 sites
2025-08-05 03:03:01,446 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=11.806, H2=4.950, H3=7.006
2025-08-05 03:03:01,473 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 487 sites
2025-08-05 03:03:02,324 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.988, H2=-6.534, H3=2.922
2025-08-05 03:03:02,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 378 sites
2025-08-05 03:03:03,022 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-4.553, H2=0.160, H3=-1.287
2025-08-05 03:03:03,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 852 sites
2025-08-05 03:03:04,532 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=20.945, H2=-2.436, H3=-6.279
2025-08-05 03:03:04,563 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2313 sites
2025-08-05 03:03:08,622 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.999, H2=3.072, H3=8.259
2025-08-05 03:03:08,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2304 sites
2025-08-05 03:03:12,695 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.892, H2=3.368, H3=-1.454
2025-08-05 03:03:12,724 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1876 sites
2025-08-05 03:03:15,992 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-2.736, H2=-1.159, H3=3.262
2025-08-05 03:03:16,022 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2099 sites
2025-08-05 03:03:19,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=15.733, H2=0.329, H3=-1.099
2025-08-05 03:03:19,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1300 sites
2025-08-05 03:03:21,999 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-1.586, H2=3.831, H3=2.567
2025-08-05 03:03:22,029 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2605 sites
2025-08-05 03:03:26,610 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=4.784, H2=-0.396, H3=0.526
2025-08-05 03:03:26,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 571 sites
2025-08-05 03:03:27,650 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-3.058, H2=-1.551, H3=-3.336
2025-08-05 03:03:27,678 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1149 sites
2025-08-05 03:03:29,704 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=2.343, H2=1.230, H3=-1.580
2025-08-05 03:03:29,729 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-08-05 03:03:29,729 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-08-05 03:03:29,731 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-08-05 03:03:29,758 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 03:03:31,671 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 03:03:31,700 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 03:03:34,904 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 03:03:34,937 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-08-05 03:03:41,391 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=29.876, H2=3.560, H3=4.934
2025-08-05 03:03:41,418 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1272 sites
2025-08-05 03:03:43,644 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=27.650, H2=0.324, H3=0.076
2025-08-05 03:03:43,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1099 sites
2025-08-05 03:03:45,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=13.508, H2=-3.254, H3=-3.222
2025-08-05 03:03:45,636 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2134 sites
2025-08-05 03:03:49,434 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.548, H2=2.615, H3=-5.574
2025-08-05 03:03:49,465 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3070 sites
2025-08-05 03:03:54,858 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=30.527, H2=-0.394, H3=-0.594
2025-08-05 03:03:54,889 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2916 sites
2025-08-05 03:04:00,020 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=21.629, H2=-6.993, H3=-3.981
2025-08-05 03:04:00,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1880 sites
2025-08-05 03:04:03,342 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=5.306, H2=-2.475, H3=-1.059
2025-08-05 03:04:03,370 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1942 sites
2025-08-05 03:04:06,760 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.305, H2=-0.607, H3=0.820
2025-08-05 03:04:06,794 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4198 sites
2025-08-05 03:04:14,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=12.254, H2=5.475, H3=3.206
2025-08-05 03:04:14,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3001 sites
2025-08-05 03:04:19,480 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.916, H2=-0.954, H3=-1.074
2025-08-05 03:04:19,522 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2402 sites
2025-08-05 03:04:23,738 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=4.242, H2=0.542, H3=-1.079
2025-08-05 03:04:23,766 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2117 sites
2025-08-05 03:04:27,494 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=20.639, H2=10.075, H3=-3.308
2025-08-05 03:04:27,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3775 sites
2025-08-05 03:04:34,184 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.701, H2=5.935, H3=5.466
2025-08-05 03:04:34,211 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 487 sites
2025-08-05 03:04:35,072 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.570, H2=-11.359, H3=2.337
2025-08-05 03:04:35,098 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 378 sites
2025-08-05 03:04:35,768 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-4.021, H2=0.123, H3=-1.312
2025-08-05 03:04:35,795 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 852 sites
2025-08-05 03:04:37,300 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=16.585, H2=-3.200, H3=-5.734
2025-08-05 03:04:37,330 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2313 sites
2025-08-05 03:04:41,402 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=6.013, H2=3.061, H3=9.068
2025-08-05 03:04:41,431 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2304 sites
2025-08-05 03:04:45,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=7.846, H2=3.873, H3=-0.777
2025-08-05 03:04:45,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1876 sites
2025-08-05 03:04:48,803 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-4.076, H2=-1.310, H3=2.934
2025-08-05 03:04:48,833 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2099 sites
2025-08-05 03:04:52,509 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=12.583, H2=1.103, H3=-1.347
2025-08-05 03:04:52,537 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1300 sites
2025-08-05 03:04:54,819 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-1.201, H2=3.993, H3=1.946
2025-08-05 03:04:54,849 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2605 sites
2025-08-05 03:04:59,437 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=6.318, H2=-0.251, H3=0.874
2025-08-05 03:04:59,464 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 571 sites
2025-08-05 03:05:00,465 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-3.425, H2=-1.561, H3=-3.142
2025-08-05 03:05:00,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1149 sites
2025-08-05 03:05:02,523 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=1.128, H2=1.672, H3=-1.454
2025-08-05 03:05:02,548 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-08-05 03:05:02,549 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-08-05 03:05:02,550 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-08-05 03:05:02,586 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 03:05:04,516 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 03:05:04,546 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 03:05:07,803 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 03:05:07,832 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1804 sites
2025-08-05 03:05:11,009 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.672, H2=-3.091, H3=-1.300
2025-08-05 03:05:11,039 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1881 sites
2025-08-05 03:05:14,357 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.830, H2=8.267, H3=5.202
2025-08-05 03:05:14,385 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1272 sites
2025-08-05 03:05:16,642 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=20.981, H2=0.040, H3=0.364
2025-08-05 03:05:16,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1099 sites
2025-08-05 03:05:18,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.912, H2=-3.421, H3=-2.509
2025-08-05 03:05:18,648 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2134 sites
2025-08-05 03:05:22,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.709, H2=2.435, H3=-5.595
2025-08-05 03:05:22,479 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3070 sites
2025-08-05 03:05:27,904 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=25.080, H2=-0.423, H3=-0.612
2025-08-05 03:05:27,935 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2916 sites
2025-08-05 03:05:33,109 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=31.033, H2=-11.784, H3=-3.452
2025-08-05 03:05:33,138 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1880 sites
2025-08-05 03:05:36,438 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.695, H2=-1.710, H3=-0.930
2025-08-05 03:05:36,468 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1942 sites
2025-08-05 03:05:39,898 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.705, H2=-0.220, H3=1.986
2025-08-05 03:05:39,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4198 sites
2025-08-05 03:05:47,361 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=18.942, H2=6.964, H3=3.579
2025-08-05 03:05:47,392 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3001 sites
2025-08-05 03:05:52,761 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.244, H2=-1.053, H3=-1.062
2025-08-05 03:05:52,792 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2402 sites
2025-08-05 03:05:57,085 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.561, H2=0.686, H3=-0.440
2025-08-05 03:05:57,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2117 sites
2025-08-05 03:06:00,912 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=23.310, H2=10.523, H3=-4.072
2025-08-05 03:06:00,944 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3775 sites
2025-08-05 03:06:07,624 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=10.250, H2=5.830, H3=6.036
2025-08-05 03:06:07,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 487 sites
2025-08-05 03:06:08,527 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.581, H2=-7.206, H3=2.529
2025-08-05 03:06:08,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 378 sites
2025-08-05 03:06:09,223 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-3.823, H2=0.216, H3=-1.496
2025-08-05 03:06:09,251 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 852 sites
2025-08-05 03:06:10,760 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=22.148, H2=-1.993, H3=-7.284
2025-08-05 03:06:10,789 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2313 sites
2025-08-05 03:06:14,880 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.521, H2=3.348, H3=7.229
2025-08-05 03:06:14,909 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2304 sites
2025-08-05 03:06:18,993 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=9.296, H2=3.881, H3=-0.824
2025-08-05 03:06:19,021 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1876 sites
2025-08-05 03:06:22,362 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-3.193, H2=-0.999, H3=3.283
2025-08-05 03:06:22,391 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2099 sites
2025-08-05 03:06:26,113 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=9.470, H2=0.772, H3=-0.810
2025-08-05 03:06:26,141 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1300 sites
2025-08-05 03:06:28,442 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-1.321, H2=4.121, H3=2.691
2025-08-05 03:06:28,472 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2605 sites
2025-08-05 03:06:33,083 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=7.497, H2=-1.000, H3=0.300
2025-08-05 03:06:33,110 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 571 sites
2025-08-05 03:06:34,110 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-2.454, H2=-1.929, H3=-4.221
2025-08-05 03:06:34,137 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1149 sites
2025-08-05 03:06:36,156 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=1.966, H2=1.606, H3=-1.922
2025-08-05 03:06:36,182 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-08-05 03:06:36,182 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-08-05 03:06:36,184 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-08-05 03:06:36,212 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 03:06:38,156 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 03:06:38,185 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 03:06:41,419 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 03:06:41,448 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1804 sites
2025-08-05 03:06:44,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.672, H2=-3.091, H3=-1.300
2025-08-05 03:06:44,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1881 sites
2025-08-05 03:06:47,962 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.830, H2=8.267, H3=5.202
2025-08-05 03:06:47,992 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1272 sites
2025-08-05 03:06:50,224 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=20.981, H2=0.040, H3=0.364
2025-08-05 03:06:50,252 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1099 sites
2025-08-05 03:06:52,172 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.912, H2=-3.421, H3=-2.509
2025-08-05 03:06:52,202 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2134 sites
2025-08-05 03:06:55,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.709, H2=2.435, H3=-5.595
2025-08-05 03:06:55,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3070 sites
2025-08-05 03:07:01,433 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=25.080, H2=-0.423, H3=-0.612
2025-08-05 03:07:01,464 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2916 sites
2025-08-05 03:07:06,578 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=31.033, H2=-11.784, H3=-3.452
2025-08-05 03:07:06,607 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1880 sites
2025-08-05 03:07:09,933 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.695, H2=-1.710, H3=-0.930
2025-08-05 03:07:09,962 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1942 sites
2025-08-05 03:07:13,382 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.705, H2=-0.220, H3=1.986
2025-08-05 03:07:13,415 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4198 sites
2025-08-05 03:07:20,820 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=18.942, H2=6.964, H3=3.579
2025-08-05 03:07:20,851 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3001 sites
2025-08-05 03:07:26,137 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.244, H2=-1.053, H3=-1.062
2025-08-05 03:07:26,167 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2402 sites
2025-08-05 03:07:30,403 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.561, H2=0.686, H3=-0.440
2025-08-05 03:07:30,433 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2117 sites
2025-08-05 03:07:34,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=23.310, H2=10.523, H3=-4.072
2025-08-05 03:07:34,182 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3775 sites
2025-08-05 03:07:40,822 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=10.250, H2=5.830, H3=6.036
2025-08-05 03:07:40,848 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 269 sites
2025-08-05 03:07:41,321 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-2.403, H2=-4.682, H3=1.830
2025-08-05 03:07:41,346 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 218 sites
2025-08-05 03:07:41,726 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.203, H2=-4.739, H3=1.301
2025-08-05 03:07:41,753 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 378 sites
2025-08-05 03:07:42,410 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-5.045, H2=0.497, H3=-1.635
2025-08-05 03:07:42,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 852 sites
2025-08-05 03:07:43,928 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=19.183, H2=-2.667, H3=-6.901
2025-08-05 03:07:43,974 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2313 sites
2025-08-05 03:07:48,047 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=5.869, H2=3.271, H3=7.308
2025-08-05 03:07:48,076 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2304 sites
2025-08-05 03:07:52,127 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=9.192, H2=5.173, H3=-0.932
2025-08-05 03:07:52,155 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1876 sites
2025-08-05 03:07:55,422 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-1.965, H2=-0.825, H3=2.479
2025-08-05 03:07:55,452 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2099 sites
2025-08-05 03:07:59,180 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=13.202, H2=1.291, H3=-0.965
2025-08-05 03:07:59,207 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1300 sites
2025-08-05 03:08:01,485 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.873, H2=3.058, H3=3.383
2025-08-05 03:08:01,516 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2605 sites
2025-08-05 03:08:06,106 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=8.821, H2=-0.824, H3=0.462
2025-08-05 03:08:06,132 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 571 sites
2025-08-05 03:08:07,131 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-2.785, H2=-1.051, H3=-3.572
2025-08-05 03:08:07,159 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1149 sites
2025-08-05 03:08:09,182 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=2.354, H2=2.541, H3=-1.201
2025-08-05 03:08:09,207 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-08-05 03:08:09,208 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-08-05 03:08:09,210 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-08-05 03:08:09,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 03:08:11,169 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 03:08:11,198 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 03:08:14,428 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 03:08:14,457 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1804 sites
2025-08-05 03:08:17,632 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.672, H2=-3.091, H3=-1.300
2025-08-05 03:08:17,661 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1881 sites
2025-08-05 03:08:20,967 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.830, H2=8.267, H3=5.202
2025-08-05 03:08:20,995 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1272 sites
2025-08-05 03:08:23,226 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=20.981, H2=0.040, H3=0.364
2025-08-05 03:08:23,265 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1099 sites
2025-08-05 03:08:25,181 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.912, H2=-3.421, H3=-2.509
2025-08-05 03:08:25,211 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2134 sites
2025-08-05 03:08:28,952 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.709, H2=2.435, H3=-5.595
2025-08-05 03:08:28,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3070 sites
2025-08-05 03:08:34,376 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=25.080, H2=-0.423, H3=-0.612
2025-08-05 03:08:34,407 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2916 sites
2025-08-05 03:08:39,522 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=31.033, H2=-11.784, H3=-3.452
2025-08-05 03:08:39,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1880 sites
2025-08-05 03:08:42,850 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.695, H2=-1.710, H3=-0.930
2025-08-05 03:08:42,879 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1942 sites
2025-08-05 03:08:46,284 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.705, H2=-0.220, H3=1.986
2025-08-05 03:08:46,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4198 sites
2025-08-05 03:08:53,667 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=18.942, H2=6.964, H3=3.579
2025-08-05 03:08:53,698 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3001 sites
2025-08-05 03:08:58,983 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.244, H2=-1.053, H3=-1.062
2025-08-05 03:08:59,013 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2402 sites
2025-08-05 03:09:03,283 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.561, H2=0.686, H3=-0.440
2025-08-05 03:09:03,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2117 sites
2025-08-05 03:09:07,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=23.310, H2=10.523, H3=-4.072
2025-08-05 03:09:07,127 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3775 sites
2025-08-05 03:09:13,906 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=10.250, H2=5.830, H3=6.036
2025-08-05 03:09:13,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 269 sites
2025-08-05 03:09:14,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-2.403, H2=-4.682, H3=1.830
2025-08-05 03:09:14,439 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 218 sites
2025-08-05 03:09:14,830 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.203, H2=-4.739, H3=1.301
2025-08-05 03:09:14,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 378 sites
2025-08-05 03:09:15,522 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-5.045, H2=0.497, H3=-1.635
2025-08-05 03:09:15,547 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 343 sites
2025-08-05 03:09:16,157 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.687, H2=3.895, H3=-3.166
2025-08-05 03:09:16,184 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 509 sites
2025-08-05 03:09:17,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=7.882, H2=-10.340, H3=-5.842
2025-08-05 03:09:17,114 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2313 sites
2025-08-05 03:09:21,220 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=7.158, H2=4.686, H3=7.016
2025-08-05 03:09:21,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2304 sites
2025-08-05 03:09:25,312 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=6.683, H2=4.325, H3=-0.503
2025-08-05 03:09:25,341 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1876 sites
2025-08-05 03:09:28,632 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-2.640, H2=-1.251, H3=4.377
2025-08-05 03:09:28,661 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2099 sites
2025-08-05 03:09:32,349 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=13.534, H2=0.233, H3=-1.055
2025-08-05 03:09:32,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1300 sites
2025-08-05 03:09:34,654 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-1.366, H2=3.095, H3=3.312
2025-08-05 03:09:34,684 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2605 sites
2025-08-05 03:09:39,248 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=8.786, H2=-0.530, H3=0.326
2025-08-05 03:09:39,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 571 sites
2025-08-05 03:09:40,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-3.346, H2=-1.545, H3=-2.481
2025-08-05 03:09:40,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1149 sites
2025-08-05 03:09:42,324 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=2.158, H2=1.784, H3=-1.779
2025-08-05 03:09:42,349 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-08-05 03:09:42,349 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-08-05 03:09:42,351 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-08-05 03:09:42,379 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 03:09:44,292 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 03:09:44,321 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 03:09:47,531 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 03:09:47,561 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1804 sites
2025-08-05 03:09:50,749 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.672, H2=-3.091, H3=-1.300
2025-08-05 03:09:50,779 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1881 sites
2025-08-05 03:09:54,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.830, H2=8.267, H3=5.202
2025-08-05 03:09:54,099 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1272 sites
2025-08-05 03:09:56,342 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=20.981, H2=0.040, H3=0.364
2025-08-05 03:09:56,369 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1099 sites
2025-08-05 03:09:58,294 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.912, H2=-3.421, H3=-2.509
2025-08-05 03:09:58,324 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2134 sites
2025-08-05 03:10:02,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.709, H2=2.435, H3=-5.595
2025-08-05 03:10:02,130 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3070 sites
2025-08-05 03:10:07,502 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=25.080, H2=-0.423, H3=-0.612
2025-08-05 03:10:07,533 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2916 sites
2025-08-05 03:10:12,685 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=31.033, H2=-11.784, H3=-3.452
2025-08-05 03:10:12,714 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1880 sites
2025-08-05 03:10:16,001 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.695, H2=-1.710, H3=-0.930
2025-08-05 03:10:16,030 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1942 sites
2025-08-05 03:10:19,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.705, H2=-0.220, H3=1.986
2025-08-05 03:10:19,501 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4198 sites
2025-08-05 03:10:26,839 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=18.942, H2=6.964, H3=3.579
2025-08-05 03:10:26,870 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3001 sites
2025-08-05 03:10:32,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.244, H2=-1.053, H3=-1.062
2025-08-05 03:10:32,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2402 sites
2025-08-05 03:10:36,426 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.561, H2=0.686, H3=-0.440
2025-08-05 03:10:36,457 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2117 sites
2025-08-05 03:10:40,212 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=23.310, H2=10.523, H3=-4.072
2025-08-05 03:10:40,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3775 sites
2025-08-05 03:10:46,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=10.250, H2=5.830, H3=6.036
2025-08-05 03:10:46,847 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 269 sites
2025-08-05 03:10:47,321 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-2.403, H2=-4.682, H3=1.830
2025-08-05 03:10:47,346 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 218 sites
2025-08-05 03:10:47,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.203, H2=-4.739, H3=1.301
2025-08-05 03:10:47,755 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 378 sites
2025-08-05 03:10:48,420 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-5.045, H2=0.497, H3=-1.635
2025-08-05 03:10:48,446 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 343 sites
2025-08-05 03:10:49,049 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.687, H2=3.895, H3=-3.166
2025-08-05 03:10:49,074 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 509 sites
2025-08-05 03:10:49,971 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=7.882, H2=-10.340, H3=-5.842
2025-08-05 03:10:50,000 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2313 sites
2025-08-05 03:10:54,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=7.158, H2=4.686, H3=7.016
2025-08-05 03:10:54,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2304 sites
2025-08-05 03:10:58,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=6.683, H2=4.325, H3=-0.503
2025-08-05 03:10:58,206 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1876 sites
2025-08-05 03:11:01,506 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-2.640, H2=-1.251, H3=4.377
2025-08-05 03:11:01,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 611 sites
2025-08-05 03:11:02,598 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=5.530, H2=-0.564, H3=-3.195
2025-08-05 03:11:02,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1488 sites
2025-08-05 03:11:05,233 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=2.531, H2=-2.006, H3=0.440
2025-08-05 03:11:05,261 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1300 sites
2025-08-05 03:11:07,533 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.986, H2=2.366, H3=3.133
2025-08-05 03:11:07,563 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2605 sites
2025-08-05 03:11:12,145 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=6.493, H2=-0.546, H3=0.419
2025-08-05 03:11:12,171 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 571 sites
2025-08-05 03:11:13,175 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-2.891, H2=-1.460, H3=-3.809
2025-08-05 03:11:13,202 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1149 sites
2025-08-05 03:11:15,224 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=1.878, H2=2.524, H3=-1.374
2025-08-05 03:11:15,249 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=30 has insufficient data
2025-08-05 03:11:15,249 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=31
2025-08-05 03:11:15,251 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31 -9223372036854775808]
2025-08-05 03:11:15,278 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1096 sites
2025-08-05 03:11:17,206 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=18.358, H2=-2.306, H3=-0.804
2025-08-05 03:11:17,236 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1838 sites
2025-08-05 03:11:20,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=26.512, H2=0.986, H3=-0.225
2025-08-05 03:11:20,497 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1804 sites
2025-08-05 03:11:23,666 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.672, H2=-3.091, H3=-1.300
2025-08-05 03:11:23,694 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1881 sites
2025-08-05 03:11:27,006 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.830, H2=8.267, H3=5.202
2025-08-05 03:11:27,034 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1272 sites
2025-08-05 03:11:29,271 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=20.981, H2=0.040, H3=0.364
2025-08-05 03:11:29,298 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1099 sites
2025-08-05 03:11:31,226 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.912, H2=-3.421, H3=-2.509
2025-08-05 03:11:31,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2134 sites
2025-08-05 03:11:35,008 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.709, H2=2.435, H3=-5.595
2025-08-05 03:11:35,039 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3070 sites
2025-08-05 03:11:40,438 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=25.080, H2=-0.423, H3=-0.612
2025-08-05 03:11:40,468 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2916 sites
2025-08-05 03:11:45,593 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=31.033, H2=-11.784, H3=-3.452
2025-08-05 03:11:45,622 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1880 sites
2025-08-05 03:11:48,934 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.695, H2=-1.710, H3=-0.930
2025-08-05 03:11:48,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1942 sites
2025-08-05 03:11:52,365 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.705, H2=-0.220, H3=1.986
2025-08-05 03:11:52,398 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4198 sites
2025-08-05 03:11:59,778 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=18.942, H2=6.964, H3=3.579
2025-08-05 03:11:59,809 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3001 sites
