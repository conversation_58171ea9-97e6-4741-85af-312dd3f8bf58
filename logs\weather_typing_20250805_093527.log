2025-08-05 09:35:27,456 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-08-05 09:35:27,456 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-05 09:35:27,456 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_16_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 09:35:27,456 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_16_hierarchical_singlevariable
2025-08-05 09:35:27,456 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-08-05 09:35:27,456 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-08-05 09:35:27,456 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-08-05 09:35:27,461 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-08-05 09:35:27,462 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_16_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 09:35:27,462 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-08-05 09:35:28,830 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-08-05 09:35:29,695 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-08-05 09:35:29,699 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-08-05 09:35:29,761 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-08-05 09:35:29,763 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-08-05 09:35:29,764 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-08-05 09:35:29,766 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-08-05 09:35:29,768 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-08-05 09:35:29,792 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-08-05 09:35:29,850 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-08-05 09:35:29,852 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-08-05 09:35:29,854 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-08-05 09:35:29,856 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-08-05 09:35:29,857 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-08-05 09:35:29,859 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-08-05 09:35:29,862 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-08-05 09:35:29,863 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-08-05 09:35:29,864 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-08-05 09:35:29,865 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-08-05 09:35:29,869 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-08-05 09:35:29,869 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-08-05 09:35:29,872 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-08-05 09:35:29,872 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-08-05 09:35:29,872 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-08-05 09:35:29,874 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-08-05 09:35:29,876 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-08-05 09:35:29,877 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-08-05 09:35:29,880 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-08-05 09:35:29,881 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-08-05 09:35:29,884 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-08-05 09:35:29,886 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-08-05 09:35:29,888 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-08-05 09:35:29,891 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-08-05 09:35:29,892 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-08-05 09:35:29,894 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-08-05 09:35:29,895 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-08-05 09:35:29,898 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-08-05 09:35:29,900 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-08-05 09:35:29,902 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-08-05 09:35:29,912 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-08-05 09:35:29,913 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-08-05 09:35:29,913 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-08-05 09:35:29,914 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-08-05 09:35:29,915 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-08-05 09:35:29,918 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-08-05 09:35:29,918 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-08-05 09:35:29,920 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-08-05 09:35:29,921 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-08-05 09:35:29,923 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-08-05 09:35:29,923 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-08-05 09:35:29,925 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-08-05 09:35:29,925 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-08-05 09:35:29,927 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-08-05 09:35:29,927 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-08-05 09:35:29,929 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-08-05 09:35:29,930 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-08-05 09:35:29,932 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-08-05 09:35:29,932 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-08-05 09:35:29,935 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-08-05 09:35:29,935 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-08-05 09:35:29,938 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-08-05 09:35:29,938 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-08-05 09:35:29,941 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-08-05 09:35:29,941 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-08-05 09:35:29,944 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-08-05 09:35:29,944 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-08-05 09:35:29,947 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-08-05 09:35:29,947 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-08-05 09:35:29,950 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-08-05 09:35:29,950 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-08-05 09:35:29,953 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-08-05 09:35:29,953 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-08-05 09:35:29,956 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-08-05 09:35:29,956 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-08-05 09:35:29,959 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-08-05 09:35:29,959 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-08-05 09:35:29,962 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-08-05 09:35:29,962 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-08-05 09:35:29,964 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-08-05 09:35:29,964 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-08-05 09:35:29,967 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-08-05 09:35:29,967 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-08-05 09:35:29,970 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-08-05 09:35:29,970 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-08-05 09:35:29,973 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-08-05 09:35:29,974 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-08-05 09:35:29,976 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-08-05 09:35:29,977 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-08-05 09:35:29,979 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-08-05 09:35:29,979 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-08-05 09:35:29,982 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-08-05 09:35:29,982 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-08-05 09:35:29,985 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-08-05 09:35:29,985 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-08-05 09:35:29,987 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-08-05 09:35:29,988 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-08-05 09:35:29,991 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-08-05 09:35:29,991 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-08-05 09:35:29,994 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-08-05 09:35:29,994 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-08-05 09:35:29,997 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-08-05 09:35:29,997 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-08-05 09:35:30,000 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-08-05 09:35:30,000 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-08-05 09:35:30,002 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-08-05 09:35:30,002 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-08-05 09:35:30,005 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-08-05 09:35:30,005 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-08-05 09:35:30,008 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-08-05 09:35:30,008 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-08-05 09:35:30,011 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-08-05 09:35:30,011 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-08-05 09:35:30,014 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-08-05 09:35:30,014 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-08-05 09:35:30,017 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-08-05 09:35:30,017 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-08-05 09:35:30,020 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-08-05 09:35:30,020 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-08-05 09:35:30,023 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-08-05 09:35:30,023 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-08-05 09:35:30,025 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-08-05 09:35:30,026 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-08-05 09:35:30,028 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-08-05 09:35:30,029 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-08-05 09:35:30,031 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-08-05 09:35:30,032 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-08-05 09:35:30,034 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-08-05 09:35:30,034 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-08-05 09:35:30,037 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-08-05 09:35:30,037 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-08-05 09:35:30,040 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-08-05 09:35:30,040 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-08-05 09:35:30,043 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-08-05 09:35:30,043 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-08-05 09:35:30,046 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-08-05 09:35:30,046 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-08-05 09:35:30,048 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-08-05 09:35:30,049 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-08-05 09:35:30,051 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-08-05 09:35:30,052 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-08-05 09:35:30,054 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-08-05 09:35:30,055 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-08-05 09:35:30,057 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-08-05 09:35:30,058 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-08-05 09:35:30,060 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-08-05 09:35:30,061 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-08-05 09:35:30,063 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-08-05 09:35:30,063 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-08-05 09:35:30,066 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-08-05 09:35:30,066 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-08-05 09:35:30,069 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-08-05 09:35:30,069 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-08-05 09:35:30,071 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-08-05 09:35:30,071 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-08-05 09:35:30,074 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-08-05 09:35:30,074 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-08-05 09:35:30,077 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-08-05 09:35:30,077 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-08-05 09:35:30,080 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-08-05 09:35:30,080 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-08-05 09:35:30,083 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-08-05 09:35:30,083 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-08-05 09:35:30,086 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-08-05 09:35:30,086 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-08-05 09:35:30,089 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-08-05 09:35:30,089 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-08-05 09:35:30,091 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-08-05 09:35:30,092 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-08-05 09:35:30,094 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-08-05 09:35:30,094 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-08-05 09:35:30,097 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-08-05 09:35:30,097 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-08-05 09:35:30,100 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-08-05 09:35:30,100 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-08-05 09:35:30,103 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-08-05 09:35:30,103 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-08-05 09:35:30,106 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-08-05 09:35:30,106 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-08-05 09:35:30,108 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-08-05 09:35:30,108 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-08-05 09:35:30,111 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-08-05 09:35:30,111 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-08-05 09:35:30,114 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-08-05 09:35:30,114 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-08-05 09:35:30,116 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-08-05 09:35:30,117 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-08-05 09:35:30,119 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-08-05 09:35:30,120 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-08-05 09:35:30,122 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-08-05 09:35:30,123 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-08-05 09:35:30,125 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-08-05 09:35:30,125 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-08-05 09:35:30,128 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-08-05 09:35:30,128 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-08-05 09:35:30,131 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-08-05 09:35:30,131 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-08-05 09:35:30,134 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-08-05 09:35:30,134 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-08-05 09:35:30,137 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-08-05 09:35:30,137 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-08-05 09:35:30,139 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-08-05 09:35:30,139 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-08-05 09:35:30,151 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-08-05 09:35:30,151 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-08-05 09:35:30,463 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-08-05 09:35:30,463 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-08-05 09:35:30,466 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-08-05 09:35:30,466 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-08-05 09:35:30,469 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-08-05 09:35:30,469 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-08-05 09:35:30,471 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-08-05 09:35:30,472 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-08-05 09:35:30,474 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-08-05 09:35:30,474 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-08-05 09:35:30,477 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-08-05 09:35:30,477 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-08-05 09:35:30,480 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-08-05 09:35:30,480 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-08-05 09:35:30,483 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-08-05 09:35:30,483 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-08-05 09:35:30,485 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-08-05 09:35:30,486 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-08-05 09:35:30,488 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-08-05 09:35:30,489 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-08-05 09:35:30,491 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-08-05 09:35:30,491 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-08-05 09:35:30,494 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-08-05 09:35:30,494 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-08-05 09:35:30,497 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-08-05 09:35:30,497 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-08-05 09:35:30,500 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-08-05 09:35:30,500 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-08-05 09:35:30,502 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-08-05 09:35:30,502 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-08-05 09:35:30,505 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-08-05 09:35:30,505 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-08-05 09:35:30,508 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-08-05 09:35:30,508 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-08-05 09:35:30,511 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-08-05 09:35:30,511 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-08-05 09:35:30,513 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-08-05 09:35:30,514 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-08-05 09:35:30,516 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-08-05 09:35:30,516 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-08-05 09:35:30,519 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-08-05 09:35:30,519 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-08-05 09:35:30,522 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-08-05 09:35:30,522 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-08-05 09:35:30,524 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-08-05 09:35:30,524 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-08-05 09:35:30,527 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-08-05 09:35:30,527 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-08-05 09:35:30,530 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-08-05 09:35:30,530 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-08-05 09:35:30,533 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-08-05 09:35:30,533 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-08-05 09:35:30,536 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-08-05 09:35:30,536 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-08-05 09:35:30,538 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-08-05 09:35:30,538 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-08-05 09:35:30,541 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-08-05 09:35:30,541 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-08-05 09:35:30,544 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-08-05 09:35:30,544 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-08-05 09:35:30,546 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-08-05 09:35:30,546 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-08-05 09:35:30,549 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-08-05 09:35:30,549 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-08-05 09:35:30,552 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-08-05 09:35:30,552 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-08-05 09:35:30,555 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-08-05 09:35:30,555 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-08-05 09:35:30,557 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-08-05 09:35:30,558 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-08-05 09:35:30,560 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-08-05 09:35:30,560 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-08-05 09:35:30,563 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-08-05 09:35:30,563 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-08-05 09:35:30,565 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-08-05 09:35:30,566 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-08-05 09:35:30,568 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-08-05 09:35:30,568 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-08-05 09:35:30,571 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-08-05 09:35:30,571 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-08-05 09:35:30,574 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-08-05 09:35:30,574 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-08-05 09:35:30,576 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-08-05 09:35:30,577 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-08-05 09:35:30,579 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-08-05 09:35:30,579 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-08-05 09:35:30,582 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-08-05 09:35:30,582 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-08-05 09:35:30,584 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-08-05 09:35:30,585 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_16_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-05 09:35:32,837 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-08-05 09:35:32,838 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-08-05 09:35:32,838 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-08-05 09:35:32,838 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_16_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 09:35:32,838 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_16_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-05 09:35:32,838 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 125 variables
2025-08-05 09:35:32,838 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125
2025-08-05 09:35:32,838 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-08-05 09:35:32,841 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_16_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-05 09:35:32,841 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-08-05 09:35:32,841 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-05 09:35:32,904 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-08-05 09:35:58,065 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_16_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-05 09:35:59,182 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-08-05 09:35:59,182 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-08-05 09:35:59,182 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-08-05 09:35:59,197 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-08-05 09:35:59,265 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 23184 sites
2025-08-05 09:36:40,219 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=121.803, H2=7.335, H3=0.383
2025-08-05 09:36:40,299 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 29175 sites
2025-08-05 09:37:31,389 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=84.165, H2=32.590, H3=7.250
2025-08-05 09:37:31,460 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-08-05 09:37:31,460 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-08-05 09:37:31,462 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-08-05 09:37:31,528 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 23184 sites
2025-08-05 09:38:12,405 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=121.803, H2=7.335, H3=0.383
2025-08-05 09:38:12,463 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 17120 sites
2025-08-05 09:38:42,750 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=35.094, H2=12.817, H3=3.350
2025-08-05 09:38:42,806 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 12055 sites
2025-08-05 09:39:03,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=86.203, H2=28.408, H3=9.971
2025-08-05 09:39:03,979 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-08-05 09:39:03,980 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-08-05 09:39:03,981 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-08-05 09:39:04,037 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17364 sites
2025-08-05 09:39:34,501 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=109.790, H2=0.285, H3=-0.880
2025-08-05 09:39:34,538 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5820 sites
2025-08-05 09:39:44,839 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=21.982, H2=5.735, H3=1.919
2025-08-05 09:39:44,894 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 17120 sites
2025-08-05 09:40:15,077 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.322, H2=12.508, H3=2.529
2025-08-05 09:40:15,125 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 12055 sites
2025-08-05 09:40:36,441 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=90.308, H2=29.200, H3=10.063
2025-08-05 09:40:36,466 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-08-05 09:40:36,467 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-08-05 09:40:36,468 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-08-05 09:40:36,524 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17364 sites
2025-08-05 09:41:07,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=109.790, H2=0.285, H3=-0.880
2025-08-05 09:41:07,156 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5820 sites
2025-08-05 09:41:17,455 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=21.982, H2=5.735, H3=1.919
2025-08-05 09:41:17,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 17120 sites
2025-08-05 09:41:47,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.322, H2=12.508, H3=2.529
2025-08-05 09:41:48,001 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 10261 sites
2025-08-05 09:42:06,248 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=52.553, H2=18.943, H3=9.241
2025-08-05 09:42:06,277 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1794 sites
2025-08-05 09:42:09,442 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=25.190, H2=-1.455, H3=-2.513
2025-08-05 09:42:09,467 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-08-05 09:42:09,467 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-08-05 09:42:09,469 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-08-05 09:42:09,525 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17364 sites
2025-08-05 09:42:40,475 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=109.790, H2=0.285, H3=-0.880
2025-08-05 09:42:40,513 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5820 sites
2025-08-05 09:42:50,881 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=21.982, H2=5.735, H3=1.919
2025-08-05 09:42:50,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7919 sites
2025-08-05 09:43:04,911 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=24.788, H2=9.705, H3=-2.382
2025-08-05 09:43:04,953 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 9201 sites
2025-08-05 09:43:21,292 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.497, H2=6.832, H3=5.167
2025-08-05 09:43:21,336 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 10261 sites
2025-08-05 09:43:39,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=55.797, H2=14.339, H3=5.559
2025-08-05 09:43:39,573 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1794 sites
2025-08-05 09:43:42,763 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=23.646, H2=-0.433, H3=-1.700
2025-08-05 09:43:42,788 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-08-05 09:43:42,788 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-08-05 09:43:42,790 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-08-05 09:43:42,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 17364 sites
2025-08-05 09:44:13,624 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=109.790, H2=0.285, H3=-0.880
2025-08-05 09:44:13,655 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2535 sites
2025-08-05 09:44:18,144 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=10.057, H2=3.738, H3=0.278
2025-08-05 09:44:18,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3285 sites
2025-08-05 09:44:23,959 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=1.872, H2=2.230, H3=2.087
2025-08-05 09:44:23,999 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7919 sites
2025-08-05 09:44:38,009 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=24.744, H2=9.485, H3=-2.680
2025-08-05 09:44:38,051 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9201 sites
2025-08-05 09:44:54,290 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.451, H2=5.949, H3=3.232
2025-08-05 09:44:54,334 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 10261 sites
2025-08-05 09:45:12,404 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=53.772, H2=15.431, H3=6.776
2025-08-05 09:45:12,434 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1794 sites
2025-08-05 09:45:15,575 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=23.428, H2=-0.675, H3=-1.937
2025-08-05 09:45:15,599 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-08-05 09:45:15,599 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-08-05 09:45:15,601 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-08-05 09:45:15,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6477 sites
2025-08-05 09:45:27,047 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=65.269, H2=1.142, H3=0.587
2025-08-05 09:45:27,092 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 10887 sites
2025-08-05 09:45:46,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=70.656, H2=-0.458, H3=-1.939
2025-08-05 09:45:46,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2535 sites
2025-08-05 09:45:50,721 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.579, H2=4.729, H3=0.328
2025-08-05 09:45:50,753 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3285 sites
2025-08-05 09:45:56,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=1.992, H2=3.155, H3=2.137
2025-08-05 09:45:56,584 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7919 sites
2025-08-05 09:46:10,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=32.762, H2=8.749, H3=-2.284
2025-08-05 09:46:10,546 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 9201 sites
2025-08-05 09:46:26,699 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.904, H2=5.907, H3=3.911
2025-08-05 09:46:26,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 10261 sites
2025-08-05 09:46:44,792 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=37.414, H2=15.488, H3=6.609
2025-08-05 09:46:44,821 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1794 sites
2025-08-05 09:46:47,981 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=23.106, H2=-0.503, H3=-2.371
2025-08-05 09:46:48,006 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-08-05 09:46:48,007 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-08-05 09:46:48,008 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-08-05 09:46:48,046 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6477 sites
2025-08-05 09:46:59,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=65.269, H2=1.142, H3=0.587
2025-08-05 09:46:59,497 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 10887 sites
2025-08-05 09:47:18,649 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=70.656, H2=-0.458, H3=-1.939
2025-08-05 09:47:18,679 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2535 sites
2025-08-05 09:47:23,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.579, H2=4.729, H3=0.328
2025-08-05 09:47:23,149 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3285 sites
2025-08-05 09:47:28,919 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=1.992, H2=3.155, H3=2.137
2025-08-05 09:47:28,959 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7919 sites
2025-08-05 09:47:42,887 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=32.762, H2=8.749, H3=-2.284
2025-08-05 09:47:42,929 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 9201 sites
2025-08-05 09:47:59,185 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.904, H2=5.907, H3=3.911
2025-08-05 09:47:59,229 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 10261 sites
2025-08-05 09:48:17,151 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=37.414, H2=15.488, H3=6.609
2025-08-05 09:48:17,179 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1157 sites
2025-08-05 09:48:19,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.998, H2=1.016, H3=-5.515
2025-08-05 09:48:19,232 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 637 sites
2025-08-05 09:48:20,363 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.415, H2=-6.517, H3=1.833
2025-08-05 09:48:20,388 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-08-05 09:48:20,388 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-08-05 09:48:20,390 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-08-05 09:48:20,427 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6477 sites
2025-08-05 09:48:31,824 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=65.269, H2=1.142, H3=0.587
2025-08-05 09:48:31,860 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5665 sites
2025-08-05 09:48:41,764 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.948, H2=-0.849, H3=-2.717
2025-08-05 09:48:41,799 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5222 sites
2025-08-05 09:48:50,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=80.571, H2=-1.149, H3=0.132
2025-08-05 09:48:50,925 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2535 sites
2025-08-05 09:48:55,368 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.402, H2=5.327, H3=0.052
2025-08-05 09:48:55,399 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3285 sites
2025-08-05 09:49:01,149 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=1.555, H2=1.507, H3=1.854
2025-08-05 09:49:01,189 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 7919 sites
2025-08-05 09:49:15,114 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=28.446, H2=7.147, H3=-1.942
2025-08-05 09:49:15,156 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 9201 sites
2025-08-05 09:49:31,243 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=11.518, H2=5.957, H3=4.105
2025-08-05 09:49:31,287 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 10261 sites
2025-08-05 09:49:49,360 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=42.248, H2=16.168, H3=6.211
2025-08-05 09:49:49,388 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1157 sites
2025-08-05 09:49:51,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.124, H2=1.085, H3=-4.678
2025-08-05 09:49:51,444 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 637 sites
2025-08-05 09:49:52,562 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.519, H2=-6.280, H3=0.997
2025-08-05 09:49:52,586 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-08-05 09:49:52,587 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-08-05 09:49:52,588 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-08-05 09:49:52,625 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6477 sites
2025-08-05 09:50:03,960 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=65.269, H2=1.142, H3=0.587
2025-08-05 09:50:04,005 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5665 sites
2025-08-05 09:50:13,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.948, H2=-0.849, H3=-2.717
2025-08-05 09:50:14,010 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5222 sites
2025-08-05 09:50:23,120 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=80.571, H2=-1.149, H3=0.132
2025-08-05 09:50:23,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2535 sites
2025-08-05 09:50:27,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.402, H2=5.327, H3=0.052
2025-08-05 09:50:27,608 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3285 sites
2025-08-05 09:50:33,322 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=1.555, H2=1.507, H3=1.854
2025-08-05 09:50:33,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 7919 sites
2025-08-05 09:50:47,206 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=28.446, H2=7.147, H3=-1.942
2025-08-05 09:50:47,248 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 9201 sites
2025-08-05 09:51:03,210 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=11.518, H2=5.957, H3=4.105
2025-08-05 09:51:03,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4415 sites
2025-08-05 09:51:10,987 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=47.613, H2=20.227, H3=7.751
2025-08-05 09:51:11,022 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 5846 sites
2025-08-05 09:51:21,237 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.031, H2=4.167, H3=3.445
2025-08-05 09:51:21,265 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1157 sites
2025-08-05 09:51:23,277 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=27.344, H2=1.305, H3=-4.563
2025-08-05 09:51:23,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 637 sites
2025-08-05 09:51:24,414 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.934, H2=-5.146, H3=2.487
2025-08-05 09:51:24,439 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-08-05 09:51:24,439 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-08-05 09:51:24,441 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-08-05 09:51:24,477 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6477 sites
2025-08-05 09:51:35,788 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=65.269, H2=1.142, H3=0.587
2025-08-05 09:51:35,824 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5665 sites
2025-08-05 09:51:45,790 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=32.948, H2=-0.849, H3=-2.717
2025-08-05 09:51:45,825 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5222 sites
2025-08-05 09:51:54,893 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=80.571, H2=-1.149, H3=0.132
2025-08-05 09:51:54,923 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2535 sites
2025-08-05 09:51:59,359 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.402, H2=5.327, H3=0.052
2025-08-05 09:51:59,390 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3285 sites
2025-08-05 09:52:05,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=1.555, H2=1.507, H3=1.854
2025-08-05 09:52:05,160 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 7919 sites
2025-08-05 09:52:19,025 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=28.446, H2=7.147, H3=-1.942
2025-08-05 09:52:19,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4970 sites
2025-08-05 09:52:27,782 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=11.015, H2=4.371, H3=1.298
2025-08-05 09:52:27,815 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4231 sites
2025-08-05 09:52:35,248 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.510, H2=3.929, H3=4.256
2025-08-05 09:52:35,282 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4415 sites
2025-08-05 09:52:43,087 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=53.881, H2=18.394, H3=8.245
2025-08-05 09:52:43,123 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 5846 sites
2025-08-05 09:52:53,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=26.040, H2=4.280, H3=2.496
2025-08-05 09:52:53,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1157 sites
2025-08-05 09:52:55,631 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=24.229, H2=0.959, H3=-5.844
2025-08-05 09:52:55,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 637 sites
2025-08-05 09:52:56,773 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.324, H2=-8.639, H3=1.591
2025-08-05 09:52:56,811 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-08-05 09:52:56,811 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-08-05 09:52:56,813 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-08-05 09:52:56,841 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 09:52:59,684 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 09:52:59,718 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4867 sites
2025-08-05 09:53:08,309 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=48.592, H2=0.604, H3=0.920
2025-08-05 09:53:08,345 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5665 sites
2025-08-05 09:53:18,428 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.669, H2=-1.202, H3=-3.926
2025-08-05 09:53:18,463 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5222 sites
2025-08-05 09:53:27,701 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=68.733, H2=-1.636, H3=-0.317
2025-08-05 09:53:27,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2535 sites
2025-08-05 09:53:32,255 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=9.311, H2=3.291, H3=0.145
2025-08-05 09:53:32,286 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3285 sites
2025-08-05 09:53:38,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=1.633, H2=3.004, H3=2.465
2025-08-05 09:53:38,103 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 7919 sites
2025-08-05 09:53:51,968 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=16.790, H2=8.232, H3=-2.135
2025-08-05 09:53:52,003 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4970 sites
2025-08-05 09:54:00,634 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=13.129, H2=3.527, H3=1.016
2025-08-05 09:54:00,667 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4231 sites
2025-08-05 09:54:08,096 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.779, H2=4.614, H3=5.365
2025-08-05 09:54:08,130 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4415 sites
2025-08-05 09:54:15,871 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=46.979, H2=20.851, H3=6.572
2025-08-05 09:54:15,907 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 5846 sites
2025-08-05 09:54:26,137 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=17.788, H2=5.706, H3=3.596
2025-08-05 09:54:26,165 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1157 sites
2025-08-05 09:54:28,195 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=35.515, H2=1.041, H3=-4.823
2025-08-05 09:54:28,221 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 637 sites
2025-08-05 09:54:29,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.133, H2=-8.029, H3=1.508
2025-08-05 09:54:29,359 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-08-05 09:54:29,359 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-08-05 09:54:29,361 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-08-05 09:54:29,390 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 09:54:32,219 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 09:54:32,254 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4867 sites
2025-08-05 09:54:40,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=48.592, H2=0.604, H3=0.920
2025-08-05 09:54:40,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5665 sites
2025-08-05 09:54:50,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.669, H2=-1.202, H3=-3.926
2025-08-05 09:54:50,826 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5222 sites
2025-08-05 09:55:00,041 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=68.733, H2=-1.636, H3=-0.317
2025-08-05 09:55:00,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2535 sites
2025-08-05 09:55:04,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=9.311, H2=3.291, H3=0.145
2025-08-05 09:55:04,560 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3285 sites
2025-08-05 09:55:10,363 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=1.633, H2=3.004, H3=2.465
2025-08-05 09:55:10,403 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 7919 sites
2025-08-05 09:55:24,357 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=16.790, H2=8.232, H3=-2.135
2025-08-05 09:55:24,392 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4970 sites
2025-08-05 09:55:33,090 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=13.129, H2=3.527, H3=1.016
2025-08-05 09:55:33,123 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4231 sites
2025-08-05 09:55:40,536 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.779, H2=4.614, H3=5.365
2025-08-05 09:55:40,569 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4415 sites
2025-08-05 09:55:48,342 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=46.979, H2=20.851, H3=6.572
2025-08-05 09:55:48,382 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 727 sites
2025-08-05 09:55:49,654 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-4.906, H2=-3.587, H3=-5.530
2025-08-05 09:55:49,688 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 5119 sites
2025-08-05 09:55:58,642 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.770, H2=4.260, H3=5.112
2025-08-05 09:55:58,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1157 sites
2025-08-05 09:56:00,699 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=22.645, H2=1.066, H3=-4.622
2025-08-05 09:56:00,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 637 sites
2025-08-05 09:56:01,843 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.771, H2=-7.966, H3=1.447
2025-08-05 09:56:01,868 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-08-05 09:56:01,868 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-08-05 09:56:01,870 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-08-05 09:56:01,897 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 09:56:04,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 09:56:04,771 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4867 sites
2025-08-05 09:56:13,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=48.592, H2=0.604, H3=0.920
2025-08-05 09:56:13,371 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5665 sites
2025-08-05 09:56:23,302 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.669, H2=-1.202, H3=-3.926
2025-08-05 09:56:23,337 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5222 sites
2025-08-05 09:56:32,510 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=68.733, H2=-1.636, H3=-0.317
2025-08-05 09:56:32,540 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2535 sites
2025-08-05 09:56:36,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=9.311, H2=3.291, H3=0.145
2025-08-05 09:56:37,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3285 sites
2025-08-05 09:56:42,828 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=1.633, H2=3.004, H3=2.465
2025-08-05 09:56:42,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1687 sites
2025-08-05 09:56:45,809 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.371, H2=-5.442, H3=-10.380
2025-08-05 09:56:45,846 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6232 sites
2025-08-05 09:56:56,789 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=23.564, H2=12.810, H3=1.999
2025-08-05 09:56:56,823 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4970 sites
2025-08-05 09:57:05,556 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=15.387, H2=3.691, H3=0.899
2025-08-05 09:57:05,589 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4231 sites
2025-08-05 09:57:13,030 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.928, H2=5.027, H3=3.882
2025-08-05 09:57:13,064 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4415 sites
2025-08-05 09:57:20,881 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=45.099, H2=22.813, H3=8.155
2025-08-05 09:57:20,908 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 727 sites
2025-08-05 09:57:22,188 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-9.483, H2=-3.540, H3=-4.752
2025-08-05 09:57:22,223 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 5119 sites
2025-08-05 09:57:31,250 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=16.339, H2=3.540, H3=5.688
2025-08-05 09:57:31,278 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1157 sites
2025-08-05 09:57:33,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=22.419, H2=0.995, H3=-4.251
2025-08-05 09:57:33,341 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 637 sites
2025-08-05 09:57:34,456 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.215, H2=-5.875, H3=2.158
2025-08-05 09:57:34,482 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-08-05 09:57:34,482 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-08-05 09:57:34,483 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-08-05 09:57:34,513 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 09:57:37,358 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 09:57:37,392 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4867 sites
2025-08-05 09:57:45,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=48.592, H2=0.604, H3=0.920
2025-08-05 09:57:46,020 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5665 sites
2025-08-05 09:57:56,027 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.669, H2=-1.202, H3=-3.926
2025-08-05 09:57:56,063 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5222 sites
2025-08-05 09:58:05,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=68.733, H2=-1.636, H3=-0.317
2025-08-05 09:58:05,287 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2535 sites
2025-08-05 09:58:09,752 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=9.311, H2=3.291, H3=0.145
2025-08-05 09:58:09,780 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1489 sites
2025-08-05 09:58:12,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-2.969, H2=-2.314, H3=-1.284
2025-08-05 09:58:12,416 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1796 sites
2025-08-05 09:58:15,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=0.961, H2=4.775, H3=5.864
2025-08-05 09:58:15,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1687 sites
2025-08-05 09:58:18,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.727, H2=-5.073, H3=-11.279
2025-08-05 09:58:18,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6232 sites
2025-08-05 09:58:29,558 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=24.394, H2=12.852, H3=2.453
2025-08-05 09:58:29,592 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4970 sites
2025-08-05 09:58:38,340 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.460, H2=3.609, H3=0.587
2025-08-05 09:58:38,383 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4231 sites
2025-08-05 09:58:45,819 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.110, H2=4.162, H3=5.045
2025-08-05 09:58:45,852 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4415 sites
2025-08-05 09:58:53,702 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=64.160, H2=24.776, H3=8.117
2025-08-05 09:58:53,729 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 727 sites
2025-08-05 09:58:55,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-4.043, H2=-3.629, H3=-4.671
2025-08-05 09:58:55,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 5119 sites
2025-08-05 09:59:04,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=23.460, H2=3.275, H3=5.061
2025-08-05 09:59:04,178 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1157 sites
2025-08-05 09:59:06,241 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=34.006, H2=0.659, H3=-4.162
2025-08-05 09:59:06,267 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 637 sites
2025-08-05 09:59:07,399 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=3.276, H2=-9.796, H3=1.453
2025-08-05 09:59:07,424 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-08-05 09:59:07,425 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-08-05 09:59:07,426 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-08-05 09:59:07,481 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 09:59:10,346 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 09:59:10,381 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4867 sites
2025-08-05 09:59:18,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=48.592, H2=0.604, H3=0.920
2025-08-05 09:59:19,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5665 sites
2025-08-05 09:59:28,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.669, H2=-1.202, H3=-3.926
2025-08-05 09:59:29,025 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5222 sites
2025-08-05 09:59:38,198 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=68.733, H2=-1.636, H3=-0.317
2025-08-05 09:59:38,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2535 sites
2025-08-05 09:59:42,695 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=9.311, H2=3.291, H3=0.145
2025-08-05 09:59:42,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1489 sites
2025-08-05 09:59:45,342 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-2.969, H2=-2.314, H3=-1.284
2025-08-05 09:59:45,371 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1796 sites
2025-08-05 09:59:48,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=0.961, H2=4.775, H3=5.864
2025-08-05 09:59:48,543 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1687 sites
2025-08-05 09:59:51,500 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.727, H2=-5.073, H3=-11.279
2025-08-05 09:59:51,553 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6232 sites
2025-08-05 10:00:02,473 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=24.394, H2=12.852, H3=2.453
2025-08-05 10:00:02,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4970 sites
2025-08-05 10:00:11,174 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.460, H2=3.609, H3=0.587
2025-08-05 10:00:11,207 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4231 sites
2025-08-05 10:00:18,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.110, H2=4.162, H3=5.045
2025-08-05 10:00:18,636 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4415 sites
2025-08-05 10:00:26,384 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=64.160, H2=24.776, H3=8.117
2025-08-05 10:00:26,411 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 727 sites
2025-08-05 10:00:27,681 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-4.043, H2=-3.629, H3=-4.671
2025-08-05 10:00:27,712 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2872 sites
2025-08-05 10:00:32,717 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.406, H2=-0.089, H3=2.698
2025-08-05 10:00:32,746 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2247 sites
2025-08-05 10:00:36,708 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=24.500, H2=3.883, H3=4.323
2025-08-05 10:00:36,735 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1157 sites
2025-08-05 10:00:38,759 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=23.751, H2=1.853, H3=-4.743
2025-08-05 10:00:38,786 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 637 sites
2025-08-05 10:00:39,900 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.446, H2=-6.236, H3=1.513
2025-08-05 10:00:39,925 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-08-05 10:00:39,925 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-08-05 10:00:39,927 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-08-05 10:00:39,954 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 10:00:42,801 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 10:00:42,837 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4867 sites
2025-08-05 10:00:51,450 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=48.592, H2=0.604, H3=0.920
2025-08-05 10:00:51,486 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5665 sites
2025-08-05 10:01:01,501 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.669, H2=-1.202, H3=-3.926
2025-08-05 10:01:01,547 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5222 sites
2025-08-05 10:01:10,720 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=68.733, H2=-1.636, H3=-0.317
2025-08-05 10:01:10,749 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2535 sites
2025-08-05 10:01:15,183 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=9.311, H2=3.291, H3=0.145
2025-08-05 10:01:15,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1489 sites
2025-08-05 10:01:17,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-2.969, H2=-2.314, H3=-1.284
2025-08-05 10:01:17,867 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1796 sites
2025-08-05 10:01:21,019 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=0.961, H2=4.775, H3=5.864
2025-08-05 10:01:21,047 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1687 sites
2025-08-05 10:01:24,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.727, H2=-5.073, H3=-11.279
2025-08-05 10:01:24,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6232 sites
2025-08-05 10:01:34,964 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=24.394, H2=12.852, H3=2.453
2025-08-05 10:01:34,998 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4970 sites
2025-08-05 10:01:43,748 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.460, H2=3.609, H3=0.587
2025-08-05 10:01:43,776 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1305 sites
2025-08-05 10:01:46,053 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-0.077, H2=-0.450, H3=0.811
2025-08-05 10:01:46,083 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2926 sites
2025-08-05 10:01:51,193 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=9.521, H2=3.077, H3=3.235
2025-08-05 10:01:51,225 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4415 sites
2025-08-05 10:01:58,982 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=39.656, H2=20.425, H3=9.243
2025-08-05 10:01:59,009 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 727 sites
2025-08-05 10:02:00,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-5.566, H2=-3.682, H3=-7.104
2025-08-05 10:02:00,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2872 sites
2025-08-05 10:02:05,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.193, H2=-0.006, H3=2.938
2025-08-05 10:02:05,354 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2247 sites
2025-08-05 10:02:09,290 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=22.732, H2=4.558, H3=4.541
2025-08-05 10:02:09,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1157 sites
2025-08-05 10:02:11,337 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=31.372, H2=1.161, H3=-5.408
2025-08-05 10:02:11,363 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 637 sites
2025-08-05 10:02:12,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.338, H2=-7.079, H3=1.516
2025-08-05 10:02:12,501 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-08-05 10:02:12,501 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-08-05 10:02:12,503 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-08-05 10:02:12,531 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 10:02:15,353 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 10:02:15,382 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1980 sites
2025-08-05 10:02:18,862 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=38.106, H2=0.065, H3=-1.171
2025-08-05 10:02:18,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2887 sites
2025-08-05 10:02:23,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=21.705, H2=-0.656, H3=2.133
2025-08-05 10:02:23,982 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5665 sites
2025-08-05 10:02:33,906 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=42.380, H2=-1.462, H3=-3.959
2025-08-05 10:02:33,940 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 5222 sites
2025-08-05 10:02:43,055 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=74.471, H2=-2.181, H3=-0.323
2025-08-05 10:02:43,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2535 sites
2025-08-05 10:02:47,522 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.946, H2=5.314, H3=0.747
2025-08-05 10:02:47,565 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1489 sites
2025-08-05 10:02:50,157 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-5.630, H2=-2.591, H3=-2.057
2025-08-05 10:02:50,185 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1796 sites
2025-08-05 10:02:53,323 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=1.505, H2=3.690, H3=4.776
2025-08-05 10:02:53,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1687 sites
2025-08-05 10:02:56,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.482, H2=-7.823, H3=-9.907
2025-08-05 10:02:56,345 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6232 sites
2025-08-05 10:03:07,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=26.253, H2=12.566, H3=1.824
2025-08-05 10:03:07,169 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4970 sites
2025-08-05 10:03:15,837 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=12.151, H2=6.385, H3=1.433
2025-08-05 10:03:15,865 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1305 sites
2025-08-05 10:03:18,151 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=0.264, H2=-0.975, H3=0.311
2025-08-05 10:03:18,181 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2926 sites
2025-08-05 10:03:23,304 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=4.984, H2=3.249, H3=3.189
2025-08-05 10:03:23,337 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4415 sites
2025-08-05 10:03:31,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=53.863, H2=20.473, H3=8.129
2025-08-05 10:03:31,107 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 727 sites
2025-08-05 10:03:32,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-4.726, H2=-3.019, H3=-6.786
2025-08-05 10:03:32,405 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2872 sites
2025-08-05 10:03:37,396 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=1.220, H2=0.148, H3=2.872
2025-08-05 10:03:37,427 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2247 sites
2025-08-05 10:03:41,358 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=19.350, H2=3.939, H3=4.440
2025-08-05 10:03:41,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1157 sites
2025-08-05 10:03:43,411 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=23.197, H2=1.003, H3=-4.931
2025-08-05 10:03:43,438 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 637 sites
2025-08-05 10:03:44,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=4.395, H2=-7.854, H3=1.578
2025-08-05 10:03:44,591 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-08-05 10:03:44,591 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-08-05 10:03:44,592 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-08-05 10:03:44,620 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 10:03:47,468 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 10:03:47,498 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1980 sites
2025-08-05 10:03:51,015 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=38.106, H2=0.065, H3=-1.171
2025-08-05 10:03:51,046 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2887 sites
2025-08-05 10:03:56,122 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=21.705, H2=-0.656, H3=2.133
2025-08-05 10:03:56,158 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5665 sites
2025-08-05 10:04:06,146 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=42.380, H2=-1.462, H3=-3.959
2025-08-05 10:04:06,177 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2941 sites
2025-08-05 10:04:11,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.007, H2=-0.198, H3=-0.388
2025-08-05 10:04:11,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2281 sites
2025-08-05 10:04:15,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.780, H2=-3.092, H3=-1.167
2025-08-05 10:04:15,443 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2535 sites
2025-08-05 10:04:19,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.933, H2=5.413, H3=0.679
2025-08-05 10:04:19,923 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1489 sites
2025-08-05 10:04:22,543 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-3.776, H2=-2.328, H3=-2.344
2025-08-05 10:04:22,583 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1796 sites
2025-08-05 10:04:25,740 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=2.040, H2=5.596, H3=5.092
2025-08-05 10:04:25,768 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1687 sites
2025-08-05 10:04:28,700 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.408, H2=-5.699, H3=-8.555
2025-08-05 10:04:28,737 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6232 sites
2025-08-05 10:04:39,672 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=21.856, H2=16.580, H3=2.914
2025-08-05 10:04:39,707 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4970 sites
2025-08-05 10:04:48,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=18.639, H2=6.967, H3=1.492
2025-08-05 10:04:48,382 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1305 sites
2025-08-05 10:04:50,660 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-0.225, H2=-0.969, H3=0.179
2025-08-05 10:04:50,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2926 sites
2025-08-05 10:04:55,833 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.788, H2=2.902, H3=3.286
2025-08-05 10:04:55,868 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4415 sites
2025-08-05 10:05:03,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=48.869, H2=20.143, H3=7.384
2025-08-05 10:05:03,582 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 727 sites
2025-08-05 10:05:04,858 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-6.984, H2=-5.285, H3=-5.370
2025-08-05 10:05:04,889 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2872 sites
2025-08-05 10:05:09,911 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=1.599, H2=0.027, H3=3.576
2025-08-05 10:05:09,941 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2247 sites
2025-08-05 10:05:13,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=22.545, H2=4.040, H3=4.812
2025-08-05 10:05:13,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1157 sites
2025-08-05 10:05:15,918 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=30.892, H2=1.219, H3=-5.423
2025-08-05 10:05:15,945 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 637 sites
2025-08-05 10:05:17,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=3.922, H2=-8.303, H3=1.791
2025-08-05 10:05:17,089 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-08-05 10:05:17,089 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-08-05 10:05:17,091 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-08-05 10:05:17,120 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 10:05:19,926 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 10:05:19,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1980 sites
2025-08-05 10:05:23,429 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=38.106, H2=0.065, H3=-1.171
2025-08-05 10:05:23,461 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2887 sites
2025-08-05 10:05:28,481 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=21.705, H2=-0.656, H3=2.133
2025-08-05 10:05:28,517 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5665 sites
2025-08-05 10:05:38,341 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=42.380, H2=-1.462, H3=-3.959
2025-08-05 10:05:38,372 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2941 sites
2025-08-05 10:05:43,495 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.007, H2=-0.198, H3=-0.388
2025-08-05 10:05:43,524 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2281 sites
2025-08-05 10:05:47,484 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.780, H2=-3.092, H3=-1.167
2025-08-05 10:05:47,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 877 sites
2025-08-05 10:05:49,033 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-2.314, H2=2.296, H3=-2.587
2025-08-05 10:05:49,062 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1658 sites
2025-08-05 10:05:51,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.260, H2=1.087, H3=0.434
2025-08-05 10:05:51,991 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1489 sites
2025-08-05 10:05:54,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-4.260, H2=-3.619, H3=-2.102
2025-08-05 10:05:54,612 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1796 sites
2025-08-05 10:05:57,727 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=1.284, H2=3.772, H3=3.727
2025-08-05 10:05:57,755 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1687 sites
2025-08-05 10:06:00,686 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.854, H2=-5.458, H3=-8.915
2025-08-05 10:06:00,722 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6232 sites
2025-08-05 10:06:11,558 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=25.614, H2=13.967, H3=2.778
2025-08-05 10:06:11,592 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4970 sites
2025-08-05 10:06:20,182 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=12.435, H2=3.872, H3=0.628
2025-08-05 10:06:20,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1305 sites
2025-08-05 10:06:22,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.218, H2=-0.804, H3=0.630
2025-08-05 10:06:22,492 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2926 sites
2025-08-05 10:06:27,573 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=6.309, H2=2.836, H3=2.815
2025-08-05 10:06:27,607 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4415 sites
2025-08-05 10:06:35,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=38.492, H2=23.338, H3=5.856
2025-08-05 10:06:35,313 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 727 sites
2025-08-05 10:06:36,580 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-5.597, H2=-3.094, H3=-5.574
2025-08-05 10:06:36,610 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2872 sites
2025-08-05 10:06:41,588 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=1.270, H2=0.207, H3=3.743
2025-08-05 10:06:41,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2247 sites
2025-08-05 10:06:45,532 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=21.515, H2=4.062, H3=4.303
2025-08-05 10:06:45,559 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1157 sites
2025-08-05 10:06:47,567 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=25.782, H2=1.221, H3=-4.973
2025-08-05 10:06:47,594 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 637 sites
2025-08-05 10:06:48,703 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=4.844, H2=-10.021, H3=2.315
2025-08-05 10:06:48,728 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-08-05 10:06:48,728 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-08-05 10:06:48,730 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-08-05 10:06:48,768 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1610 sites
2025-08-05 10:06:51,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=22.248, H2=0.484, H3=-0.334
2025-08-05 10:06:51,613 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1980 sites
2025-08-05 10:06:55,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=38.106, H2=0.065, H3=-1.171
2025-08-05 10:06:55,101 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2887 sites
2025-08-05 10:07:00,170 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=21.705, H2=-0.656, H3=2.133
2025-08-05 10:07:00,199 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1711 sites
2025-08-05 10:07:03,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=19.183, H2=-1.294, H3=0.646
2025-08-05 10:07:03,209 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3954 sites
2025-08-05 10:07:10,169 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=27.345, H2=-1.756, H3=-5.509
2025-08-05 10:07:10,200 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2941 sites
2025-08-05 10:07:15,331 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.145, H2=0.494, H3=0.023
2025-08-05 10:07:15,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2281 sites
2025-08-05 10:07:19,355 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=22.765, H2=-2.598, H3=-1.616
2025-08-05 10:07:19,382 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 877 sites
2025-08-05 10:07:20,905 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-1.610, H2=1.747, H3=-2.598
2025-08-05 10:07:20,959 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1658 sites
2025-08-05 10:07:23,853 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=7.702, H2=1.169, H3=0.987
2025-08-05 10:07:23,883 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1489 sites
2025-08-05 10:07:26,487 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-3.629, H2=-2.637, H3=-1.766
2025-08-05 10:07:26,516 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1796 sites
2025-08-05 10:07:29,648 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.681, H2=4.296, H3=6.869
2025-08-05 10:07:29,677 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1687 sites
2025-08-05 10:07:32,614 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.946, H2=-5.554, H3=-9.922
2025-08-05 10:07:32,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 6232 sites
2025-08-05 10:07:43,575 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=19.389, H2=12.478, H3=1.818
2025-08-05 10:07:43,610 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4970 sites
2025-08-05 10:07:52,264 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=18.565, H2=3.782, H3=1.349
2025-08-05 10:07:52,292 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1305 sites
2025-08-05 10:07:54,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.571, H2=-0.977, H3=0.251
2025-08-05 10:07:54,615 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2926 sites
2025-08-05 10:07:59,801 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=5.189, H2=3.132, H3=2.369
2025-08-05 10:07:59,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4415 sites
2025-08-05 10:08:07,732 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=53.281, H2=16.141, H3=7.648
2025-08-05 10:08:07,759 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 727 sites
2025-08-05 10:08:09,060 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-5.868, H2=-3.612, H3=-4.956
2025-08-05 10:08:09,091 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2872 sites
2025-08-05 10:08:14,212 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=1.215, H2=-0.097, H3=2.457
2025-08-05 10:08:14,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2247 sites
2025-08-05 10:08:18,218 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=20.921, H2=5.639, H3=5.893
2025-08-05 10:08:18,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1157 sites
2025-08-05 10:08:20,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=32.700, H2=1.070, H3=-5.475
2025-08-05 10:08:20,313 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 637 sites
2025-08-05 10:08:21,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=3.134, H2=-6.521, H3=1.660
2025-08-05 10:08:21,461 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-08-05 10:08:21,461 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-08-05 10:08:21,463 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-08-05 10:08:21,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:08:22,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:08:22,041 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:08:24,345 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:08:24,374 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:08:27,860 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:08:27,891 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2887 sites
2025-08-05 10:08:32,935 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=20.914, H2=-0.355, H3=2.502
2025-08-05 10:08:32,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1711 sites
2025-08-05 10:08:35,956 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.830, H2=-2.343, H3=0.815
2025-08-05 10:08:35,988 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3954 sites
2025-08-05 10:08:42,891 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=22.339, H2=-0.597, H3=-5.177
2025-08-05 10:08:42,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2941 sites
2025-08-05 10:08:48,082 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=20.851, H2=0.602, H3=-0.134
2025-08-05 10:08:48,112 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2281 sites
2025-08-05 10:08:52,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=21.012, H2=-2.363, H3=-1.930
2025-08-05 10:08:52,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 877 sites
2025-08-05 10:08:53,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-2.211, H2=2.211, H3=-2.776
2025-08-05 10:08:53,694 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1658 sites
2025-08-05 10:08:56,583 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=7.470, H2=0.937, H3=0.439
2025-08-05 10:08:56,611 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1489 sites
2025-08-05 10:08:59,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-4.814, H2=-2.754, H3=-1.430
2025-08-05 10:08:59,234 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1796 sites
2025-08-05 10:09:02,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=2.296, H2=5.696, H3=5.483
2025-08-05 10:09:02,384 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1687 sites
2025-08-05 10:09:05,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=5.246, H2=-6.430, H3=-10.328
2025-08-05 10:09:05,347 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 6232 sites
2025-08-05 10:09:16,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=23.157, H2=10.636, H3=2.477
2025-08-05 10:09:16,234 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4970 sites
2025-08-05 10:09:24,869 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=18.174, H2=4.520, H3=1.043
2025-08-05 10:09:24,897 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1305 sites
2025-08-05 10:09:27,169 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-0.674, H2=-0.695, H3=0.347
2025-08-05 10:09:27,201 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2926 sites
2025-08-05 10:09:32,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=5.515, H2=2.466, H3=3.191
2025-08-05 10:09:32,358 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 4415 sites
2025-08-05 10:09:40,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=49.004, H2=15.522, H3=6.826
2025-08-05 10:09:40,097 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 727 sites
2025-08-05 10:09:41,373 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-7.676, H2=-3.551, H3=-7.508
2025-08-05 10:09:41,404 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2872 sites
2025-08-05 10:09:46,414 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.615, H2=0.006, H3=4.384
2025-08-05 10:09:46,443 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2247 sites
2025-08-05 10:09:50,346 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=35.567, H2=4.849, H3=4.683
2025-08-05 10:09:50,374 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1157 sites
2025-08-05 10:09:52,391 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=35.747, H2=2.303, H3=-5.422
2025-08-05 10:09:52,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 637 sites
2025-08-05 10:09:53,529 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=3.353, H2=-7.729, H3=1.239
2025-08-05 10:09:53,556 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-08-05 10:09:53,556 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-08-05 10:09:53,557 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-08-05 10:09:53,601 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:09:54,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:09:54,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:09:56,428 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:09:56,457 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:09:59,920 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:09:59,969 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2887 sites
2025-08-05 10:10:05,061 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=20.914, H2=-0.355, H3=2.502
2025-08-05 10:10:05,089 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1711 sites
2025-08-05 10:10:08,066 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.830, H2=-2.343, H3=0.815
2025-08-05 10:10:08,094 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 820 sites
2025-08-05 10:10:09,526 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-1.136, H2=-2.487, H3=-0.057
2025-08-05 10:10:09,557 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3134 sites
2025-08-05 10:10:15,012 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.928, H2=0.192, H3=-6.404
2025-08-05 10:10:15,044 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2941 sites
2025-08-05 10:10:20,166 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.964, H2=0.382, H3=-0.391
2025-08-05 10:10:20,196 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2281 sites
2025-08-05 10:10:24,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.910, H2=-4.908, H3=-1.327
2025-08-05 10:10:24,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 877 sites
2025-08-05 10:10:25,772 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-1.485, H2=2.050, H3=-2.354
2025-08-05 10:10:25,802 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1658 sites
2025-08-05 10:10:28,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.640, H2=1.254, H3=1.374
2025-08-05 10:10:28,716 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1489 sites
2025-08-05 10:10:31,319 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-3.829, H2=-2.953, H3=-1.654
2025-08-05 10:10:31,349 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1796 sites
2025-08-05 10:10:34,499 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.662, H2=4.199, H3=3.836
2025-08-05 10:10:34,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1687 sites
2025-08-05 10:10:37,453 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.007, H2=-5.961, H3=-10.194
2025-08-05 10:10:37,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 6232 sites
2025-08-05 10:10:48,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=22.609, H2=11.300, H3=2.189
2025-08-05 10:10:48,409 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4970 sites
2025-08-05 10:10:57,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=12.067, H2=4.111, H3=0.676
2025-08-05 10:10:57,085 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1305 sites
2025-08-05 10:10:59,365 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.094, H2=-0.722, H3=0.333
2025-08-05 10:10:59,407 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2926 sites
2025-08-05 10:11:04,495 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=5.520, H2=2.673, H3=3.896
2025-08-05 10:11:04,529 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 4415 sites
2025-08-05 10:11:12,223 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=44.661, H2=19.551, H3=7.200
2025-08-05 10:11:12,250 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 727 sites
2025-08-05 10:11:13,515 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-5.642, H2=-4.626, H3=-8.503
2025-08-05 10:11:13,546 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2872 sites
2025-08-05 10:11:18,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=1.665, H2=0.042, H3=3.085
2025-08-05 10:11:18,619 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2247 sites
2025-08-05 10:11:22,536 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=25.876, H2=5.638, H3=5.452
2025-08-05 10:11:22,563 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1157 sites
2025-08-05 10:11:24,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=24.181, H2=1.767, H3=-2.977
2025-08-05 10:11:24,620 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 637 sites
2025-08-05 10:11:25,732 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=3.198, H2=-9.832, H3=2.005
2025-08-05 10:11:25,757 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-08-05 10:11:25,757 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-08-05 10:11:25,759 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-08-05 10:11:25,798 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:11:26,317 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:11:26,345 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:11:28,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:11:28,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:11:32,089 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:11:32,120 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2887 sites
2025-08-05 10:11:37,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=20.914, H2=-0.355, H3=2.502
2025-08-05 10:11:37,172 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1711 sites
2025-08-05 10:11:40,171 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.830, H2=-2.343, H3=0.815
2025-08-05 10:11:40,198 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 820 sites
2025-08-05 10:11:41,638 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-1.136, H2=-2.487, H3=-0.057
2025-08-05 10:11:41,669 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3134 sites
2025-08-05 10:11:47,188 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.928, H2=0.192, H3=-6.404
2025-08-05 10:11:47,219 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2941 sites
2025-08-05 10:11:52,392 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.964, H2=0.382, H3=-0.391
2025-08-05 10:11:52,422 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2281 sites
2025-08-05 10:11:56,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.910, H2=-4.908, H3=-1.327
2025-08-05 10:11:56,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 877 sites
2025-08-05 10:11:58,018 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-1.485, H2=2.050, H3=-2.354
2025-08-05 10:11:58,047 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1658 sites
2025-08-05 10:12:00,981 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.640, H2=1.254, H3=1.374
2025-08-05 10:12:01,009 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1489 sites
2025-08-05 10:12:03,616 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-3.829, H2=-2.953, H3=-1.654
2025-08-05 10:12:03,645 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1796 sites
2025-08-05 10:12:06,805 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.662, H2=4.199, H3=3.836
2025-08-05 10:12:06,833 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1687 sites
2025-08-05 10:12:09,794 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.007, H2=-5.961, H3=-10.194
2025-08-05 10:12:09,830 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 6232 sites
2025-08-05 10:12:20,779 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=22.609, H2=11.300, H3=2.189
2025-08-05 10:12:20,813 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4970 sites
2025-08-05 10:12:29,549 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=12.067, H2=4.111, H3=0.676
2025-08-05 10:12:29,578 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1305 sites
2025-08-05 10:12:31,844 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.094, H2=-0.722, H3=0.333
2025-08-05 10:12:31,874 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2926 sites
2025-08-05 10:12:36,982 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=5.520, H2=2.673, H3=3.896
2025-08-05 10:12:37,015 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 4415 sites
2025-08-05 10:12:44,795 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=44.661, H2=19.551, H3=7.200
2025-08-05 10:12:44,822 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 727 sites
2025-08-05 10:12:46,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-5.642, H2=-4.626, H3=-8.503
2025-08-05 10:12:46,127 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2872 sites
2025-08-05 10:12:51,174 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=1.665, H2=0.042, H3=3.085
2025-08-05 10:12:51,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2247 sites
2025-08-05 10:12:55,147 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=25.876, H2=5.638, H3=5.452
2025-08-05 10:12:55,174 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1157 sites
2025-08-05 10:12:57,198 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=24.181, H2=1.767, H3=-2.977
2025-08-05 10:12:57,224 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 135 sites
2025-08-05 10:12:57,473 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=1.365, H2=-4.913, H3=1.525
2025-08-05 10:12:57,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 502 sites
2025-08-05 10:12:58,394 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=1.628, H2=-7.593, H3=1.685
2025-08-05 10:12:58,419 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-08-05 10:12:58,419 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-08-05 10:12:58,421 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-08-05 10:12:58,446 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:12:58,968 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:12:58,996 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:13:01,289 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:13:01,318 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:13:04,792 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:13:04,823 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2887 sites
2025-08-05 10:13:09,908 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=20.914, H2=-0.355, H3=2.502
2025-08-05 10:13:09,937 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1711 sites
2025-08-05 10:13:12,938 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.830, H2=-2.343, H3=0.815
2025-08-05 10:13:12,965 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 820 sites
2025-08-05 10:13:14,405 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-1.136, H2=-2.487, H3=-0.057
2025-08-05 10:13:14,436 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3134 sites
2025-08-05 10:13:19,928 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.928, H2=0.192, H3=-6.404
2025-08-05 10:13:19,959 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2941 sites
2025-08-05 10:13:25,092 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.964, H2=0.382, H3=-0.391
2025-08-05 10:13:25,119 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 797 sites
2025-08-05 10:13:26,522 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-1.827, H2=-6.072, H3=-2.116
2025-08-05 10:13:26,550 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1484 sites
2025-08-05 10:13:29,151 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=24.632, H2=-0.011, H3=-0.741
2025-08-05 10:13:29,178 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 877 sites
2025-08-05 10:13:30,722 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-1.308, H2=1.942, H3=-1.835
2025-08-05 10:13:30,751 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1658 sites
2025-08-05 10:13:33,654 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=9.699, H2=1.455, H3=0.811
2025-08-05 10:13:33,698 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1489 sites
2025-08-05 10:13:36,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-3.754, H2=-2.891, H3=-2.611
2025-08-05 10:13:36,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1796 sites
2025-08-05 10:13:39,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.517, H2=4.128, H3=5.835
2025-08-05 10:13:39,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1687 sites
2025-08-05 10:13:42,451 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=6.233, H2=-5.912, H3=-9.425
2025-08-05 10:13:42,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 6232 sites
2025-08-05 10:13:53,327 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=21.304, H2=16.361, H3=1.947
2025-08-05 10:13:53,362 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4970 sites
2025-08-05 10:14:02,065 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=15.015, H2=3.207, H3=1.145
2025-08-05 10:14:02,093 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1305 sites
2025-08-05 10:14:04,366 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-0.380, H2=-0.892, H3=0.608
2025-08-05 10:14:04,397 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2926 sites
2025-08-05 10:14:09,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=5.113, H2=2.563, H3=2.367
2025-08-05 10:14:09,522 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 4415 sites
2025-08-05 10:14:17,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=49.708, H2=20.253, H3=8.017
2025-08-05 10:14:17,284 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 727 sites
2025-08-05 10:14:18,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-6.676, H2=-4.923, H3=-7.374
2025-08-05 10:14:18,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2872 sites
2025-08-05 10:14:23,613 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.943, H2=0.406, H3=3.572
2025-08-05 10:14:23,643 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2247 sites
2025-08-05 10:14:27,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=22.203, H2=5.461, H3=4.359
2025-08-05 10:14:27,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1157 sites
2025-08-05 10:14:29,638 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=25.287, H2=1.560, H3=-5.830
2025-08-05 10:14:29,663 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 135 sites
2025-08-05 10:14:29,901 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=1.663, H2=-4.104, H3=1.433
2025-08-05 10:14:29,928 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 502 sites
2025-08-05 10:14:30,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=1.163, H2=-7.257, H3=1.295
2025-08-05 10:14:30,838 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-08-05 10:14:30,838 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-08-05 10:14:30,840 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-08-05 10:14:30,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:14:31,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:14:31,435 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:14:33,746 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:14:33,775 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:14:37,266 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:14:37,296 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2887 sites
2025-08-05 10:14:42,332 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=20.914, H2=-0.355, H3=2.502
2025-08-05 10:14:42,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1711 sites
2025-08-05 10:14:45,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.830, H2=-2.343, H3=0.815
2025-08-05 10:14:45,401 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 820 sites
2025-08-05 10:14:46,844 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-1.136, H2=-2.487, H3=-0.057
2025-08-05 10:14:46,875 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3134 sites
2025-08-05 10:14:52,345 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.928, H2=0.192, H3=-6.404
2025-08-05 10:14:52,376 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2941 sites
2025-08-05 10:14:57,558 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.964, H2=0.382, H3=-0.391
2025-08-05 10:14:57,586 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 797 sites
2025-08-05 10:14:58,975 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-1.827, H2=-6.072, H3=-2.116
2025-08-05 10:14:59,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1484 sites
2025-08-05 10:15:01,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=24.632, H2=-0.011, H3=-0.741
2025-08-05 10:15:01,621 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 877 sites
2025-08-05 10:15:03,156 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-1.308, H2=1.942, H3=-1.835
2025-08-05 10:15:03,185 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1658 sites
2025-08-05 10:15:06,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=9.699, H2=1.455, H3=0.811
2025-08-05 10:15:06,125 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1489 sites
2025-08-05 10:15:08,770 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-3.754, H2=-2.891, H3=-2.611
2025-08-05 10:15:08,798 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1796 sites
2025-08-05 10:15:11,988 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.517, H2=4.128, H3=5.835
2025-08-05 10:15:12,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1687 sites
2025-08-05 10:15:15,004 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=6.233, H2=-5.912, H3=-9.425
2025-08-05 10:15:15,041 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 6232 sites
2025-08-05 10:15:26,117 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=21.304, H2=16.361, H3=1.947
2025-08-05 10:15:26,145 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1641 sites
2025-08-05 10:15:29,032 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=4.431, H2=-0.302, H3=-1.427
2025-08-05 10:15:29,063 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3329 sites
2025-08-05 10:15:34,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.345, H2=4.681, H3=2.759
2025-08-05 10:15:34,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1305 sites
2025-08-05 10:15:37,272 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-0.423, H2=-0.603, H3=0.150
2025-08-05 10:15:37,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2926 sites
2025-08-05 10:15:42,424 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.913, H2=3.136, H3=4.479
2025-08-05 10:15:42,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 4415 sites
2025-08-05 10:15:50,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=59.361, H2=20.596, H3=8.190
2025-08-05 10:15:50,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 727 sites
2025-08-05 10:15:51,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-5.774, H2=-4.321, H3=-7.440
2025-08-05 10:15:51,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2872 sites
2025-08-05 10:15:56,541 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=1.737, H2=0.511, H3=3.142
2025-08-05 10:15:56,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2247 sites
2025-08-05 10:16:00,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=24.326, H2=5.066, H3=5.194
2025-08-05 10:16:00,541 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1157 sites
2025-08-05 10:16:02,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=32.685, H2=0.845, H3=-4.972
2025-08-05 10:16:02,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 135 sites
2025-08-05 10:16:02,816 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=0.817, H2=-3.058, H3=0.886
2025-08-05 10:16:02,842 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 502 sites
2025-08-05 10:16:03,717 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=1.646, H2=-5.280, H3=1.073
2025-08-05 10:16:03,742 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-08-05 10:16:03,742 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-08-05 10:16:03,920 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-08-05 10:16:03,946 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:16:04,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:16:04,495 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:16:06,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:16:06,822 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:16:10,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:16:10,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2887 sites
2025-08-05 10:16:15,435 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=20.914, H2=-0.355, H3=2.502
2025-08-05 10:16:15,464 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1711 sites
2025-08-05 10:16:18,476 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.830, H2=-2.343, H3=0.815
2025-08-05 10:16:18,502 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 820 sites
2025-08-05 10:16:19,962 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-1.136, H2=-2.487, H3=-0.057
2025-08-05 10:16:19,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3134 sites
2025-08-05 10:16:25,460 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.928, H2=0.192, H3=-6.404
2025-08-05 10:16:25,491 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2941 sites
2025-08-05 10:16:30,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.964, H2=0.382, H3=-0.391
2025-08-05 10:16:30,663 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 797 sites
2025-08-05 10:16:32,049 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-1.827, H2=-6.072, H3=-2.116
2025-08-05 10:16:32,076 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1484 sites
2025-08-05 10:16:34,653 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=24.632, H2=-0.011, H3=-0.741
2025-08-05 10:16:34,680 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 877 sites
2025-08-05 10:16:36,212 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-1.308, H2=1.942, H3=-1.835
2025-08-05 10:16:36,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1658 sites
2025-08-05 10:16:39,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=9.699, H2=1.455, H3=0.811
2025-08-05 10:16:39,171 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1489 sites
2025-08-05 10:16:41,773 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-3.754, H2=-2.891, H3=-2.611
2025-08-05 10:16:41,802 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1796 sites
2025-08-05 10:16:44,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.517, H2=4.128, H3=5.835
2025-08-05 10:16:44,991 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1687 sites
2025-08-05 10:16:47,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=6.233, H2=-5.912, H3=-9.425
2025-08-05 10:16:47,982 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 6232 sites
2025-08-05 10:16:58,927 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=21.304, H2=16.361, H3=1.947
2025-08-05 10:16:58,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1641 sites
2025-08-05 10:17:01,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=4.431, H2=-0.302, H3=-1.427
2025-08-05 10:17:01,853 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3329 sites
2025-08-05 10:17:07,709 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.345, H2=4.681, H3=2.759
2025-08-05 10:17:07,737 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1305 sites
2025-08-05 10:17:10,011 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-0.423, H2=-0.603, H3=0.150
2025-08-05 10:17:10,041 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2926 sites
2025-08-05 10:17:15,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.913, H2=3.136, H3=4.479
2025-08-05 10:17:15,177 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1772 sites
2025-08-05 10:17:18,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=29.159, H2=8.317, H3=-2.993
2025-08-05 10:17:18,308 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2643 sites
2025-08-05 10:17:22,921 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=3.426, H2=3.875, H3=8.428
2025-08-05 10:17:22,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 727 sites
2025-08-05 10:17:24,226 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-6.225, H2=-4.018, H3=-5.050
2025-08-05 10:17:24,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2872 sites
2025-08-05 10:17:29,291 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=2.089, H2=0.819, H3=4.716
2025-08-05 10:17:29,321 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2247 sites
2025-08-05 10:17:33,232 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=29.792, H2=3.940, H3=4.676
2025-08-05 10:17:33,260 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1157 sites
2025-08-05 10:17:35,283 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=19.929, H2=1.293, H3=-4.503
2025-08-05 10:17:35,308 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 135 sites
2025-08-05 10:17:35,545 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=1.541, H2=-2.969, H3=1.641
2025-08-05 10:17:35,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 502 sites
2025-08-05 10:17:36,449 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=1.352, H2=-7.395, H3=1.114
2025-08-05 10:17:36,475 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-08-05 10:17:36,475 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-08-05 10:17:36,476 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-08-05 10:17:36,516 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:17:37,037 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:17:37,066 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:17:39,361 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:17:39,390 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:17:42,831 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:17:42,858 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 987 sites
2025-08-05 10:17:44,588 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=1.659, H2=4.417, H3=3.247
2025-08-05 10:17:44,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1900 sites
2025-08-05 10:17:47,940 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.153, H2=-4.216, H3=-2.001
2025-08-05 10:17:47,969 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1711 sites
2025-08-05 10:17:50,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=16.373, H2=-1.379, H3=1.437
2025-08-05 10:17:51,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 820 sites
2025-08-05 10:17:52,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.474, H2=-1.711, H3=-0.049
2025-08-05 10:17:52,468 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3134 sites
2025-08-05 10:17:57,942 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=14.248, H2=0.125, H3=-5.418
2025-08-05 10:17:57,973 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2941 sites
2025-08-05 10:18:03,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.041, H2=-0.029, H3=-0.490
2025-08-05 10:18:03,114 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 797 sites
2025-08-05 10:18:04,506 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-1.955, H2=-5.125, H3=-1.921
2025-08-05 10:18:04,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1484 sites
2025-08-05 10:18:07,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=27.339, H2=0.283, H3=-0.114
2025-08-05 10:18:07,159 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 877 sites
2025-08-05 10:18:08,688 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-2.507, H2=2.144, H3=-2.578
2025-08-05 10:18:08,716 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1658 sites
2025-08-05 10:18:11,626 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=5.488, H2=0.890, H3=0.279
2025-08-05 10:18:11,654 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1489 sites
2025-08-05 10:18:14,278 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.930, H2=-3.001, H3=-1.487
2025-08-05 10:18:14,306 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1796 sites
2025-08-05 10:18:17,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.372, H2=3.829, H3=4.647
2025-08-05 10:18:17,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1687 sites
2025-08-05 10:18:20,521 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=4.094, H2=-5.281, H3=-9.503
2025-08-05 10:18:20,557 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 6232 sites
2025-08-05 10:18:31,560 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=22.333, H2=9.663, H3=2.671
2025-08-05 10:18:31,589 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1641 sites
2025-08-05 10:18:34,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=4.030, H2=-0.160, H3=-0.840
2025-08-05 10:18:34,524 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3329 sites
2025-08-05 10:18:40,420 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.415, H2=4.447, H3=1.964
2025-08-05 10:18:40,448 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1305 sites
2025-08-05 10:18:42,749 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-0.283, H2=-0.772, H3=0.315
2025-08-05 10:18:42,779 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2926 sites
2025-08-05 10:18:47,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=6.565, H2=3.756, H3=4.435
2025-08-05 10:18:47,975 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1772 sites
2025-08-05 10:18:51,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=22.785, H2=8.966, H3=-3.266
2025-08-05 10:18:51,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2643 sites
2025-08-05 10:18:55,803 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=2.962, H2=3.419, H3=6.525
2025-08-05 10:18:55,830 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 727 sites
2025-08-05 10:18:57,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-6.933, H2=-4.196, H3=-5.863
2025-08-05 10:18:57,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2872 sites
2025-08-05 10:19:02,151 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=2.172, H2=0.062, H3=2.648
2025-08-05 10:19:02,181 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2247 sites
2025-08-05 10:19:06,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=29.193, H2=5.349, H3=5.983
2025-08-05 10:19:06,112 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1157 sites
2025-08-05 10:19:08,120 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=25.740, H2=1.009, H3=-5.215
2025-08-05 10:19:08,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 135 sites
2025-08-05 10:19:08,383 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=1.479, H2=-3.568, H3=0.945
2025-08-05 10:19:08,409 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 502 sites
2025-08-05 10:19:09,287 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=1.806, H2=-7.847, H3=1.604
2025-08-05 10:19:09,313 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-08-05 10:19:09,313 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-08-05 10:19:09,314 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-08-05 10:19:09,340 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:19:09,861 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:19:09,889 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:19:12,180 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:19:12,209 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:19:15,658 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:19:15,686 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 987 sites
2025-08-05 10:19:17,406 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=1.659, H2=4.417, H3=3.247
2025-08-05 10:19:17,434 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1900 sites
2025-08-05 10:19:20,743 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.153, H2=-4.216, H3=-2.001
2025-08-05 10:19:20,772 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1711 sites
2025-08-05 10:19:23,746 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=16.373, H2=-1.379, H3=1.437
2025-08-05 10:19:23,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 820 sites
2025-08-05 10:19:25,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.474, H2=-1.711, H3=-0.049
2025-08-05 10:19:25,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3134 sites
2025-08-05 10:19:30,733 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=14.248, H2=0.125, H3=-5.418
2025-08-05 10:19:30,764 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2941 sites
2025-08-05 10:19:35,893 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.041, H2=-0.029, H3=-0.490
2025-08-05 10:19:35,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 797 sites
2025-08-05 10:19:37,308 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-1.955, H2=-5.125, H3=-1.921
2025-08-05 10:19:37,336 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1484 sites
2025-08-05 10:19:39,916 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=27.339, H2=0.283, H3=-0.114
2025-08-05 10:19:39,943 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 877 sites
2025-08-05 10:19:41,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-2.507, H2=2.144, H3=-2.578
2025-08-05 10:19:41,544 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1658 sites
2025-08-05 10:19:44,442 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=5.488, H2=0.890, H3=0.279
2025-08-05 10:19:44,470 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1489 sites
2025-08-05 10:19:47,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.930, H2=-3.001, H3=-1.487
2025-08-05 10:19:47,087 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1796 sites
2025-08-05 10:19:50,211 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.372, H2=3.829, H3=4.647
2025-08-05 10:19:50,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1687 sites
2025-08-05 10:19:53,189 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=4.094, H2=-5.281, H3=-9.503
2025-08-05 10:19:53,226 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 6232 sites
2025-08-05 10:20:04,044 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=22.333, H2=9.663, H3=2.671
2025-08-05 10:20:04,073 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1641 sites
2025-08-05 10:20:06,937 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=4.030, H2=-0.160, H3=-0.840
2025-08-05 10:20:06,968 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3329 sites
2025-08-05 10:20:12,750 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.415, H2=4.447, H3=1.964
2025-08-05 10:20:12,778 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1305 sites
2025-08-05 10:20:15,064 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-0.283, H2=-0.772, H3=0.315
2025-08-05 10:20:15,095 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2926 sites
2025-08-05 10:20:20,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=6.565, H2=3.756, H3=4.435
2025-08-05 10:20:20,236 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1772 sites
2025-08-05 10:20:23,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=22.785, H2=8.966, H3=-3.266
2025-08-05 10:20:23,355 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2643 sites
2025-08-05 10:20:27,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=2.962, H2=3.419, H3=6.525
2025-08-05 10:20:28,018 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 727 sites
2025-08-05 10:20:29,283 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-6.933, H2=-4.196, H3=-5.863
2025-08-05 10:20:29,323 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2872 sites
2025-08-05 10:20:34,324 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=2.172, H2=0.062, H3=2.648
2025-08-05 10:20:34,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 772 sites
2025-08-05 10:20:35,694 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=2.343, H2=-0.247, H3=-2.457
2025-08-05 10:20:35,722 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1475 sites
2025-08-05 10:20:38,306 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-3.684, H2=-0.774, H3=3.105
2025-08-05 10:20:38,333 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1157 sites
2025-08-05 10:20:40,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=28.260, H2=1.352, H3=-3.125
2025-08-05 10:20:40,383 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 135 sites
2025-08-05 10:20:40,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=1.511, H2=-3.723, H3=1.224
2025-08-05 10:20:40,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 502 sites
2025-08-05 10:20:41,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=1.313, H2=-5.358, H3=1.051
2025-08-05 10:20:41,553 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=30 has insufficient data
2025-08-05 10:20:41,553 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=31
2025-08-05 10:20:41,555 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31 -9223372036854775808]
2025-08-05 10:20:41,580 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:20:42,101 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:20:42,144 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:20:44,420 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:20:44,450 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:20:47,882 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:20:47,910 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 987 sites
2025-08-05 10:20:49,622 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=1.659, H2=4.417, H3=3.247
2025-08-05 10:20:49,651 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1900 sites
2025-08-05 10:20:52,939 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.153, H2=-4.216, H3=-2.001
2025-08-05 10:20:52,968 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1711 sites
2025-08-05 10:20:55,937 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=16.373, H2=-1.379, H3=1.437
2025-08-05 10:20:55,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 820 sites
2025-08-05 10:20:57,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.474, H2=-1.711, H3=-0.049
2025-08-05 10:20:57,425 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3134 sites
2025-08-05 10:21:02,853 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=14.248, H2=0.125, H3=-5.418
2025-08-05 10:21:02,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2941 sites
2025-08-05 10:21:07,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.041, H2=-0.029, H3=-0.490
2025-08-05 10:21:08,011 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 797 sites
2025-08-05 10:21:09,388 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-1.955, H2=-5.125, H3=-1.921
2025-08-05 10:21:09,416 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1484 sites
2025-08-05 10:21:12,001 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=27.339, H2=0.283, H3=-0.114
2025-08-05 10:21:12,029 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 877 sites
2025-08-05 10:21:13,547 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-2.507, H2=2.144, H3=-2.578
2025-08-05 10:21:13,576 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1658 sites
2025-08-05 10:21:16,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=5.488, H2=0.890, H3=0.279
2025-08-05 10:21:16,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1489 sites
2025-08-05 10:21:19,083 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.930, H2=-3.001, H3=-1.487
2025-08-05 10:21:19,112 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1796 sites
2025-08-05 10:21:22,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.372, H2=3.829, H3=4.647
2025-08-05 10:21:22,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1687 sites
2025-08-05 10:21:25,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=4.094, H2=-5.281, H3=-9.503
2025-08-05 10:21:25,294 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 6232 sites
2025-08-05 10:21:36,302 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=22.333, H2=9.663, H3=2.671
2025-08-05 10:21:36,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1641 sites
2025-08-05 10:21:39,187 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=4.030, H2=-0.160, H3=-0.840
2025-08-05 10:21:39,219 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3329 sites
2025-08-05 10:21:45,073 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.415, H2=4.447, H3=1.964
2025-08-05 10:21:45,101 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1305 sites
2025-08-05 10:21:47,384 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-0.283, H2=-0.772, H3=0.315
2025-08-05 10:21:47,415 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2926 sites
2025-08-05 10:21:52,574 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=6.565, H2=3.756, H3=4.435
2025-08-05 10:21:52,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1772 sites
2025-08-05 10:21:55,698 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=22.785, H2=8.966, H3=-3.266
2025-08-05 10:21:55,728 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2643 sites
2025-08-05 10:22:00,357 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=2.962, H2=3.419, H3=6.525
2025-08-05 10:22:00,383 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 727 sites
2025-08-05 10:22:01,649 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-6.933, H2=-4.196, H3=-5.863
2025-08-05 10:22:01,691 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2872 sites
2025-08-05 10:22:06,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=2.172, H2=0.062, H3=2.648
2025-08-05 10:22:06,718 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 772 sites
2025-08-05 10:22:08,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=2.343, H2=-0.247, H3=-2.457
2025-08-05 10:22:08,110 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1475 sites
2025-08-05 10:22:10,672 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-3.684, H2=-0.774, H3=3.105
2025-08-05 10:22:10,698 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 486 sites
2025-08-05 10:22:11,539 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=12.308, H2=-0.427, H3=-1.708
2025-08-05 10:22:11,566 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 671 sites
2025-08-05 10:22:12,734 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=9.387, H2=-1.611, H3=-7.351
2025-08-05 10:22:12,759 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 135 sites
2025-08-05 10:22:12,994 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=1.378, H2=-2.415, H3=0.650
2025-08-05 10:22:13,020 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 502 sites
2025-08-05 10:22:13,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=2.320, H2=-8.794, H3=2.122
2025-08-05 10:22:13,920 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=31 has insufficient data
2025-08-05 10:22:13,920 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=32
2025-08-05 10:22:13,922 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32 -9223372036854775808]
2025-08-05 10:22:13,947 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 298 sites
2025-08-05 10:22:14,465 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-3.572, H2=0.406, H3=-1.405
2025-08-05 10:22:14,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1312 sites
2025-08-05 10:22:16,785 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.367, H2=0.255, H3=0.263
2025-08-05 10:22:16,814 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1980 sites
2025-08-05 10:22:20,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=41.837, H2=-0.442, H3=-1.430
2025-08-05 10:22:20,302 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 987 sites
2025-08-05 10:22:22,024 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=1.659, H2=4.417, H3=3.247
2025-08-05 10:22:22,053 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1900 sites
2025-08-05 10:22:25,369 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.153, H2=-4.216, H3=-2.001
2025-08-05 10:22:25,397 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1711 sites
2025-08-05 10:22:28,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=16.373, H2=-1.379, H3=1.437
2025-08-05 10:22:28,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 820 sites
2025-08-05 10:22:29,840 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.474, H2=-1.711, H3=-0.049
2025-08-05 10:22:29,871 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3134 sites
