2025-08-03 13:37:46,162 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-08-03 13:37:46,163 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-03 13:37:46,163 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_12_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-03 13:37:46,163 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_12_hierarchical_singlevariable
2025-08-03 13:37:46,163 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-08-03 13:37:46,163 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-08-03 13:37:46,163 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-08-03 13:37:46,165 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-08-03 13:37:46,166 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_12_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-03 13:37:46,166 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-08-03 13:37:47,687 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-08-03 13:37:47,951 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-08-03 13:37:47,956 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-08-03 13:37:47,959 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-08-03 13:37:47,962 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-08-03 13:37:47,963 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-08-03 13:37:47,964 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-08-03 13:37:47,966 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-08-03 13:37:47,988 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-08-03 13:37:48,039 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-08-03 13:37:48,042 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-08-03 13:37:48,044 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-08-03 13:37:48,046 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-08-03 13:37:48,047 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-08-03 13:37:48,049 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-08-03 13:37:48,051 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-08-03 13:37:48,052 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-08-03 13:37:48,053 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-08-03 13:37:48,055 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-08-03 13:37:48,059 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-08-03 13:37:48,059 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-08-03 13:37:48,061 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-08-03 13:37:48,062 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-08-03 13:37:48,062 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-08-03 13:37:48,064 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-08-03 13:37:48,066 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-08-03 13:37:48,067 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-08-03 13:37:48,069 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-08-03 13:37:48,071 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-08-03 13:37:48,073 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-08-03 13:37:48,076 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-08-03 13:37:48,078 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-08-03 13:37:48,080 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-08-03 13:37:48,082 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-08-03 13:37:48,084 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-08-03 13:37:48,085 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-08-03 13:37:48,087 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-08-03 13:37:48,090 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-08-03 13:37:48,092 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-08-03 13:37:48,102 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-08-03 13:37:48,102 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-08-03 13:37:48,102 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-08-03 13:37:48,103 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-08-03 13:37:48,104 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-08-03 13:37:48,108 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-08-03 13:37:48,108 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-08-03 13:37:48,110 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-08-03 13:37:48,110 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-08-03 13:37:48,112 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-08-03 13:37:48,112 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-08-03 13:37:48,114 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-08-03 13:37:48,114 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-08-03 13:37:48,116 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-08-03 13:37:48,117 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-08-03 13:37:48,119 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-08-03 13:37:48,119 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-08-03 13:37:48,121 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-08-03 13:37:48,122 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-08-03 13:37:48,125 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-08-03 13:37:48,125 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-08-03 13:37:48,128 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-08-03 13:37:48,128 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-08-03 13:37:48,130 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-08-03 13:37:48,130 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-08-03 13:37:48,133 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-08-03 13:37:48,133 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-08-03 13:37:48,136 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-08-03 13:37:48,137 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-08-03 13:37:48,139 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-08-03 13:37:48,140 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-08-03 13:37:48,143 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-08-03 13:37:48,143 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-08-03 13:37:48,145 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-08-03 13:37:48,145 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-08-03 13:37:48,148 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-08-03 13:37:48,148 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-08-03 13:37:48,151 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-08-03 13:37:48,151 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-08-03 13:37:48,154 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-08-03 13:37:48,154 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-08-03 13:37:48,157 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-08-03 13:37:48,157 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-08-03 13:37:48,160 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-08-03 13:37:48,160 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-08-03 13:37:48,163 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-08-03 13:37:48,163 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-08-03 13:37:48,166 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-08-03 13:37:48,166 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-08-03 13:37:48,169 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-08-03 13:37:48,169 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-08-03 13:37:48,172 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-08-03 13:37:48,172 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-08-03 13:37:48,175 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-08-03 13:37:48,175 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-08-03 13:37:48,177 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-08-03 13:37:48,177 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-08-03 13:37:48,180 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-08-03 13:37:48,180 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-08-03 13:37:48,183 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-08-03 13:37:48,183 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-08-03 13:37:48,186 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-08-03 13:37:48,186 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-08-03 13:37:48,189 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-08-03 13:37:48,189 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-08-03 13:37:48,192 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-08-03 13:37:48,192 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-08-03 13:37:48,195 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-08-03 13:37:48,195 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-08-03 13:37:48,198 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-08-03 13:37:48,198 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-08-03 13:37:48,200 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-08-03 13:37:48,201 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-08-03 13:37:48,203 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-08-03 13:37:48,204 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-08-03 13:37:48,206 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-08-03 13:37:48,207 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-08-03 13:37:48,209 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-08-03 13:37:48,210 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-08-03 13:37:48,213 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-08-03 13:37:48,213 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-08-03 13:37:48,215 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-08-03 13:37:48,215 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-08-03 13:37:48,218 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-08-03 13:37:48,218 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-08-03 13:37:48,221 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-08-03 13:37:48,221 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-08-03 13:37:48,224 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-08-03 13:37:48,224 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-08-03 13:37:48,227 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-08-03 13:37:48,227 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-08-03 13:37:48,230 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-08-03 13:37:48,230 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-08-03 13:37:48,233 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-08-03 13:37:48,233 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-08-03 13:37:48,236 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-08-03 13:37:48,236 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-08-03 13:37:48,238 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-08-03 13:37:48,238 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-08-03 13:37:48,241 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-08-03 13:37:48,241 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-08-03 13:37:48,244 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-08-03 13:37:48,244 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-08-03 13:37:48,247 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-08-03 13:37:48,247 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-08-03 13:37:48,250 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-08-03 13:37:48,250 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-08-03 13:37:48,253 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-08-03 13:37:48,253 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-08-03 13:37:48,256 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-08-03 13:37:48,256 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-08-03 13:37:48,259 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-08-03 13:37:48,259 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-08-03 13:37:48,261 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-08-03 13:37:48,261 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-08-03 13:37:48,264 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-08-03 13:37:48,264 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-08-03 13:37:48,267 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-08-03 13:37:48,267 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-08-03 13:37:48,270 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-08-03 13:37:48,270 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-08-03 13:37:48,273 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-08-03 13:37:48,273 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-08-03 13:37:48,275 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-08-03 13:37:48,276 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-08-03 13:37:48,278 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-08-03 13:37:48,278 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-08-03 13:37:48,281 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-08-03 13:37:48,281 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-08-03 13:37:48,284 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-08-03 13:37:48,284 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-08-03 13:37:48,287 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-08-03 13:37:48,287 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-08-03 13:37:48,290 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-08-03 13:37:48,290 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-08-03 13:37:48,293 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-08-03 13:37:48,293 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-08-03 13:37:48,296 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-08-03 13:37:48,296 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-08-03 13:37:48,298 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-08-03 13:37:48,298 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-08-03 13:37:48,301 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-08-03 13:37:48,301 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-08-03 13:37:48,304 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-08-03 13:37:48,304 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-08-03 13:37:48,306 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-08-03 13:37:48,306 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-08-03 13:37:48,309 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-08-03 13:37:48,309 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-08-03 13:37:48,312 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-08-03 13:37:48,312 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-08-03 13:37:48,315 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-08-03 13:37:48,315 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-08-03 13:37:48,318 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-08-03 13:37:48,318 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-08-03 13:37:48,321 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-08-03 13:37:48,321 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-08-03 13:37:48,323 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-08-03 13:37:48,324 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-08-03 13:37:48,326 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-08-03 13:37:48,327 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-08-03 13:37:48,329 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-08-03 13:37:48,329 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-08-03 13:37:48,343 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-08-03 13:37:48,344 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-08-03 13:37:48,622 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-08-03 13:37:48,622 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-08-03 13:37:48,625 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-08-03 13:37:48,625 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-08-03 13:37:48,627 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-08-03 13:37:48,628 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-08-03 13:37:48,630 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-08-03 13:37:48,630 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-08-03 13:37:48,633 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-08-03 13:37:48,633 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-08-03 13:37:48,636 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-08-03 13:37:48,636 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-08-03 13:37:48,638 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-08-03 13:37:48,638 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-08-03 13:37:48,641 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-08-03 13:37:48,641 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-08-03 13:37:48,644 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-08-03 13:37:48,644 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-08-03 13:37:48,647 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-08-03 13:37:48,647 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-08-03 13:37:48,650 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-08-03 13:37:48,650 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-08-03 13:37:48,652 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-08-03 13:37:48,652 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-08-03 13:37:48,655 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-08-03 13:37:48,655 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-08-03 13:37:48,658 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-08-03 13:37:48,658 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-08-03 13:37:48,660 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-08-03 13:37:48,660 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-08-03 13:37:48,663 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-08-03 13:37:48,663 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-08-03 13:37:48,666 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-08-03 13:37:48,666 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-08-03 13:37:48,669 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-08-03 13:37:48,669 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-08-03 13:37:48,672 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-08-03 13:37:48,672 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-08-03 13:37:48,674 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-08-03 13:37:48,674 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-08-03 13:37:48,677 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-08-03 13:37:48,677 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-08-03 13:37:48,680 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-08-03 13:37:48,680 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-08-03 13:37:48,682 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-08-03 13:37:48,683 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-08-03 13:37:48,685 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-08-03 13:37:48,686 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-08-03 13:37:48,688 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-08-03 13:37:48,688 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-08-03 13:37:48,691 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-08-03 13:37:48,691 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-08-03 13:37:48,694 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-08-03 13:37:48,694 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-08-03 13:37:48,696 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-08-03 13:37:48,697 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-08-03 13:37:48,699 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-08-03 13:37:48,699 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-08-03 13:37:48,702 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-08-03 13:37:48,702 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-08-03 13:37:48,705 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-08-03 13:37:48,705 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-08-03 13:37:48,707 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-08-03 13:37:48,708 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-08-03 13:37:48,710 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-08-03 13:37:48,710 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-08-03 13:37:48,713 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-08-03 13:37:48,713 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-08-03 13:37:48,716 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-08-03 13:37:48,716 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-08-03 13:37:48,718 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-08-03 13:37:48,719 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-08-03 13:37:48,721 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-08-03 13:37:48,721 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-08-03 13:37:48,724 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-08-03 13:37:48,724 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-08-03 13:37:48,726 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-08-03 13:37:48,727 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-08-03 13:37:48,729 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-08-03 13:37:48,729 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-08-03 13:37:48,732 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-08-03 13:37:48,732 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-08-03 13:37:48,735 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-08-03 13:37:48,735 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-08-03 13:37:48,738 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-08-03 13:37:48,738 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-08-03 13:37:48,740 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-08-03 13:37:48,740 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-08-03 13:37:48,743 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-08-03 13:37:48,743 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_12_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-03 13:37:50,430 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-08-03 13:37:50,430 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-08-03 13:37:50,430 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-08-03 13:37:50,430 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_12_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-03 13:37:50,430 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_12_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-03 13:37:50,430 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 125 variables
2025-08-03 13:37:50,430 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125
2025-08-03 13:37:50,431 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-08-03 13:37:50,433 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_12_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-03 13:37:50,433 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-08-03 13:37:50,433 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-03 13:37:50,469 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-08-03 13:38:00,335 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_12_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-03 13:38:00,370 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-08-03 13:38:00,370 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-08-03 13:38:00,370 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-08-03 13:38:00,372 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-08-03 13:38:00,423 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 14778 sites
2025-08-03 13:38:26,772 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=118.016, H2=-0.608, H3=-0.943
2025-08-03 13:38:26,867 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 37581 sites
2025-08-03 13:39:33,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=125.246, H2=27.627, H3=6.898
2025-08-03 13:39:33,493 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-08-03 13:39:33,493 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-08-03 13:39:33,495 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-08-03 13:39:33,548 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 14778 sites
2025-08-03 13:39:59,865 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=118.016, H2=-0.608, H3=-0.943
2025-08-03 13:39:59,928 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 19869 sites
2025-08-03 13:40:35,342 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=39.335, H2=12.204, H3=3.958
2025-08-03 13:40:35,401 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 17712 sites
2025-08-03 13:41:06,917 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=165.858, H2=33.136, H3=6.728
2025-08-03 13:41:06,943 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-08-03 13:41:06,944 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-08-03 13:41:06,945 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-08-03 13:41:06,997 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 14778 sites
2025-08-03 13:41:33,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=118.016, H2=-0.608, H3=-0.943
2025-08-03 13:41:33,449 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 19869 sites
2025-08-03 13:42:08,966 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=39.335, H2=12.204, H3=3.958
2025-08-03 13:42:08,999 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3609 sites
2025-08-03 13:42:15,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=46.664, H2=3.248, H3=-1.757
2025-08-03 13:42:15,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 14103 sites
2025-08-03 13:42:40,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=56.740, H2=18.965, H3=5.530
2025-08-03 13:42:40,594 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-08-03 13:42:40,594 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-08-03 13:42:40,595 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-08-03 13:42:40,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 14778 sites
2025-08-03 13:43:06,760 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=118.016, H2=-0.608, H3=-0.943
2025-08-03 13:43:06,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 7969 sites
2025-08-03 13:43:21,076 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.868, H2=11.782, H3=-1.846
2025-08-03 13:43:21,123 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 11900 sites
2025-08-03 13:43:42,548 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=18.738, H2=12.779, H3=11.276
2025-08-03 13:43:42,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3609 sites
2025-08-03 13:43:49,129 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=49.083, H2=2.681, H3=-2.235
2025-08-03 13:43:49,180 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 14103 sites
2025-08-03 13:44:14,657 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=70.544, H2=13.244, H3=4.764
2025-08-03 13:44:14,682 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-08-03 13:44:14,682 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-08-03 13:44:14,684 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-08-03 13:44:14,736 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 14778 sites
2025-08-03 13:44:41,562 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=118.016, H2=-0.608, H3=-0.943
2025-08-03 13:44:41,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 7969 sites
2025-08-03 13:44:56,103 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.868, H2=11.782, H3=-1.846
2025-08-03 13:44:56,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 11900 sites
2025-08-03 13:45:17,641 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=18.738, H2=12.779, H3=11.276
2025-08-03 13:45:17,673 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3609 sites
2025-08-03 13:45:24,194 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=49.083, H2=2.681, H3=-2.235
2025-08-03 13:45:24,224 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2642 sites
2025-08-03 13:45:28,977 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=8.991, H2=5.478, H3=-0.590
2025-08-03 13:45:29,023 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 11461 sites
2025-08-03 13:45:49,671 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=42.715, H2=14.745, H3=7.360
2025-08-03 13:45:49,697 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-08-03 13:45:49,697 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-08-03 13:45:49,698 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-08-03 13:45:49,749 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 14778 sites
2025-08-03 13:46:16,566 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=118.016, H2=-0.608, H3=-0.943
2025-08-03 13:46:16,606 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 7969 sites
2025-08-03 13:46:31,036 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.868, H2=11.782, H3=-1.846
2025-08-03 13:46:31,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 11900 sites
2025-08-03 13:46:52,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=18.738, H2=12.779, H3=11.276
2025-08-03 13:46:52,588 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 648 sites
2025-08-03 13:46:53,752 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.449, H2=-7.896, H3=0.985
2025-08-03 13:46:53,784 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2961 sites
2025-08-03 13:46:59,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=40.975, H2=5.389, H3=-3.459
2025-08-03 13:46:59,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2642 sites
2025-08-03 13:47:03,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=9.478, H2=6.845, H3=-0.090
2025-08-03 13:47:03,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 11461 sites
2025-08-03 13:47:24,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=32.466, H2=14.812, H3=6.050
2025-08-03 13:47:24,529 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-08-03 13:47:24,530 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-08-03 13:47:24,531 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-08-03 13:47:24,582 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 14778 sites
2025-08-03 13:47:51,009 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=118.016, H2=-0.608, H3=-0.943
2025-08-03 13:47:51,050 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 7969 sites
2025-08-03 13:48:05,264 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.868, H2=11.782, H3=-1.846
2025-08-03 13:48:05,296 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3638 sites
2025-08-03 13:48:11,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=11.069, H2=6.627, H3=5.269
2025-08-03 13:48:11,878 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8262 sites
2025-08-03 13:48:26,583 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=19.300, H2=5.585, H3=5.090
2025-08-03 13:48:26,610 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 648 sites
2025-08-03 13:48:27,768 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=2.191, H2=-8.357, H3=2.427
2025-08-03 13:48:27,798 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2961 sites
2025-08-03 13:48:33,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=34.942, H2=6.452, H3=-2.840
2025-08-03 13:48:33,088 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2642 sites
2025-08-03 13:48:37,807 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=10.123, H2=7.904, H3=-0.433
2025-08-03 13:48:37,854 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 11461 sites
2025-08-03 13:48:58,363 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=49.740, H2=15.810, H3=6.369
2025-08-03 13:48:58,388 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-08-03 13:48:58,388 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-08-03 13:48:58,390 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-08-03 13:48:58,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 13:49:07,067 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 13:49:07,111 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 9965 sites
2025-08-03 13:49:25,015 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=77.010, H2=-1.532, H3=-0.588
2025-08-03 13:49:25,056 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7969 sites
2025-08-03 13:49:39,396 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=26.668, H2=10.734, H3=-2.220
2025-08-03 13:49:39,428 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3638 sites
2025-08-03 13:49:45,944 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.332, H2=4.565, H3=5.507
2025-08-03 13:49:45,984 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8262 sites
2025-08-03 13:50:00,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.838, H2=4.935, H3=3.635
2025-08-03 13:50:00,762 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 648 sites
2025-08-03 13:50:01,924 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.903, H2=-8.001, H3=2.044
2025-08-03 13:50:01,954 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2961 sites
2025-08-03 13:50:07,255 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=41.594, H2=6.624, H3=-2.986
2025-08-03 13:50:07,285 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2642 sites
2025-08-03 13:50:12,036 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.021, H2=6.171, H3=-0.927
2025-08-03 13:50:12,082 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 11461 sites
2025-08-03 13:50:32,457 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=43.581, H2=14.524, H3=9.189
2025-08-03 13:50:32,483 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-08-03 13:50:32,483 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-08-03 13:50:32,485 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-08-03 13:50:32,519 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 13:50:41,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 13:50:41,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 9965 sites
2025-08-03 13:50:58,857 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=77.010, H2=-1.532, H3=-0.588
2025-08-03 13:50:58,897 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7969 sites
2025-08-03 13:51:13,152 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=26.668, H2=10.734, H3=-2.220
2025-08-03 13:51:13,184 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3638 sites
2025-08-03 13:51:19,670 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.332, H2=4.565, H3=5.507
2025-08-03 13:51:19,711 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8262 sites
2025-08-03 13:51:34,496 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.838, H2=4.935, H3=3.635
2025-08-03 13:51:34,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 648 sites
2025-08-03 13:51:35,683 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.903, H2=-8.001, H3=2.044
2025-08-03 13:51:35,714 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2961 sites
2025-08-03 13:51:40,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=41.594, H2=6.624, H3=-2.986
2025-08-03 13:51:41,014 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2642 sites
2025-08-03 13:51:45,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.021, H2=6.171, H3=-0.927
2025-08-03 13:51:45,776 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3012 sites
2025-08-03 13:51:51,165 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.696, H2=-0.592, H3=-3.441
2025-08-03 13:51:51,206 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 8449 sites
2025-08-03 13:52:06,362 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=41.267, H2=20.420, H3=6.959
2025-08-03 13:52:06,388 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-08-03 13:52:06,388 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-08-03 13:52:06,390 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-08-03 13:52:06,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 13:52:15,021 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 13:52:15,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 9965 sites
2025-08-03 13:52:32,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=77.010, H2=-1.532, H3=-0.588
2025-08-03 13:52:32,831 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7969 sites
2025-08-03 13:52:46,983 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=26.668, H2=10.734, H3=-2.220
2025-08-03 13:52:47,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3638 sites
2025-08-03 13:52:53,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.332, H2=4.565, H3=5.507
2025-08-03 13:52:53,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8262 sites
2025-08-03 13:53:08,210 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.838, H2=4.935, H3=3.635
2025-08-03 13:53:08,236 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 648 sites
2025-08-03 13:53:09,378 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.903, H2=-8.001, H3=2.044
2025-08-03 13:53:09,406 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1585 sites
2025-08-03 13:53:12,229 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=39.472, H2=1.426, H3=-4.936
2025-08-03 13:53:12,257 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1376 sites
2025-08-03 13:53:14,697 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=15.002, H2=-0.135, H3=-0.845
2025-08-03 13:53:14,728 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2642 sites
2025-08-03 13:53:19,438 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=13.882, H2=10.066, H3=-0.751
2025-08-03 13:53:19,469 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3012 sites
2025-08-03 13:53:24,818 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.919, H2=-0.053, H3=-2.472
2025-08-03 13:53:24,859 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 8449 sites
2025-08-03 13:53:39,798 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=35.604, H2=17.363, H3=7.800
2025-08-03 13:53:39,823 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-08-03 13:53:39,823 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-08-03 13:53:39,825 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-08-03 13:53:39,858 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 13:53:48,452 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 13:53:48,496 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 9965 sites
2025-08-03 13:54:06,277 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=77.010, H2=-1.532, H3=-0.588
2025-08-03 13:54:06,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7969 sites
2025-08-03 13:54:20,610 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=26.668, H2=10.734, H3=-2.220
2025-08-03 13:54:20,642 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3638 sites
2025-08-03 13:54:27,160 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.332, H2=4.565, H3=5.507
2025-08-03 13:54:27,200 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8262 sites
2025-08-03 13:54:42,018 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.838, H2=4.935, H3=3.635
2025-08-03 13:54:42,045 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 648 sites
2025-08-03 13:54:43,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.903, H2=-8.001, H3=2.044
2025-08-03 13:54:43,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1585 sites
2025-08-03 13:54:46,069 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=39.472, H2=1.426, H3=-4.936
2025-08-03 13:54:46,098 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1376 sites
2025-08-03 13:54:48,572 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=15.002, H2=-0.135, H3=-0.845
2025-08-03 13:54:48,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2642 sites
2025-08-03 13:54:53,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=13.882, H2=10.066, H3=-0.751
2025-08-03 13:54:53,387 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3012 sites
2025-08-03 13:54:58,850 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.919, H2=-0.053, H3=-2.472
2025-08-03 13:54:58,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2465 sites
2025-08-03 13:55:03,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-2.988, H2=-3.719, H3=2.305
2025-08-03 13:55:03,372 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 5984 sites
2025-08-03 13:55:14,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=44.091, H2=19.939, H3=7.672
2025-08-03 13:55:14,203 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-08-03 13:55:14,203 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-08-03 13:55:14,204 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-08-03 13:55:14,238 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 13:55:22,939 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 13:55:22,968 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2447 sites
2025-08-03 13:55:27,400 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=27.401, H2=-1.876, H3=-1.167
2025-08-03 13:55:27,439 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7518 sites
2025-08-03 13:55:40,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.352, H2=-2.948, H3=-0.833
2025-08-03 13:55:40,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7969 sites
2025-08-03 13:55:55,240 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=24.175, H2=8.748, H3=-2.661
2025-08-03 13:55:55,272 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3638 sites
2025-08-03 13:56:01,834 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=11.175, H2=5.642, H3=4.660
2025-08-03 13:56:01,875 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8262 sites
2025-08-03 13:56:16,678 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=15.726, H2=4.553, H3=3.755
2025-08-03 13:56:16,705 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 648 sites
2025-08-03 13:56:17,859 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=2.753, H2=-5.435, H3=3.016
2025-08-03 13:56:17,887 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1585 sites
2025-08-03 13:56:20,713 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=41.511, H2=1.442, H3=-5.997
2025-08-03 13:56:20,741 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1376 sites
2025-08-03 13:56:23,190 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=13.337, H2=-0.360, H3=-0.475
2025-08-03 13:56:23,221 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2642 sites
2025-08-03 13:56:27,964 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.652, H2=6.395, H3=-0.687
2025-08-03 13:56:27,996 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3012 sites
2025-08-03 13:56:33,378 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.550, H2=-0.144, H3=-2.355
2025-08-03 13:56:33,408 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2465 sites
2025-08-03 13:56:37,786 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-3.911, H2=-2.587, H3=2.569
2025-08-03 13:56:37,822 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 5984 sites
2025-08-03 13:56:48,487 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=41.385, H2=20.218, H3=9.711
2025-08-03 13:56:48,512 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-08-03 13:56:48,512 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-08-03 13:56:48,514 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-08-03 13:56:48,549 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 13:56:57,163 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 13:56:57,193 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2447 sites
2025-08-03 13:57:01,507 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=27.401, H2=-1.876, H3=-1.167
2025-08-03 13:57:01,547 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7518 sites
2025-08-03 13:57:15,023 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.352, H2=-2.948, H3=-0.833
2025-08-03 13:57:15,052 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1824 sites
2025-08-03 13:57:18,303 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.933, H2=-5.044, H3=-8.909
2025-08-03 13:57:18,339 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6145 sites
2025-08-03 13:57:29,355 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=31.518, H2=12.237, H3=1.779
2025-08-03 13:57:29,388 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3638 sites
2025-08-03 13:57:35,872 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.019, H2=5.093, H3=5.783
2025-08-03 13:57:35,912 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8262 sites
2025-08-03 13:57:50,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=12.134, H2=5.430, H3=3.496
2025-08-03 13:57:50,632 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 648 sites
2025-08-03 13:57:51,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=2.663, H2=-10.446, H3=1.312
2025-08-03 13:57:51,806 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1585 sites
2025-08-03 13:57:54,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=50.720, H2=1.245, H3=-4.528
2025-08-03 13:57:54,658 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1376 sites
2025-08-03 13:57:57,106 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=14.231, H2=-0.346, H3=-0.805
2025-08-03 13:57:57,137 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2642 sites
2025-08-03 13:58:01,844 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=9.990, H2=6.563, H3=-0.236
2025-08-03 13:58:01,876 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3012 sites
2025-08-03 13:58:07,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.628, H2=0.039, H3=-3.705
2025-08-03 13:58:07,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2465 sites
2025-08-03 13:58:11,677 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-3.618, H2=-3.317, H3=1.573
2025-08-03 13:58:11,713 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 5984 sites
2025-08-03 13:58:22,433 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=58.344, H2=17.838, H3=8.709
2025-08-03 13:58:22,458 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-08-03 13:58:22,458 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-08-03 13:58:22,460 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-08-03 13:58:22,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 13:58:31,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 13:58:31,087 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2447 sites
2025-08-03 13:58:35,439 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=27.401, H2=-1.876, H3=-1.167
2025-08-03 13:58:35,478 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7518 sites
2025-08-03 13:58:48,869 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.352, H2=-2.948, H3=-0.833
2025-08-03 13:58:48,898 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1824 sites
2025-08-03 13:58:52,136 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.933, H2=-5.044, H3=-8.909
2025-08-03 13:58:52,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6145 sites
2025-08-03 13:59:03,092 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=31.518, H2=12.237, H3=1.779
2025-08-03 13:59:03,124 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3638 sites
2025-08-03 13:59:09,610 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.019, H2=5.093, H3=5.783
2025-08-03 13:59:09,644 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4635 sites
2025-08-03 13:59:17,852 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.588, H2=5.950, H3=3.059
2025-08-03 13:59:17,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3627 sites
2025-08-03 13:59:24,317 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=6.036, H2=1.480, H3=2.439
2025-08-03 13:59:24,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 648 sites
2025-08-03 13:59:25,487 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.723, H2=-7.471, H3=1.553
2025-08-03 13:59:25,516 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1585 sites
2025-08-03 13:59:28,315 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=40.871, H2=1.286, H3=-4.391
2025-08-03 13:59:28,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1376 sites
2025-08-03 13:59:30,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.789, H2=-0.037, H3=-0.126
2025-08-03 13:59:30,821 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2642 sites
2025-08-03 13:59:35,512 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=14.450, H2=7.117, H3=-0.526
2025-08-03 13:59:35,544 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3012 sites
2025-08-03 13:59:40,926 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.600, H2=-0.242, H3=-3.268
2025-08-03 13:59:40,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2465 sites
2025-08-03 13:59:45,365 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.165, H2=-3.161, H3=2.078
2025-08-03 13:59:45,401 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 5984 sites
2025-08-03 13:59:56,125 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=52.472, H2=19.534, H3=8.222
2025-08-03 13:59:56,150 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-08-03 13:59:56,150 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-08-03 13:59:56,151 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-08-03 13:59:56,185 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 14:00:04,811 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 14:00:04,841 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2447 sites
2025-08-03 14:00:09,211 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=27.401, H2=-1.876, H3=-1.167
2025-08-03 14:00:09,250 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7518 sites
2025-08-03 14:00:22,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.352, H2=-2.948, H3=-0.833
2025-08-03 14:00:22,649 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1824 sites
2025-08-03 14:00:25,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.933, H2=-5.044, H3=-8.909
2025-08-03 14:00:25,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6145 sites
2025-08-03 14:00:36,774 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=31.518, H2=12.237, H3=1.779
2025-08-03 14:00:36,806 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3638 sites
2025-08-03 14:00:43,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.019, H2=5.093, H3=5.783
2025-08-03 14:00:43,272 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4635 sites
2025-08-03 14:00:51,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.588, H2=5.950, H3=3.059
2025-08-03 14:00:51,560 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3627 sites
2025-08-03 14:00:58,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=6.036, H2=1.480, H3=2.439
2025-08-03 14:00:58,043 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 648 sites
2025-08-03 14:00:59,196 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.723, H2=-7.471, H3=1.553
2025-08-03 14:00:59,225 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1585 sites
2025-08-03 14:01:02,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=40.871, H2=1.286, H3=-4.391
2025-08-03 14:01:02,085 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1376 sites
2025-08-03 14:01:04,552 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.789, H2=-0.037, H3=-0.126
2025-08-03 14:01:04,582 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2642 sites
2025-08-03 14:01:09,340 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=14.450, H2=7.117, H3=-0.526
2025-08-03 14:01:09,368 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 833 sites
2025-08-03 14:01:10,856 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-6.069, H2=-6.500, H3=-6.707
2025-08-03 14:01:10,885 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2179 sites
2025-08-03 14:01:14,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=6.710, H2=-1.033, H3=0.612
2025-08-03 14:01:14,821 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2465 sites
2025-08-03 14:01:19,291 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-5.421, H2=-4.377, H3=1.739
2025-08-03 14:01:19,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 5984 sites
2025-08-03 14:01:30,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=41.763, H2=21.026, H3=8.322
2025-08-03 14:01:30,097 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-08-03 14:01:30,097 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-08-03 14:01:30,099 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-08-03 14:01:30,132 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4813 sites
2025-08-03 14:01:39,038 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=34.601, H2=1.033, H3=-0.640
2025-08-03 14:01:39,068 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2447 sites
2025-08-03 14:01:43,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=27.401, H2=-1.876, H3=-1.167
2025-08-03 14:01:43,506 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7518 sites
2025-08-03 14:01:57,208 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=42.352, H2=-2.948, H3=-0.833
2025-08-03 14:01:57,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1824 sites
2025-08-03 14:02:00,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=5.933, H2=-5.044, H3=-8.909
2025-08-03 14:02:00,529 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6145 sites
2025-08-03 14:02:11,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=31.518, H2=12.237, H3=1.779
2025-08-03 14:02:11,582 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1349 sites
2025-08-03 14:02:14,000 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.441, H2=5.083, H3=6.600
2025-08-03 14:02:14,030 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2289 sites
2025-08-03 14:02:18,110 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=7.737, H2=2.756, H3=2.925
2025-08-03 14:02:18,144 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4635 sites
2025-08-03 14:02:26,421 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.189, H2=5.118, H3=3.149
2025-08-03 14:02:26,453 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3627 sites
2025-08-03 14:02:32,940 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=7.839, H2=2.211, H3=2.389
2025-08-03 14:02:32,967 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 648 sites
2025-08-03 14:02:34,109 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.844, H2=-7.266, H3=1.025
2025-08-03 14:02:34,138 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1585 sites
2025-08-03 14:02:36,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=36.557, H2=1.426, H3=-4.599
2025-08-03 14:02:36,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1376 sites
2025-08-03 14:02:39,424 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=18.746, H2=-0.230, H3=-0.666
2025-08-03 14:02:39,455 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2642 sites
2025-08-03 14:02:44,137 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=9.727, H2=6.976, H3=-0.876
2025-08-03 14:02:44,164 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 833 sites
2025-08-03 14:02:45,644 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-6.687, H2=-5.677, H3=-9.300
2025-08-03 14:02:45,674 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2179 sites
2025-08-03 14:02:49,567 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=7.058, H2=-0.949, H3=0.375
2025-08-03 14:02:49,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2465 sites
2025-08-03 14:02:53,967 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-4.717, H2=-2.704, H3=1.718
2025-08-03 14:02:54,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 5984 sites
2025-08-03 14:03:04,682 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=53.688, H2=19.120, H3=8.940
2025-08-03 14:03:04,707 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-08-03 14:03:04,707 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-08-03 14:03:04,709 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-08-03 14:03:04,739 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:03:09,501 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:03:09,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:03:13,352 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:03:13,382 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2447 sites
2025-08-03 14:03:17,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=30.323, H2=-2.541, H3=-1.344
2025-08-03 14:03:17,816 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7518 sites
2025-08-03 14:03:31,209 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=42.692, H2=-2.903, H3=-0.995
2025-08-03 14:03:31,238 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1824 sites
2025-08-03 14:03:34,495 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=5.043, H2=-5.338, H3=-9.063
2025-08-03 14:03:34,531 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6145 sites
2025-08-03 14:03:45,498 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=21.437, H2=11.320, H3=2.362
2025-08-03 14:03:45,526 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1349 sites
2025-08-03 14:03:47,921 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=2.624, H2=4.563, H3=6.116
2025-08-03 14:03:47,951 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2289 sites
2025-08-03 14:03:52,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=6.681, H2=2.377, H3=2.579
2025-08-03 14:03:52,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4635 sites
2025-08-03 14:04:00,281 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=13.522, H2=4.957, H3=2.765
2025-08-03 14:04:00,313 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3627 sites
2025-08-03 14:04:06,761 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=6.780, H2=1.789, H3=2.033
2025-08-03 14:04:06,787 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 648 sites
2025-08-03 14:04:07,941 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.902, H2=-7.144, H3=2.099
2025-08-03 14:04:07,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1585 sites
2025-08-03 14:04:10,784 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=62.137, H2=1.527, H3=-5.222
2025-08-03 14:04:10,812 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1376 sites
2025-08-03 14:04:13,268 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=10.915, H2=-0.375, H3=-0.736
2025-08-03 14:04:13,298 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2642 sites
2025-08-03 14:04:18,029 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=10.169, H2=7.016, H3=-0.209
2025-08-03 14:04:18,057 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 833 sites
2025-08-03 14:04:19,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-8.603, H2=-5.314, H3=-8.217
2025-08-03 14:04:19,574 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2179 sites
2025-08-03 14:04:23,492 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=6.642, H2=-0.740, H3=0.112
2025-08-03 14:04:23,522 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2465 sites
2025-08-03 14:04:27,938 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-2.911, H2=-2.500, H3=2.135
2025-08-03 14:04:27,974 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 5984 sites
2025-08-03 14:04:38,627 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=51.915, H2=20.196, H3=9.449
2025-08-03 14:04:38,652 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-08-03 14:04:38,652 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-08-03 14:04:38,653 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-08-03 14:04:38,683 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:04:43,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:04:43,477 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:04:47,263 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:04:47,293 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2447 sites
2025-08-03 14:04:51,651 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=30.323, H2=-2.541, H3=-1.344
2025-08-03 14:04:51,684 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3951 sites
2025-08-03 14:04:58,688 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=23.798, H2=0.495, H3=-0.836
2025-08-03 14:04:58,720 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3567 sites
2025-08-03 14:05:05,087 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=53.297, H2=-6.010, H3=-1.288
2025-08-03 14:05:05,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1824 sites
2025-08-03 14:05:08,359 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.057, H2=-4.237, H3=-10.385
2025-08-03 14:05:08,395 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6145 sites
2025-08-03 14:05:19,331 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.536, H2=9.571, H3=1.617
2025-08-03 14:05:19,359 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1349 sites
2025-08-03 14:05:21,765 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=2.880, H2=4.484, H3=6.289
2025-08-03 14:05:21,795 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2289 sites
2025-08-03 14:05:25,876 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=7.723, H2=2.818, H3=1.985
2025-08-03 14:05:25,910 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4635 sites
2025-08-03 14:05:34,195 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.776, H2=4.383, H3=1.887
2025-08-03 14:05:34,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3627 sites
2025-08-03 14:05:40,681 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.459, H2=2.313, H3=2.726
2025-08-03 14:05:40,708 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 648 sites
2025-08-03 14:05:41,859 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.750, H2=-7.985, H3=1.119
2025-08-03 14:05:41,888 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1585 sites
2025-08-03 14:05:44,715 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=40.771, H2=1.402, H3=-4.986
2025-08-03 14:05:44,742 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1376 sites
2025-08-03 14:05:47,197 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=12.474, H2=-0.326, H3=-0.281
2025-08-03 14:05:47,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2642 sites
2025-08-03 14:05:51,929 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=15.009, H2=7.162, H3=-0.462
2025-08-03 14:05:51,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 833 sites
2025-08-03 14:05:53,432 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-7.412, H2=-7.789, H3=-5.812
2025-08-03 14:05:53,462 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2179 sites
2025-08-03 14:05:57,313 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=6.874, H2=-0.456, H3=0.471
2025-08-03 14:05:57,342 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2465 sites
2025-08-03 14:06:01,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-3.967, H2=-2.530, H3=2.505
2025-08-03 14:06:01,816 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 5984 sites
2025-08-03 14:06:12,534 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=48.115, H2=15.930, H3=7.585
2025-08-03 14:06:12,560 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-08-03 14:06:12,560 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-08-03 14:06:12,562 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-08-03 14:06:12,593 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:06:17,393 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:06:17,422 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:06:21,266 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:06:21,296 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2447 sites
2025-08-03 14:06:25,693 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=30.323, H2=-2.541, H3=-1.344
2025-08-03 14:06:25,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3951 sites
2025-08-03 14:06:32,804 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=23.798, H2=0.495, H3=-0.836
2025-08-03 14:06:32,836 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3567 sites
2025-08-03 14:06:39,230 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=53.297, H2=-6.010, H3=-1.288
2025-08-03 14:06:39,259 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1824 sites
2025-08-03 14:06:42,516 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.057, H2=-4.237, H3=-10.385
2025-08-03 14:06:42,553 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6145 sites
2025-08-03 14:06:53,559 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.536, H2=9.571, H3=1.617
2025-08-03 14:06:53,587 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1349 sites
2025-08-03 14:06:56,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=2.880, H2=4.484, H3=6.289
2025-08-03 14:06:56,033 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2289 sites
2025-08-03 14:07:00,114 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=7.723, H2=2.818, H3=1.985
2025-08-03 14:07:00,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4635 sites
2025-08-03 14:07:08,420 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.776, H2=4.383, H3=1.887
2025-08-03 14:07:08,452 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3627 sites
2025-08-03 14:07:14,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.459, H2=2.313, H3=2.726
2025-08-03 14:07:14,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 648 sites
2025-08-03 14:07:16,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.750, H2=-7.985, H3=1.119
2025-08-03 14:07:16,114 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1585 sites
2025-08-03 14:07:18,906 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=40.771, H2=1.402, H3=-4.986
2025-08-03 14:07:18,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 302 sites
2025-08-03 14:07:19,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.520, H2=-0.298, H3=-2.248
2025-08-03 14:07:19,493 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1074 sites
2025-08-03 14:07:21,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=17.350, H2=-0.454, H3=0.017
2025-08-03 14:07:21,432 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2642 sites
2025-08-03 14:07:26,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=10.524, H2=7.781, H3=-0.801
2025-08-03 14:07:26,161 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 833 sites
2025-08-03 14:07:27,640 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-6.723, H2=-5.171, H3=-7.464
2025-08-03 14:07:27,669 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2179 sites
2025-08-03 14:07:31,567 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.067, H2=-0.571, H3=1.078
2025-08-03 14:07:31,597 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2465 sites
2025-08-03 14:07:35,988 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-2.991, H2=-2.886, H3=1.730
2025-08-03 14:07:36,026 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 5984 sites
2025-08-03 14:07:46,714 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=54.535, H2=19.261, H3=9.560
2025-08-03 14:07:46,739 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-08-03 14:07:46,739 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-08-03 14:07:46,741 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-08-03 14:07:46,770 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:07:51,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:07:51,580 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:07:55,365 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:07:55,395 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2447 sites
2025-08-03 14:07:59,761 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=30.323, H2=-2.541, H3=-1.344
2025-08-03 14:07:59,794 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3951 sites
2025-08-03 14:08:06,877 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=23.798, H2=0.495, H3=-0.836
2025-08-03 14:08:06,909 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3567 sites
2025-08-03 14:08:13,266 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=53.297, H2=-6.010, H3=-1.288
2025-08-03 14:08:13,295 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1824 sites
2025-08-03 14:08:16,531 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.057, H2=-4.237, H3=-10.385
2025-08-03 14:08:16,561 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2545 sites
2025-08-03 14:08:21,110 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.646, H2=1.076, H3=1.193
2025-08-03 14:08:21,142 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3600 sites
2025-08-03 14:08:27,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=25.130, H2=10.897, H3=1.795
2025-08-03 14:08:27,605 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1349 sites
2025-08-03 14:08:30,008 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.745, H2=5.389, H3=5.426
2025-08-03 14:08:30,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2289 sites
2025-08-03 14:08:34,128 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=7.666, H2=2.275, H3=1.768
2025-08-03 14:08:34,162 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4635 sites
2025-08-03 14:08:42,483 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.675, H2=6.313, H3=3.536
2025-08-03 14:08:42,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3627 sites
2025-08-03 14:08:48,997 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=8.608, H2=2.264, H3=3.586
2025-08-03 14:08:49,024 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 648 sites
2025-08-03 14:08:50,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.185, H2=-8.505, H3=1.493
2025-08-03 14:08:50,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1585 sites
2025-08-03 14:08:53,044 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=37.567, H2=1.348, H3=-4.279
2025-08-03 14:08:53,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 302 sites
2025-08-03 14:08:53,611 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-3.830, H2=0.525, H3=-1.225
2025-08-03 14:08:53,639 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1074 sites
2025-08-03 14:08:55,559 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=15.926, H2=-0.523, H3=-0.012
2025-08-03 14:08:55,590 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2642 sites
2025-08-03 14:09:00,327 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=8.738, H2=6.115, H3=-0.629
2025-08-03 14:09:00,354 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 833 sites
2025-08-03 14:09:01,840 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-7.539, H2=-6.856, H3=-7.697
2025-08-03 14:09:01,870 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2179 sites
2025-08-03 14:09:05,761 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=5.104, H2=-0.943, H3=0.180
2025-08-03 14:09:05,791 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2465 sites
2025-08-03 14:09:10,203 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-2.837, H2=-2.804, H3=2.393
2025-08-03 14:09:10,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 5984 sites
2025-08-03 14:09:20,912 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=52.514, H2=25.952, H3=10.327
2025-08-03 14:09:20,937 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-08-03 14:09:20,938 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-08-03 14:09:20,939 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-08-03 14:09:20,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:09:25,785 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:09:25,815 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:09:29,633 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:09:29,661 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:09:32,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:09:32,207 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:09:34,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:09:34,102 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
2025-08-03 14:09:41,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=26.685, H2=-0.095, H3=-0.985
2025-08-03 14:09:41,209 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3567 sites
2025-08-03 14:09:47,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.109, H2=-4.965, H3=-1.057
2025-08-03 14:09:47,590 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1824 sites
2025-08-03 14:09:50,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.101, H2=-4.370, H3=-9.895
2025-08-03 14:09:50,885 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2545 sites
2025-08-03 14:09:55,415 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.194, H2=0.650, H3=0.422
2025-08-03 14:09:55,447 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3600 sites
2025-08-03 14:10:01,849 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.793, H2=16.682, H3=1.825
2025-08-03 14:10:01,876 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1349 sites
2025-08-03 14:10:04,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.500, H2=5.249, H3=4.696
2025-08-03 14:10:04,302 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2289 sites
2025-08-03 14:10:08,364 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.567, H2=2.472, H3=2.787
2025-08-03 14:10:08,398 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4635 sites
2025-08-03 14:10:16,648 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.659, H2=8.134, H3=3.454
2025-08-03 14:10:16,680 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3627 sites
2025-08-03 14:10:23,089 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.120, H2=1.704, H3=2.136
2025-08-03 14:10:23,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 648 sites
2025-08-03 14:10:24,268 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.421, H2=-7.664, H3=1.278
2025-08-03 14:10:24,296 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1585 sites
2025-08-03 14:10:27,122 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=45.873, H2=0.812, H3=-4.352
2025-08-03 14:10:27,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 302 sites
2025-08-03 14:10:27,689 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-4.073, H2=0.315, H3=-1.005
2025-08-03 14:10:27,716 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1074 sites
2025-08-03 14:10:29,659 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=17.860, H2=-0.475, H3=-0.013
2025-08-03 14:10:29,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2642 sites
2025-08-03 14:10:34,479 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=11.287, H2=7.057, H3=-0.073
2025-08-03 14:10:34,506 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 833 sites
2025-08-03 14:10:36,001 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-10.366, H2=-6.629, H3=-8.781
2025-08-03 14:10:36,031 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2179 sites
2025-08-03 14:10:39,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.732, H2=-0.754, H3=0.640
2025-08-03 14:10:39,987 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2465 sites
2025-08-03 14:10:44,413 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-3.491, H2=-3.971, H3=2.566
2025-08-03 14:10:44,449 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 5984 sites
2025-08-03 14:10:55,196 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=53.591, H2=21.655, H3=9.228
2025-08-03 14:10:55,221 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-08-03 14:10:55,221 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-08-03 14:10:55,222 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-08-03 14:10:55,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:11:00,054 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:11:00,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:11:03,928 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:11:03,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:11:06,462 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:11:06,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:11:08,352 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:11:08,385 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
2025-08-03 14:11:15,479 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=26.685, H2=-0.095, H3=-0.985
2025-08-03 14:11:15,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3567 sites
2025-08-03 14:11:21,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.109, H2=-4.965, H3=-1.057
2025-08-03 14:11:21,912 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1824 sites
2025-08-03 14:11:25,151 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.101, H2=-4.370, H3=-9.895
2025-08-03 14:11:25,181 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2545 sites
2025-08-03 14:11:29,719 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.194, H2=0.650, H3=0.422
2025-08-03 14:11:29,752 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3600 sites
2025-08-03 14:11:36,147 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.793, H2=16.682, H3=1.825
2025-08-03 14:11:36,175 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1349 sites
2025-08-03 14:11:38,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.500, H2=5.249, H3=4.696
2025-08-03 14:11:38,607 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2289 sites
2025-08-03 14:11:42,704 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.567, H2=2.472, H3=2.787
2025-08-03 14:11:42,737 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4635 sites
2025-08-03 14:11:51,014 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.659, H2=8.134, H3=3.454
2025-08-03 14:11:51,042 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1205 sites
2025-08-03 14:11:53,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.628, H2=3.494, H3=3.092
2025-08-03 14:11:53,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2422 sites
2025-08-03 14:11:57,552 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.222, H2=-1.744, H3=-0.103
2025-08-03 14:11:57,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 648 sites
2025-08-03 14:11:58,728 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.467, H2=-6.088, H3=1.708
2025-08-03 14:11:58,757 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1585 sites
2025-08-03 14:12:01,570 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=36.316, H2=2.062, H3=-4.740
2025-08-03 14:12:01,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 302 sites
2025-08-03 14:12:02,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-4.750, H2=0.327, H3=-1.392
2025-08-03 14:12:02,159 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1074 sites
2025-08-03 14:12:04,074 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=13.906, H2=-0.783, H3=0.131
2025-08-03 14:12:04,105 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2642 sites
2025-08-03 14:12:08,809 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=8.609, H2=6.589, H3=-1.041
2025-08-03 14:12:08,836 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 833 sites
2025-08-03 14:12:10,322 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-6.600, H2=-7.282, H3=-8.210
2025-08-03 14:12:10,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2179 sites
2025-08-03 14:12:14,217 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=9.122, H2=-1.161, H3=0.740
2025-08-03 14:12:14,246 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2465 sites
2025-08-03 14:12:18,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-3.466, H2=-2.962, H3=2.596
2025-08-03 14:12:18,653 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 5984 sites
2025-08-03 14:12:29,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=48.397, H2=19.717, H3=9.933
2025-08-03 14:12:29,301 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-08-03 14:12:29,301 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-08-03 14:12:29,302 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-08-03 14:12:29,333 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:12:34,141 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:12:34,171 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:12:38,009 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:12:38,037 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:12:40,557 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:12:40,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:12:42,446 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:12:42,479 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
2025-08-03 14:12:49,553 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=26.685, H2=-0.095, H3=-0.985
2025-08-03 14:12:49,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3567 sites
2025-08-03 14:12:55,929 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.109, H2=-4.965, H3=-1.057
2025-08-03 14:12:55,958 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1824 sites
2025-08-03 14:12:59,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.101, H2=-4.370, H3=-9.895
2025-08-03 14:12:59,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2545 sites
2025-08-03 14:13:03,765 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.194, H2=0.650, H3=0.422
2025-08-03 14:13:03,797 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3600 sites
2025-08-03 14:13:10,255 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.793, H2=16.682, H3=1.825
2025-08-03 14:13:10,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1349 sites
2025-08-03 14:13:12,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.500, H2=5.249, H3=4.696
2025-08-03 14:13:12,716 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2289 sites
2025-08-03 14:13:16,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.567, H2=2.472, H3=2.787
2025-08-03 14:13:16,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4635 sites
2025-08-03 14:13:25,110 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.659, H2=8.134, H3=3.454
2025-08-03 14:13:25,137 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1205 sites
2025-08-03 14:13:27,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.628, H2=3.494, H3=3.092
2025-08-03 14:13:27,315 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2422 sites
2025-08-03 14:13:31,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.222, H2=-1.744, H3=-0.103
2025-08-03 14:13:31,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 648 sites
2025-08-03 14:13:32,832 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.467, H2=-6.088, H3=1.708
2025-08-03 14:13:32,859 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 819 sites
2025-08-03 14:13:34,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=30.554, H2=0.413, H3=-1.959
2025-08-03 14:13:34,352 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 766 sites
2025-08-03 14:13:35,717 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=17.600, H2=-2.312, H3=-5.656
2025-08-03 14:13:35,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 302 sites
2025-08-03 14:13:36,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-6.104, H2=0.449, H3=-1.220
2025-08-03 14:13:36,313 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1074 sites
2025-08-03 14:13:38,225 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=28.315, H2=-0.268, H3=-0.288
2025-08-03 14:13:38,255 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2642 sites
2025-08-03 14:13:42,970 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=9.039, H2=7.258, H3=-0.551
2025-08-03 14:13:42,997 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 833 sites
2025-08-03 14:13:44,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-7.880, H2=-8.297, H3=-7.783
2025-08-03 14:13:44,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2179 sites
2025-08-03 14:13:48,429 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=6.337, H2=-0.329, H3=0.749
2025-08-03 14:13:48,459 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2465 sites
2025-08-03 14:13:52,870 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-2.391, H2=-2.777, H3=1.962
2025-08-03 14:13:52,906 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 5984 sites
2025-08-03 14:14:03,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=48.509, H2=22.827, H3=11.398
2025-08-03 14:14:03,576 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-08-03 14:14:03,577 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-08-03 14:14:03,578 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-08-03 14:14:03,608 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:14:08,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:14:08,384 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:14:12,172 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:14:12,201 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:14:14,719 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:14:14,746 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:14:16,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:14:16,627 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
2025-08-03 14:14:23,720 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=26.685, H2=-0.095, H3=-0.985
2025-08-03 14:14:23,753 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3567 sites
2025-08-03 14:14:30,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.109, H2=-4.965, H3=-1.057
2025-08-03 14:14:30,164 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1824 sites
2025-08-03 14:14:33,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.101, H2=-4.370, H3=-9.895
2025-08-03 14:14:33,507 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2545 sites
2025-08-03 14:14:38,113 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.194, H2=0.650, H3=0.422
2025-08-03 14:14:38,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3600 sites
2025-08-03 14:14:44,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.793, H2=16.682, H3=1.825
2025-08-03 14:14:44,675 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1349 sites
2025-08-03 14:14:47,072 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.500, H2=5.249, H3=4.696
2025-08-03 14:14:47,102 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2289 sites
2025-08-03 14:14:51,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.567, H2=2.472, H3=2.787
2025-08-03 14:14:51,278 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4635 sites
2025-08-03 14:14:59,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.659, H2=8.134, H3=3.454
2025-08-03 14:14:59,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1205 sites
2025-08-03 14:15:01,815 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.628, H2=3.494, H3=3.092
2025-08-03 14:15:01,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2422 sites
2025-08-03 14:15:06,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.222, H2=-1.744, H3=-0.103
2025-08-03 14:15:06,165 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 648 sites
2025-08-03 14:15:07,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.467, H2=-6.088, H3=1.708
2025-08-03 14:15:07,352 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 819 sites
2025-08-03 14:15:08,819 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=30.554, H2=0.413, H3=-1.959
2025-08-03 14:15:08,846 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 766 sites
2025-08-03 14:15:10,208 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=17.600, H2=-2.312, H3=-5.656
2025-08-03 14:15:10,234 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 302 sites
2025-08-03 14:15:10,772 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-6.104, H2=0.449, H3=-1.220
2025-08-03 14:15:10,799 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1074 sites
2025-08-03 14:15:12,718 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=28.315, H2=-0.268, H3=-0.288
2025-08-03 14:15:12,746 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1137 sites
2025-08-03 14:15:14,773 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.567, H2=1.792, H3=-0.149
2025-08-03 14:15:14,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1505 sites
2025-08-03 14:15:17,472 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=8.301, H2=4.248, H3=-2.257
2025-08-03 14:15:17,499 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 833 sites
2025-08-03 14:15:18,980 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-6.690, H2=-6.534, H3=-8.912
2025-08-03 14:15:19,010 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2179 sites
2025-08-03 14:15:22,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=5.342, H2=-0.393, H3=0.713
2025-08-03 14:15:22,922 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2465 sites
2025-08-03 14:15:27,288 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-2.743, H2=-2.684, H3=3.849
2025-08-03 14:15:27,324 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 5984 sites
2025-08-03 14:15:38,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=50.419, H2=19.140, H3=10.119
2025-08-03 14:15:38,088 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-08-03 14:15:38,088 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-08-03 14:15:38,090 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-08-03 14:15:38,121 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:15:42,912 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:15:42,941 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:15:46,768 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:15:46,796 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:15:49,320 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:15:49,348 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:15:51,204 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:15:51,236 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
2025-08-03 14:15:58,324 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=26.685, H2=-0.095, H3=-0.985
2025-08-03 14:15:58,356 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3567 sites
2025-08-03 14:16:04,742 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.109, H2=-4.965, H3=-1.057
2025-08-03 14:16:04,771 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1824 sites
2025-08-03 14:16:08,024 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.101, H2=-4.370, H3=-9.895
2025-08-03 14:16:08,054 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2545 sites
2025-08-03 14:16:12,602 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.194, H2=0.650, H3=0.422
2025-08-03 14:16:12,634 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3600 sites
2025-08-03 14:16:19,125 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.793, H2=16.682, H3=1.825
2025-08-03 14:16:19,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1349 sites
2025-08-03 14:16:21,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.500, H2=5.249, H3=4.696
2025-08-03 14:16:21,614 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2289 sites
2025-08-03 14:16:25,726 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.567, H2=2.472, H3=2.787
2025-08-03 14:16:25,761 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4635 sites
2025-08-03 14:16:34,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.659, H2=8.134, H3=3.454
2025-08-03 14:16:34,090 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1205 sites
2025-08-03 14:16:36,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.628, H2=3.494, H3=3.092
2025-08-03 14:16:36,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2422 sites
2025-08-03 14:16:40,597 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.222, H2=-1.744, H3=-0.103
2025-08-03 14:16:40,622 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 135 sites
2025-08-03 14:16:40,862 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.705, H2=-3.306, H3=1.379
2025-08-03 14:16:40,888 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 513 sites
2025-08-03 14:16:41,809 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=0.951, H2=-9.504, H3=0.891
2025-08-03 14:16:41,836 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 819 sites
2025-08-03 14:16:43,305 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=29.546, H2=0.238, H3=-2.149
2025-08-03 14:16:43,332 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 766 sites
2025-08-03 14:16:44,702 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=13.260, H2=-2.706, H3=-6.682
2025-08-03 14:16:44,728 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 302 sites
2025-08-03 14:16:45,268 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-4.681, H2=0.772, H3=-1.613
2025-08-03 14:16:45,295 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1074 sites
2025-08-03 14:16:47,225 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=12.694, H2=-0.505, H3=-0.210
2025-08-03 14:16:47,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1137 sites
2025-08-03 14:16:49,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=9.641, H2=1.888, H3=-0.083
2025-08-03 14:16:49,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1505 sites
2025-08-03 14:16:51,993 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.229, H2=5.364, H3=-2.256
2025-08-03 14:16:52,020 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 833 sites
2025-08-03 14:16:53,511 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-8.146, H2=-6.563, H3=-5.559
2025-08-03 14:16:53,540 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2179 sites
2025-08-03 14:16:57,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=6.907, H2=-0.228, H3=1.235
2025-08-03 14:16:57,442 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2465 sites
2025-08-03 14:17:01,841 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-3.974, H2=-2.810, H3=1.839
2025-08-03 14:17:01,877 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 5984 sites
2025-08-03 14:17:12,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=47.305, H2=18.542, H3=10.701
2025-08-03 14:17:12,593 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-08-03 14:17:12,593 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-08-03 14:17:12,595 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-08-03 14:17:12,624 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:17:17,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:17:17,436 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:17:21,214 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:17:21,242 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:17:23,752 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:17:23,779 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:17:25,631 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:17:25,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
2025-08-03 14:17:32,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=26.685, H2=-0.095, H3=-0.985
2025-08-03 14:17:32,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3567 sites
2025-08-03 14:17:39,015 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.109, H2=-4.965, H3=-1.057
2025-08-03 14:17:39,044 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1824 sites
2025-08-03 14:17:42,296 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.101, H2=-4.370, H3=-9.895
2025-08-03 14:17:42,326 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2545 sites
2025-08-03 14:17:46,848 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.194, H2=0.650, H3=0.422
2025-08-03 14:17:46,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3600 sites
2025-08-03 14:17:53,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.793, H2=16.682, H3=1.825
2025-08-03 14:17:53,338 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1349 sites
2025-08-03 14:17:55,733 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.500, H2=5.249, H3=4.696
2025-08-03 14:17:55,762 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2289 sites
2025-08-03 14:17:59,817 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.567, H2=2.472, H3=2.787
2025-08-03 14:17:59,851 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4635 sites
2025-08-03 14:18:08,175 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.659, H2=8.134, H3=3.454
2025-08-03 14:18:08,202 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1205 sites
2025-08-03 14:18:10,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.628, H2=3.494, H3=3.092
2025-08-03 14:18:10,365 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2422 sites
2025-08-03 14:18:14,749 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.222, H2=-1.744, H3=-0.103
2025-08-03 14:18:14,774 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 135 sites
2025-08-03 14:18:15,019 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.705, H2=-3.306, H3=1.379
2025-08-03 14:18:15,044 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 513 sites
2025-08-03 14:18:15,973 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=0.951, H2=-9.504, H3=0.891
2025-08-03 14:18:16,000 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 819 sites
2025-08-03 14:18:17,472 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=29.546, H2=0.238, H3=-2.149
2025-08-03 14:18:17,499 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 766 sites
2025-08-03 14:18:18,869 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=13.260, H2=-2.706, H3=-6.682
2025-08-03 14:18:18,895 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 302 sites
2025-08-03 14:18:19,437 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-4.681, H2=0.772, H3=-1.613
2025-08-03 14:18:19,464 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1074 sites
2025-08-03 14:18:21,396 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=12.694, H2=-0.505, H3=-0.210
2025-08-03 14:18:21,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1137 sites
2025-08-03 14:18:23,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=9.641, H2=1.888, H3=-0.083
2025-08-03 14:18:23,475 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1505 sites
2025-08-03 14:18:26,165 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.229, H2=5.364, H3=-2.256
2025-08-03 14:18:26,192 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 833 sites
2025-08-03 14:18:27,681 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-8.146, H2=-6.563, H3=-5.559
2025-08-03 14:18:27,711 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2179 sites
2025-08-03 14:18:31,622 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=6.907, H2=-0.228, H3=1.235
2025-08-03 14:18:31,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2465 sites
2025-08-03 14:18:36,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-3.974, H2=-2.810, H3=1.839
2025-08-03 14:18:36,086 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1798 sites
2025-08-03 14:18:39,304 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=3.862, H2=5.795, H3=3.461
2025-08-03 14:18:39,337 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 4186 sites
2025-08-03 14:18:46,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=78.814, H2=19.730, H3=6.240
2025-08-03 14:18:46,838 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-08-03 14:18:46,838 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-08-03 14:18:46,839 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-08-03 14:18:46,869 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:18:51,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:18:51,694 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:18:55,503 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:18:55,531 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:18:58,040 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:18:58,068 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:18:59,918 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:18:59,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
2025-08-03 14:19:06,961 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=26.685, H2=-0.095, H3=-0.985
2025-08-03 14:19:06,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3567 sites
2025-08-03 14:19:13,379 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.109, H2=-4.965, H3=-1.057
2025-08-03 14:19:13,408 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1824 sites
2025-08-03 14:19:16,666 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.101, H2=-4.370, H3=-9.895
2025-08-03 14:19:16,696 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2545 sites
2025-08-03 14:19:21,241 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.194, H2=0.650, H3=0.422
2025-08-03 14:19:21,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3600 sites
2025-08-03 14:19:27,664 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=29.793, H2=16.682, H3=1.825
2025-08-03 14:19:27,692 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1349 sites
2025-08-03 14:19:30,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.500, H2=5.249, H3=4.696
2025-08-03 14:19:30,128 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2289 sites
2025-08-03 14:19:34,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.567, H2=2.472, H3=2.787
2025-08-03 14:19:34,233 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4635 sites
2025-08-03 14:19:42,445 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.659, H2=8.134, H3=3.454
2025-08-03 14:19:42,473 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1205 sites
2025-08-03 14:19:44,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.628, H2=3.494, H3=3.092
2025-08-03 14:19:44,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2422 sites
2025-08-03 14:19:48,931 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=5.222, H2=-1.744, H3=-0.103
2025-08-03 14:19:48,957 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 135 sites
2025-08-03 14:19:49,196 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.705, H2=-3.306, H3=1.379
2025-08-03 14:19:49,222 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 513 sites
2025-08-03 14:19:50,129 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=0.951, H2=-9.504, H3=0.891
2025-08-03 14:19:50,156 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 819 sites
2025-08-03 14:19:51,619 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=29.546, H2=0.238, H3=-2.149
2025-08-03 14:19:51,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 766 sites
2025-08-03 14:19:53,014 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=13.260, H2=-2.706, H3=-6.682
2025-08-03 14:19:53,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 302 sites
2025-08-03 14:19:53,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-4.681, H2=0.772, H3=-1.613
2025-08-03 14:19:53,604 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1074 sites
2025-08-03 14:19:55,510 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=12.694, H2=-0.505, H3=-0.210
2025-08-03 14:19:55,537 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1137 sites
2025-08-03 14:19:57,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=9.641, H2=1.888, H3=-0.083
2025-08-03 14:19:57,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 575 sites
2025-08-03 14:19:58,595 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-1.234, H2=3.845, H3=-1.404
2025-08-03 14:19:58,622 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 930 sites
2025-08-03 14:20:00,270 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=5.360, H2=2.614, H3=-1.090
2025-08-03 14:20:00,297 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 833 sites
2025-08-03 14:20:01,772 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-9.441, H2=-8.875, H3=-9.158
2025-08-03 14:20:01,802 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2179 sites
2025-08-03 14:20:05,668 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=6.921, H2=-0.758, H3=0.535
2025-08-03 14:20:05,698 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2465 sites
2025-08-03 14:20:10,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-6.044, H2=-3.363, H3=2.277
2025-08-03 14:20:10,087 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1798 sites
2025-08-03 14:20:13,253 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=3.741, H2=5.175, H3=3.373
2025-08-03 14:20:13,286 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 4186 sites
2025-08-03 14:20:20,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=61.211, H2=18.780, H3=5.845
2025-08-03 14:20:20,769 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-08-03 14:20:20,769 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-08-03 14:20:20,771 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-08-03 14:20:20,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:20:25,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:20:25,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:20:29,389 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:20:29,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:20:31,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:20:31,912 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:20:33,753 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:20:33,785 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
2025-08-03 14:20:40,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=26.685, H2=-0.095, H3=-0.985
2025-08-03 14:20:40,807 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2133 sites
2025-08-03 14:20:44,586 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=22.751, H2=-4.466, H3=-0.987
2025-08-03 14:20:44,614 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1434 sites
2025-08-03 14:20:47,156 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=20.968, H2=-2.572, H3=-1.030
2025-08-03 14:20:47,184 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1824 sites
2025-08-03 14:20:50,438 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.678, H2=-3.873, H3=-10.364
2025-08-03 14:20:50,468 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2545 sites
2025-08-03 14:20:55,010 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=0.181, H2=0.680, H3=0.640
2025-08-03 14:20:55,042 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3600 sites
2025-08-03 14:21:01,449 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=29.316, H2=11.377, H3=1.355
2025-08-03 14:21:01,477 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1349 sites
2025-08-03 14:21:03,865 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.051, H2=4.193, H3=5.983
2025-08-03 14:21:03,895 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2289 sites
2025-08-03 14:21:07,953 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=8.583, H2=3.125, H3=3.613
2025-08-03 14:21:07,986 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4635 sites
2025-08-03 14:21:16,203 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.883, H2=5.720, H3=2.282
2025-08-03 14:21:16,231 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1205 sites
2025-08-03 14:21:18,368 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.687, H2=4.078, H3=3.653
2025-08-03 14:21:18,398 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2422 sites
2025-08-03 14:21:22,719 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=6.774, H2=-1.662, H3=-0.309
2025-08-03 14:21:22,745 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 135 sites
2025-08-03 14:21:22,985 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=1.652, H2=-4.675, H3=1.252
2025-08-03 14:21:23,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 513 sites
2025-08-03 14:21:23,927 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=1.486, H2=-6.451, H3=1.130
2025-08-03 14:21:23,954 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 819 sites
2025-08-03 14:21:25,419 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=30.875, H2=0.022, H3=-2.138
2025-08-03 14:21:25,447 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 766 sites
2025-08-03 14:21:26,823 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=18.963, H2=-1.991, H3=-7.575
2025-08-03 14:21:26,849 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 302 sites
2025-08-03 14:21:27,391 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-4.567, H2=0.680, H3=-1.357
2025-08-03 14:21:27,419 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1074 sites
2025-08-03 14:21:29,340 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=23.122, H2=-0.547, H3=0.040
2025-08-03 14:21:29,368 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1137 sites
2025-08-03 14:21:31,390 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=7.496, H2=2.560, H3=-0.183
2025-08-03 14:21:31,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 575 sites
2025-08-03 14:21:32,439 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-1.350, H2=2.161, H3=-0.800
2025-08-03 14:21:32,466 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 930 sites
2025-08-03 14:21:34,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=7.978, H2=3.574, H3=-1.418
2025-08-03 14:21:34,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 833 sites
2025-08-03 14:21:35,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-9.277, H2=-6.513, H3=-6.694
2025-08-03 14:21:35,651 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2179 sites
2025-08-03 14:21:39,512 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=7.211, H2=-0.973, H3=0.658
2025-08-03 14:21:39,542 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2465 sites
2025-08-03 14:21:43,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-3.187, H2=-2.552, H3=1.881
2025-08-03 14:21:43,921 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1798 sites
2025-08-03 14:21:47,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=5.738, H2=6.886, H3=3.073
2025-08-03 14:21:47,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 4186 sites
2025-08-03 14:21:54,576 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=66.923, H2=23.534, H3=6.567
2025-08-03 14:21:54,602 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-08-03 14:21:54,602 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-08-03 14:21:54,603 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-08-03 14:21:54,633 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2678 sites
2025-08-03 14:21:59,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=14.353, H2=-0.538, H3=-4.089
2025-08-03 14:21:59,385 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2135 sites
2025-08-03 14:22:03,163 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=20.993, H2=0.840, H3=2.679
2025-08-03 14:22:03,192 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1407 sites
2025-08-03 14:22:05,673 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.541, H2=0.343, H3=0.439
2025-08-03 14:22:05,700 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1040 sites
2025-08-03 14:22:07,531 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=10.341, H2=-3.713, H3=-3.246
2025-08-03 14:22:07,564 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3951 sites
