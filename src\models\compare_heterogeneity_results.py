#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON><PERSON>t to compare heterogeneity results across different regionalization methods.

This script analyzes and compares the heterogeneity statistics from different
regionalization approaches (with and without weather typing).
"""

import os
import sys
import glob
import logging
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.utils.logging_config import setup_logging

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Compare heterogeneity results across different regionalization methods.'
    )
    
    parser.add_argument(
        '--results-dir',
        type=str,
        default='heterogeneity_results',
        help='Base directory containing heterogeneity results (default: heterogeneity_results)'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default='heterogeneity_comparison',
        help='Directory to save comparison results (default: heterogeneity_comparison)'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='Set the logging level (default: INFO)'
    )
    
    return parser.parse_args()

def find_heterogeneity_results(base_dir):
    """
    Find all heterogeneity result directories.
    
    Parameters:
    -----------
    base_dir : str
        Base directory containing heterogeneity results
        
    Returns:
    --------
    dict
        Dictionary mapping method names to result directories
    """
    # Find all subdirectories in the base directory
    result_dirs = {}
    
    # Find directories for results without weather typing
    wo_wt_dirs = glob.glob(os.path.join(base_dir, "regionalization_wo_WT"))
    if wo_wt_dirs:
        result_dirs["without_WT"] = wo_wt_dirs[0]
    
    # Find directories for results with weather typing
    w_wt_dirs = glob.glob(os.path.join(base_dir, "regionalization_w_WT_*"))
    for dir_path in w_wt_dirs:
        # Extract the WT number from the directory name
        dir_name = os.path.basename(dir_path)
        wt_num = dir_name.split("_")[-1]
        result_dirs[f"with_WT_{wt_num}"] = dir_path
    
    return result_dirs

def load_heterogeneity_summary(result_dir):
    """
    Load heterogeneity summary data from a result directory.
    
    Parameters:
    -----------
    result_dir : str
        Directory containing heterogeneity results
        
    Returns:
    --------
    pandas.DataFrame or None
        DataFrame containing heterogeneity summary data, or None if not found
    """
    summary_path = os.path.join(result_dir, "heterogeneity_summary.csv")
    if not os.path.exists(summary_path):
        logger.warning(f"Heterogeneity summary not found: {summary_path}")
        return None
    
    try:
        df = pd.read_csv(summary_path)
        return df
    except Exception as e:
        logger.error(f"Error loading heterogeneity summary from {summary_path}: {str(e)}")
        return None

def load_overall_summary(result_dir):
    """
    Load overall heterogeneity summary data from a result directory.
    
    Parameters:
    -----------
    result_dir : str
        Directory containing heterogeneity results
        
    Returns:
    --------
    pandas.DataFrame or None
        DataFrame containing overall heterogeneity summary data, or None if not found
    """
    summary_path = os.path.join(result_dir, "heterogeneity_overall_summary.csv")
    if not os.path.exists(summary_path):
        logger.warning(f"Overall heterogeneity summary not found: {summary_path}")
        return None
    
    try:
        df = pd.read_csv(summary_path)
        return df
    except Exception as e:
        logger.error(f"Error loading overall heterogeneity summary from {summary_path}: {str(e)}")
        return None

def compare_heterogeneity_statistics(results_data, output_dir):
    """
    Compare heterogeneity statistics across different regionalization methods.
    
    Parameters:
    -----------
    results_data : dict
        Dictionary mapping method names to DataFrames with heterogeneity data
    output_dir : str
        Directory to save comparison results
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Combine data from all methods
    combined_data = []
    for method, df in results_data.items():
        if df is not None:
            df_copy = df.copy()
            df_copy["method"] = method
            combined_data.append(df_copy)
    
    if not combined_data:
        logger.warning("No valid heterogeneity data found for comparison")
        return
    
    combined_df = pd.concat(combined_data, ignore_index=True)
    
    # Save combined data
    combined_path = os.path.join(output_dir, "combined_heterogeneity_summary.csv")
    combined_df.to_csv(combined_path, index=False)
    logger.info(f"Combined heterogeneity summary saved to {combined_path}")
    
    # Create comparison plots
    create_comparison_plots(combined_df, output_dir)

def compare_overall_statistics(results_data, output_dir):
    """
    Compare overall heterogeneity statistics across different regionalization methods.
    
    Parameters:
    -----------
    results_data : dict
        Dictionary mapping method names to DataFrames with overall heterogeneity data
    output_dir : str
        Directory to save comparison results
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Combine data from all methods
    combined_data = []
    for method, df in results_data.items():
        if df is not None:
            df_copy = df.copy()
            df_copy["method"] = method
            combined_data.append(df_copy)
    
    if not combined_data:
        logger.warning("No valid overall heterogeneity data found for comparison")
        return
    
    combined_df = pd.concat(combined_data, ignore_index=True)
    
    # Save combined data
    combined_path = os.path.join(output_dir, "combined_overall_summary.csv")
    combined_df.to_csv(combined_path, index=False)
    logger.info(f"Combined overall summary saved to {combined_path}")
    
    # Create comparison plots
    create_overall_comparison_plots(combined_df, output_dir)

def create_comparison_plots(df, output_dir):
    """
    Create comparison plots for heterogeneity statistics.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Combined DataFrame with heterogeneity data from all methods
    output_dir : str
        Directory to save plots
    """
    # Set plot style
    sns.set(style="whitegrid")
    
    # Plot H1 statistics by k and method
    plt.figure(figsize=(12, 8))
    sns.boxplot(x="k", y="H1", hue="method", data=df)
    plt.axhline(y=1, color='g', linestyle='--', alpha=0.7, label="H=1 (Acceptably homogeneous)")
    plt.axhline(y=2, color='r', linestyle='--', alpha=0.7, label="H=2 (Possibly heterogeneous)")
    plt.title("H1 Statistic Comparison Across Methods")
    plt.xlabel("Number of Clusters (k)")
    plt.ylabel("H1 Statistic")
    plt.legend(title="Method")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "H1_comparison.png"), dpi=300)
    plt.close()
    
    # Plot H2 statistics by k and method
    plt.figure(figsize=(12, 8))
    sns.boxplot(x="k", y="H2", hue="method", data=df)
    plt.axhline(y=1, color='g', linestyle='--', alpha=0.7, label="H=1 (Acceptably homogeneous)")
    plt.axhline(y=2, color='r', linestyle='--', alpha=0.7, label="H=2 (Possibly heterogeneous)")
    plt.title("H2 Statistic Comparison Across Methods")
    plt.xlabel("Number of Clusters (k)")
    plt.ylabel("H2 Statistic")
    plt.legend(title="Method")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "H2_comparison.png"), dpi=300)
    plt.close()
    
    # Plot H3 statistics by k and method
    plt.figure(figsize=(12, 8))
    sns.boxplot(x="k", y="H3", hue="method", data=df)
    plt.axhline(y=1, color='g', linestyle='--', alpha=0.7, label="H=1 (Acceptably homogeneous)")
    plt.axhline(y=2, color='r', linestyle='--', alpha=0.7, label="H=2 (Possibly heterogeneous)")
    plt.title("H3 Statistic Comparison Across Methods")
    plt.xlabel("Number of Clusters (k)")
    plt.ylabel("H3 Statistic")
    plt.legend(title="Method")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "H3_comparison.png"), dpi=300)
    plt.close()
    
    # Create scatter plot of H1 vs H2
    plt.figure(figsize=(10, 8))
    sns.scatterplot(x="H1", y="H2", hue="method", style="k", data=df)
    plt.axhline(y=1, color='g', linestyle='--', alpha=0.5)
    plt.axhline(y=2, color='r', linestyle='--', alpha=0.5)
    plt.axvline(x=1, color='g', linestyle='--', alpha=0.5)
    plt.axvline(x=2, color='r', linestyle='--', alpha=0.5)
    plt.title("H1 vs H2 Statistic Comparison")
    plt.xlabel("H1 Statistic")
    plt.ylabel("H2 Statistic")
    plt.legend(title="Method")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "H1_vs_H2.png"), dpi=300)
    plt.close()

def create_overall_comparison_plots(df, output_dir):
    """
    Create comparison plots for overall heterogeneity statistics.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Combined DataFrame with overall heterogeneity data from all methods
    output_dir : str
        Directory to save plots
    """
    # Set plot style
    sns.set(style="whitegrid")
    
    # Plot average H1 by k and method
    plt.figure(figsize=(12, 8))
    sns.lineplot(x="k", y="avg_H1", hue="method", marker="o", data=df)
    plt.axhline(y=1, color='g', linestyle='--', alpha=0.7, label="H=1 (Acceptably homogeneous)")
    plt.axhline(y=2, color='r', linestyle='--', alpha=0.7, label="H=2 (Possibly heterogeneous)")
    plt.title("Average H1 Statistic Comparison Across Methods")
    plt.xlabel("Number of Clusters (k)")
    plt.ylabel("Average H1 Statistic")
    plt.legend(title="Method")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "avg_H1_comparison.png"), dpi=300)
    plt.close()
    
    # Plot percentage of acceptable regions by k and method
    plt.figure(figsize=(12, 8))
    sns.lineplot(x="k", y="pct_acceptable", hue="method", marker="o", data=df)
    plt.title("Percentage of Acceptably Homogeneous Regions (H1 < 1)")
    plt.xlabel("Number of Clusters (k)")
    plt.ylabel("Percentage of Regions (%)")
    plt.legend(title="Method")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "pct_acceptable_comparison.png"), dpi=300)
    plt.close()
    
    # Plot number of regions by homogeneity category
    # Reshape data for stacked bar chart
    melted_df = pd.melt(
        df, 
        id_vars=["k", "method"], 
        value_vars=["H1_acceptable", "H1_possibly", "H1_heterogeneous"],
        var_name="category", 
        value_name="count"
    )
    
    # Map category names to more readable labels
    category_map = {
        "H1_acceptable": "Acceptably Homogeneous (H1 < 1)",
        "H1_possibly": "Possibly Heterogeneous (1 ≤ H1 < 2)",
        "H1_heterogeneous": "Definitely Heterogeneous (H1 ≥ 2)"
    }
    melted_df["category"] = melted_df["category"].map(category_map)
    
    # Create stacked bar chart
    plt.figure(figsize=(14, 10))
    g = sns.catplot(
        x="k", 
        y="count", 
        hue="category", 
        col="method", 
        data=melted_df,
        kind="bar", 
        height=6, 
        aspect=1.5,
        palette=["green", "orange", "red"]
    )
    g.set_axis_labels("Number of Clusters (k)", "Number of Regions")
    g.set_titles("{col_name}")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "homogeneity_categories.png"), dpi=300)
    plt.close()

def create_summary_table(results_data, output_dir):
    """
    Create a summary table of the best k values for each method.
    
    Parameters:
    -----------
    results_data : dict
        Dictionary mapping method names to DataFrames with overall heterogeneity data
    output_dir : str
        Directory to save the summary table
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize summary data
    summary_data = []
    
    # Process each method
    for method, df in results_data.items():
        if df is None or df.empty:
            continue
        
        # Find k with highest percentage of acceptable regions
        best_pct_row = df.loc[df["pct_acceptable"].idxmax()]
        best_pct_k = best_pct_row["k"]
        best_pct = best_pct_row["pct_acceptable"]
        
        # Find k with lowest average H1
        best_h1_row = df.loc[df["avg_H1"].idxmin()]
        best_h1_k = best_h1_row["k"]
        best_h1 = best_h1_row["avg_H1"]
        
        # Add to summary data
        summary_data.append({
            "method": method,
            "best_k_by_pct": best_pct_k,
            "best_pct_acceptable": best_pct,
            "best_k_by_h1": best_h1_k,
            "best_avg_h1": best_h1
        })
    
    # Create DataFrame and save to CSV
    summary_df = pd.DataFrame(summary_data)
    summary_path = os.path.join(output_dir, "best_k_summary.csv")
    summary_df.to_csv(summary_path, index=False)
    logger.info(f"Best k summary saved to {summary_path}")
    
    # Create a more detailed table with top 3 k values for each method
    detailed_data = []
    
    for method, df in results_data.items():
        if df is None or df.empty:
            continue
        
        # Get top 3 k values by percentage of acceptable regions
        top_pct = df.sort_values("pct_acceptable", ascending=False).head(3)
        
        for i, row in top_pct.iterrows():
            detailed_data.append({
                "method": method,
                "k": row["k"],
                "pct_acceptable": row["pct_acceptable"],
                "avg_H1": row["avg_H1"],
                "avg_H2": row["avg_H2"],
                "avg_H3": row["avg_H3"],
                "num_regions": row["num_regions"],
                "rank_by_pct": i + 1
            })
    
    # Create DataFrame and save to CSV
    detailed_df = pd.DataFrame(detailed_data)
    detailed_path = os.path.join(output_dir, "top_k_detailed.csv")
    detailed_df.to_csv(detailed_path, index=False)
    logger.info(f"Detailed top k summary saved to {detailed_path}")

def main():
    """Compare heterogeneity results across different regionalization methods."""
    # Parse command line arguments
    args = parse_args()
    
    # Setup logging
    log_level = getattr(logging, args.log_level)
    setup_logging(log_level=log_level)
    
    logger.info("Starting heterogeneity results comparison")
    logger.info(f"Results directory: {args.results_dir}")
    logger.info(f"Output directory: {args.output_dir}")
    
    try:
        # Find heterogeneity result directories
        result_dirs = find_heterogeneity_results(args.results_dir)
        logger.info(f"Found {len(result_dirs)} heterogeneity result directories")
        
        if not result_dirs:
            logger.warning("No heterogeneity result directories found. Run heterogeneity analysis first.")
            sys.exit(1)
        
        # Create output directory if it doesn't exist
        os.makedirs(args.output_dir, exist_ok=True)
        
        # Load heterogeneity summary data for each method
        heterogeneity_data = {}
        overall_data = {}
        
        for method, dir_path in result_dirs.items():
            # Load heterogeneity summary
            df = load_heterogeneity_summary(dir_path)
            if df is not None:
                heterogeneity_data[method] = df
            
            # Load overall summary
            overall_df = load_overall_summary(dir_path)
            if overall_df is not None:
                overall_data[method] = overall_df
        
        # Compare heterogeneity statistics
        if heterogeneity_data:
            logger.info("Comparing heterogeneity statistics")
            compare_heterogeneity_statistics(heterogeneity_data, args.output_dir)
        else:
            logger.warning("No valid heterogeneity data found for comparison")
        
        # Compare overall statistics
        if overall_data:
            logger.info("Comparing overall statistics")
            compare_overall_statistics(overall_data, args.output_dir)
            
            # Create summary table of best k values
            logger.info("Creating summary table of best k values")
            create_summary_table(overall_data, args.output_dir)
        else:
            logger.warning("No valid overall data found for comparison")
        
        logger.info("Heterogeneity results comparison completed")
        
    except Exception as e:
        logger.error(f"Error in heterogeneity results comparison: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
