#!/usr/bin/env python3
"""
Test script to verify that day-specific means are working correctly in weather typing.
"""

import numpy as np
import xarray as xr
import pandas as pd
import tempfile
import os
import sys
import logging

# Add src to path
sys.path.insert(0, 'src')

from features.data_preprocessing import AtmosClusterer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data_with_seasonal_cycle():
    """
    Create synthetic atmospheric data with a strong seasonal cycle.
    This will help us verify that day-specific means preserve the seasonal cycle
    while overall means would remove it.
    """
    # Create 3 years of daily data (no leap years for simplicity)
    n_years = 3
    n_days_per_year = 365
    n_total_days = n_years * n_days_per_year
    
    # Create spatial grid
    ny, nx = 10, 15
    
    # Create time coordinate
    start_date = '2020-01-01'  # Non-leap year
    dates = pd.date_range(start_date, periods=n_total_days, freq='D')
    
    # Create synthetic data with strong seasonal cycle
    # Base pattern: sinusoidal seasonal cycle + random noise
    day_of_year = np.tile(np.arange(1, 366), n_years)
    
    # Create seasonal cycle (stronger in winter, weaker in summer)
    seasonal_amplitude = 20.0  # Strong seasonal signal
    seasonal_cycle = seasonal_amplitude * np.sin(2 * np.pi * (day_of_year - 1) / 365.0)
    
    # Add spatial variation
    lat_effect = np.linspace(-5, 5, ny)[:, np.newaxis]  # Latitude gradient
    lon_effect = np.linspace(-3, 3, nx)[np.newaxis, :]  # Longitude gradient
    spatial_pattern = lat_effect + lon_effect
    
    # Create the full data array
    data = np.zeros((n_total_days, ny, nx))
    for t in range(n_total_days):
        # Seasonal cycle + spatial pattern + random noise
        data[t] = (seasonal_cycle[t] + spatial_pattern + 
                  np.random.normal(0, 2, (ny, nx)))
    
    # Create xarray dataset
    ds = xr.Dataset(
        {
            'temperature': (['time', 'latitude', 'longitude'], data)
        },
        coords={
            'time': dates,
            'latitude': np.linspace(30, 50, ny),
            'longitude': np.linspace(-120, -100, nx)
        }
    )
    
    return ds, seasonal_cycle, spatial_pattern

def test_day_specific_vs_overall_means():
    """
    Test that day-specific means preserve seasonal cycle while overall means remove it.
    """
    logger.info("Creating test data with seasonal cycle...")
    ds, true_seasonal_cycle, spatial_pattern = create_test_data_with_seasonal_cycle()
    
    # Create temporary files
    with tempfile.TemporaryDirectory() as temp_dir:
        # Save test data
        data_path = os.path.join(temp_dir, 'test_data.nc')
        ds.to_netcdf(data_path)
        
        # Create lat/lon file (same as data file for simplicity)
        latlon_path = data_path
        
        # Test 1: AtmosClusterer with day-specific means (our new implementation)
        logger.info("Testing day-specific means...")
        clusterer_daily = AtmosClusterer(
            latlon_path=latlon_path,
            variable_paths={'temp': data_path},
            variable_names={'temp': 'temperature'},
            levels={'temp': None}
        )
        
        # Get the processed data
        processed_daily = clusterer_daily.preprocess()
        
        # Test 2: Manually compute overall mean approach for comparison
        logger.info("Computing overall mean approach for comparison...")
        raw_data = ds['temperature'].values
        overall_mean = np.mean(raw_data, axis=0)
        overall_anomalies = raw_data - overall_mean[np.newaxis, :, :]
        
        # Apply same latitude weighting as AtmosClusterer
        lat_weights = np.cos(np.deg2rad(ds.latitude.values))
        lat_weights = lat_weights / np.mean(lat_weights)
        lat_weights_grid = lat_weights[:, np.newaxis]
        
        overall_weighted = overall_anomalies * lat_weights_grid[np.newaxis, :, :]
        overall_flattened = overall_weighted.reshape(overall_weighted.shape[0], -1)
        overall_std = np.nanstd(overall_flattened, axis=0, ddof=1)
        overall_standardized = overall_flattened / overall_std
        
        # Compare the results
        logger.info("Analyzing results...")
        
        # Check if seasonal cycle is preserved in day-specific approach
        # Extract a single grid point for analysis
        mid_point = (5, 7)  # Middle of the grid
        
        # For day-specific approach, extract the same grid point
        grid_idx = mid_point[0] * ds.sizes['longitude'] + mid_point[1]
        daily_timeseries = processed_daily[:, grid_idx]
        overall_timeseries = overall_standardized[:, grid_idx]
        
        # Compute seasonal averages for each approach
        n_years = 3
        daily_seasonal = np.zeros(365)
        overall_seasonal = np.zeros(365)
        
        for day in range(365):
            day_indices = [day + year * 365 for year in range(n_years)]
            daily_seasonal[day] = np.mean(daily_timeseries[day_indices])
            overall_seasonal[day] = np.mean(overall_timeseries[day_indices])
        
        # Calculate seasonal cycle strength (standard deviation of daily means)
        daily_cycle_strength = np.std(daily_seasonal)
        overall_cycle_strength = np.std(overall_seasonal)
        
        logger.info(f"Day-specific approach seasonal cycle strength: {daily_cycle_strength:.4f}")
        logger.info(f"Overall mean approach seasonal cycle strength: {overall_cycle_strength:.4f}")
        logger.info(f"Ratio (daily/overall): {daily_cycle_strength/overall_cycle_strength:.2f}")
        
        # The day-specific approach should preserve more seasonal cycle
        if daily_cycle_strength > overall_cycle_strength * 1.5:
            logger.info("✅ SUCCESS: Day-specific means preserve seasonal cycle!")
            logger.info("   Day-specific approach retains significantly more seasonal variation")
            return True
        else:
            logger.warning("❌ ISSUE: Day-specific means not working as expected")
            logger.warning("   Seasonal cycle strength should be higher with day-specific means")
            return False

def test_leap_year_handling():
    """
    Test that leap years are handled correctly.
    """
    logger.info("Testing leap year handling...")
    
    # Create data with leap year
    dates_with_leap = pd.date_range('2020-01-01', '2020-12-31', freq='D')  # 2020 is leap year
    ny, nx = 5, 8
    
    # Create simple data
    data = np.random.normal(0, 1, (len(dates_with_leap), ny, nx))
    
    ds = xr.Dataset(
        {'var': (['time', 'lat', 'lon'], data)},
        coords={
            'time': dates_with_leap,
            'lat': np.linspace(30, 40, ny),
            'lon': np.linspace(-110, -100, nx)
        }
    )
    
    with tempfile.TemporaryDirectory() as temp_dir:
        data_path = os.path.join(temp_dir, 'leap_test.nc')
        ds.to_netcdf(data_path)
        
        try:
            clusterer = AtmosClusterer(
                latlon_path=data_path,
                variable_paths={'var': data_path},
                variable_names={'var': 'var'},
                levels={'var': None}
            )
            
            processed = clusterer.preprocess()
            logger.info(f"✅ Leap year data processed successfully: {processed.shape}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Leap year handling failed: {e}")
            return False

if __name__ == '__main__':
    logger.info("Starting day-specific means verification tests...")
    
    # Run tests
    test1_passed = test_day_specific_vs_overall_means()
    test2_passed = test_leap_year_handling()
    
    if test1_passed and test2_passed:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("Day-specific means are working correctly.")
        logger.info("You should now see different weather typing results compared to overall means.")
    else:
        logger.error("\n❌ SOME TESTS FAILED!")
        logger.error("There may be issues with the day-specific mean implementation.")
    
    sys.exit(0 if (test1_passed and test2_passed) else 1)
