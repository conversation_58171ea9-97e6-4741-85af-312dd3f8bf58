2025-07-11 09:36:14,346 - __main__ - INFO - run_heterogeneity_analysis.py:525 - Starting heterogeneity analysis
2025-07-11 09:36:14,346 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-11 09:36:14,346 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Cluster data: regionalization_w_WT_3_hierarchical/CI_results_hierarchical.nc
2025-07-11 09:36:14,346 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Output directory: heterogeneity_results/regionalization_w_WT_3_hierarchical
2025-07-11 09:36:14,346 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Maximum clusters: 125
2025-07-11 09:36:14,346 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Number of simulations: 50
2025-07-11 09:36:14,346 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Remap clusters: True
2025-07-11 09:36:14,347 - __main__ - INFO - run_heterogeneity_analysis.py:540 - Remapping clusters from 1D to 2D grid format
2025-07-11 09:36:14,348 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_3_hierarchical/CI_results_hierarchical.nc
2025-07-11 09:36:14,348 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-11 09:36:15,148 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-11 09:36:15,215 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-11 09:36:15,218 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-11 09:36:15,220 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-11 09:36:15,223 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-11 09:36:15,224 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-11 09:36:15,225 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-11 09:36:15,227 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-11 09:36:15,258 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-11 09:36:15,328 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-11 09:36:15,330 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-11 09:36:15,333 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-11 09:36:15,334 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-11 09:36:15,335 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-11 09:36:15,337 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-11 09:36:15,339 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-11 09:36:15,341 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-11 09:36:15,343 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-11 09:36:15,345 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-11 09:36:15,349 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-11 09:36:15,349 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-11 09:36:15,352 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-11 09:36:15,352 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-11 09:36:15,352 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-11 09:36:15,354 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-11 09:36:15,356 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-11 09:36:15,357 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-11 09:36:15,359 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-11 09:36:15,360 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-11 09:36:15,362 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-11 09:36:15,364 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-11 09:36:15,366 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-11 09:36:15,368 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-11 09:36:15,370 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-11 09:36:15,371 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-11 09:36:15,373 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-11 09:36:15,374 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-11 09:36:15,376 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-11 09:36:15,378 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-11 09:36:15,378 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-11 09:36:15,379 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-11 09:36:15,379 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-07-11 09:36:15,380 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-11 09:36:15,385 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-11 09:36:15,385 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-11 09:36:15,387 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-11 09:36:15,387 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-11 09:36:15,390 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-11 09:36:15,390 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-11 09:36:15,393 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-11 09:36:15,394 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-11 09:36:15,397 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-11 09:36:15,397 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-11 09:36:15,400 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-11 09:36:15,400 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-11 09:36:15,403 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-11 09:36:15,403 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-11 09:36:15,406 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-11 09:36:15,406 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-11 09:36:15,409 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-11 09:36:15,409 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-11 09:36:15,413 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-11 09:36:15,413 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-11 09:36:15,415 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-11 09:36:15,415 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-11 09:36:15,419 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-11 09:36:15,419 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-11 09:36:15,422 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-11 09:36:15,422 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-11 09:36:15,425 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-11 09:36:15,425 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-11 09:36:15,428 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-11 09:36:15,429 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-11 09:36:15,431 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-11 09:36:15,431 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-11 09:36:15,434 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-11 09:36:15,434 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-11 09:36:15,438 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-11 09:36:15,438 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-11 09:36:15,440 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-11 09:36:15,440 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-11 09:36:15,444 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-11 09:36:15,444 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-11 09:36:15,447 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-11 09:36:15,447 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-11 09:36:15,450 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-11 09:36:15,450 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-11 09:36:15,453 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-11 09:36:15,453 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-11 09:36:15,456 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-11 09:36:15,456 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-11 09:36:15,459 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-11 09:36:15,459 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-11 09:36:15,462 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-11 09:36:15,462 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-11 09:36:15,465 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-11 09:36:15,465 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-11 09:36:15,468 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-11 09:36:15,468 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-11 09:36:15,471 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-11 09:36:15,472 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-11 09:36:15,475 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-11 09:36:15,475 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-11 09:36:15,478 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-11 09:36:15,478 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-11 09:36:15,481 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-11 09:36:15,481 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-11 09:36:15,484 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-11 09:36:15,484 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-11 09:36:15,487 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-11 09:36:15,487 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-11 09:36:15,490 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-11 09:36:15,490 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-11 09:36:15,493 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-11 09:36:15,493 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-11 09:36:15,496 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-11 09:36:15,496 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-11 09:36:15,499 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-11 09:36:15,499 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-11 09:36:15,502 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-11 09:36:15,502 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-11 09:36:15,505 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-11 09:36:15,505 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-11 09:36:15,508 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-11 09:36:15,508 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-11 09:36:15,511 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-11 09:36:15,511 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-11 09:36:15,514 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-11 09:36:15,514 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-11 09:36:15,517 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-11 09:36:15,517 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-11 09:36:15,520 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-11 09:36:15,520 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-11 09:36:15,523 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-11 09:36:15,523 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-11 09:36:15,526 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-11 09:36:15,526 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-11 09:36:15,529 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-11 09:36:15,529 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-11 09:36:15,532 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-11 09:36:15,532 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-11 09:36:15,535 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-11 09:36:15,535 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-11 09:36:15,538 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-11 09:36:15,538 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-11 09:36:15,541 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-11 09:36:15,541 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-11 09:36:15,544 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-11 09:36:15,544 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-11 09:36:15,547 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-11 09:36:15,547 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-11 09:36:15,550 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-11 09:36:15,551 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-11 09:36:15,553 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-11 09:36:15,553 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-11 09:36:15,556 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-11 09:36:15,556 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-11 09:36:15,559 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-11 09:36:15,559 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-11 09:36:15,562 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-11 09:36:15,562 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-11 09:36:15,565 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-11 09:36:15,565 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-11 09:36:15,568 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-11 09:36:15,568 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-11 09:36:15,571 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-11 09:36:15,571 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-11 09:36:15,574 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-11 09:36:15,575 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-07-11 09:36:15,577 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-07-11 09:36:15,577 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-07-11 09:36:15,580 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-07-11 09:36:15,580 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-07-11 09:36:15,583 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-07-11 09:36:15,583 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-07-11 09:36:15,586 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-07-11 09:36:15,586 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-07-11 09:36:15,589 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-07-11 09:36:15,589 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-07-11 09:36:15,592 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-07-11 09:36:15,592 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-07-11 09:36:15,595 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-07-11 09:36:15,595 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-07-11 09:36:15,598 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-07-11 09:36:15,598 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-07-11 09:36:15,601 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-07-11 09:36:15,601 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-07-11 09:36:15,604 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-07-11 09:36:15,604 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-07-11 09:36:15,607 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-07-11 09:36:15,607 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-07-11 09:36:15,610 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-07-11 09:36:15,610 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-07-11 09:36:15,613 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-07-11 09:36:15,613 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-07-11 09:36:15,616 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-07-11 09:36:15,616 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-07-11 09:36:15,619 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-07-11 09:36:15,619 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-07-11 09:36:15,622 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-07-11 09:36:15,622 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-07-11 09:36:15,655 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-07-11 09:36:15,655 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-07-11 09:36:15,876 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-07-11 09:36:15,876 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-07-11 09:36:15,879 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-07-11 09:36:15,879 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-07-11 09:36:15,882 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-07-11 09:36:15,882 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-07-11 09:36:15,885 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-07-11 09:36:15,885 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-07-11 09:36:15,887 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-07-11 09:36:15,887 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-07-11 09:36:15,890 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-07-11 09:36:15,891 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-07-11 09:36:15,894 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-07-11 09:36:15,894 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-07-11 09:36:15,896 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-07-11 09:36:15,896 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-07-11 09:36:15,899 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-07-11 09:36:15,899 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-07-11 09:36:15,902 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-07-11 09:36:15,902 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-07-11 09:36:15,905 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-07-11 09:36:15,905 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-07-11 09:36:15,908 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-07-11 09:36:15,908 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-07-11 09:36:15,911 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-07-11 09:36:15,911 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-07-11 09:36:15,914 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-07-11 09:36:15,914 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-07-11 09:36:15,917 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-07-11 09:36:15,917 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-07-11 09:36:15,919 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-07-11 09:36:15,919 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-07-11 09:36:15,922 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-07-11 09:36:15,923 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-07-11 09:36:15,925 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-07-11 09:36:15,926 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-07-11 09:36:15,928 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-07-11 09:36:15,929 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-07-11 09:36:15,932 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-07-11 09:36:15,932 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-07-11 09:36:15,934 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-07-11 09:36:15,934 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-07-11 09:36:15,937 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-07-11 09:36:15,937 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-07-11 09:36:15,940 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-07-11 09:36:15,940 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-07-11 09:36:15,942 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-07-11 09:36:15,943 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-07-11 09:36:15,945 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-07-11 09:36:15,946 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-07-11 09:36:15,948 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-07-11 09:36:15,949 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-07-11 09:36:15,952 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-07-11 09:36:15,952 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-07-11 09:36:15,955 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-07-11 09:36:15,955 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-07-11 09:36:15,957 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-07-11 09:36:15,957 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-07-11 09:36:15,960 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-07-11 09:36:15,960 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-07-11 09:36:15,963 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-07-11 09:36:15,963 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-07-11 09:36:15,965 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-07-11 09:36:15,966 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-07-11 09:36:15,968 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-07-11 09:36:15,968 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-07-11 09:36:15,971 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-07-11 09:36:15,971 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-07-11 09:36:15,974 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-07-11 09:36:15,974 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-07-11 09:36:15,977 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-07-11 09:36:15,977 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-07-11 09:36:15,980 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-07-11 09:36:15,980 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-07-11 09:36:15,983 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-07-11 09:36:15,983 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-07-11 09:36:15,986 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-07-11 09:36:15,986 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-07-11 09:36:15,988 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-07-11 09:36:15,988 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-07-11 09:36:15,991 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-07-11 09:36:15,991 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-07-11 09:36:15,994 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-07-11 09:36:15,994 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-07-11 09:36:15,997 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-07-11 09:36:15,997 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-07-11 09:36:16,000 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-07-11 09:36:16,000 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-07-11 09:36:16,002 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-07-11 09:36:16,003 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_3_hierarchical/CI_results_hierarchical_remap.nc
2025-07-11 09:36:16,269 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-07-11 09:36:16,270 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-07-11 09:36:16,270 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-07-11 09:36:16,270 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_3_hierarchical/CI_results_hierarchical.nc
2025-07-11 09:36:16,270 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_3_hierarchical/CI_results_hierarchical_remap.nc
2025-07-11 09:36:16,270 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 125 variables
2025-07-11 09:36:16,270 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125
2025-07-11 09:36:16,270 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-07-11 09:36:16,273 - __main__ - INFO - run_heterogeneity_analysis.py:548 - Using remapped cluster file: regionalization_w_WT_3_hierarchical/CI_results_hierarchical_remap.nc
2025-07-11 09:36:16,273 - __main__ - INFO - run_heterogeneity_analysis.py:554 - Loading data
2025-07-11 09:36:16,273 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-11 09:36:16,279 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-07-11 09:36:24,317 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_3_hierarchical/CI_results_hierarchical_remap.nc
2025-07-11 09:36:24,731 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-07-11 09:36:24,731 - __main__ - INFO - run_heterogeneity_analysis.py:558 - Running heterogeneity analysis
2025-07-11 09:36:24,731 - __main__ - INFO - run_heterogeneity_analysis.py:562 - Processing k=2
2025-07-11 09:36:24,811 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1.0 with 25606 sites
2025-07-11 09:36:24,811 - __main__ - ERROR - run_heterogeneity_analysis.py:602 - Error in heterogeneity analysis: Cannot cast scalar from dtype('float64') to dtype('int64') according to the rule 'safe'
Traceback (most recent call last):
  File "_mt19937.pyx", line 178, in numpy.random._mt19937.MT19937._legacy_seeding
TypeError: 'numpy.float64' object cannot be interpreted as an integer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/lcrc/project/Hydro-model/sgev/GDO/test2/src/models/run_heterogeneity_analysis.py", line 581, in main
    result = run_heterogeneity_analysis_for_region(
  File "/lcrc/project/Hydro-model/sgev/GDO/test2/src/models/run_heterogeneity_analysis.py", line 217, in run_heterogeneity_analysis_for_region
    np.random.seed(seed + region_id)  # Different seed for each region
  File "mtrand.pyx", line 4789, in numpy.random.mtrand.seed
  File "mtrand.pyx", line 250, in numpy.random.mtrand.RandomState.seed
  File "_mt19937.pyx", line 166, in numpy.random._mt19937.MT19937._legacy_seeding
  File "_mt19937.pyx", line 186, in numpy.random._mt19937.MT19937._legacy_seeding
TypeError: Cannot cast scalar from dtype('float64') to dtype('int64') according to the rule 'safe'
