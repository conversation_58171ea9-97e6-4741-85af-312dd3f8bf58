#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cluster Remapping Module

This module provides functionality to remap cluster assignments from 1D format
back to the original 2D grid structure, preserving the spatial relationships
and handling masked/invalid regions appropriately.

The main function processes CI_results.nc files containing cluster assignments
and creates remapped versions with proper 2D grid structure.
"""

import os
import sys
import logging
import numpy as np
import xarray as xr
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union

# Add the project root to the path for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.features.standardize import VariableStandardizer

logger = logging.getLogger(__name__)


def setup_standardizer_for_remapping(stats_file_path: str = None,
                                     custom_paths: Dict[str, str] = None) -> VariableStandardizer:
    """
    Set up a VariableStandardizer instance for cluster remapping.

    This function creates the standardizer with the same configuration used
    during the original clustering process to ensure consistent mapping.

    Parameters:
    -----------
    stats_file_path : str, optional
        Path to the cluster_precip_stats.nc file. If None, uses default location.
    custom_paths : Dict[str, str], optional
        Custom paths for sample data files. If None, uses default paths.

    Returns:
    --------
    VariableStandardizer
        Configured standardizer instance ready for inverse transformation
    """
    # Default stats file path
    if stats_file_path is None:
        stats_file_path = "cluster_precip_stats.nc"
    
    if not os.path.exists(stats_file_path):
        raise FileNotFoundError(f"Statistics file not found: {stats_file_path}")
    
    logger.info(f"Loading statistics from: {stats_file_path}")
    stats_ds = xr.open_dataset(stats_file_path)
    
    # Extract variables (using first few values as in original code)
    mam = stats_ds["mean_annual_max"]
    mat = stats_ds["mean_annual_total"]
    mad = stats_ds["mean_all_days"]
    msm = stats_ds["mean_seasonal_max"]
    mst = stats_ds["mean_seasonal_total"]
    msd = stats_ds["mean_seasonal"]

    # Use a small subset for mask computation (as in original code)
    # Ensure we don't exceed available data
    max_years = min(mam.shape[0], 2)
    i = 2
    
    # Define paths and variables (same as original clustering setup)
    if custom_paths is None:
        # Default paths
        base_paths = {
            "elevation": "sample_data/elevation_CONUS_CCSM_grid.nc",
            "lat": "sample_data/WRF_CCSM_lat_lon.nc",
            "lon": "sample_data/WRF_CCSM_lat_lon.nc"
        }
    else:
        base_paths = custom_paths

    paths = {
        "elevation": base_paths.get("elevation", "sample_data/elevation_CONUS_CCSM_grid.nc"),
        "lat": base_paths.get("lat", "sample_data/WRF_CCSM_lat_lon.nc"),
        "lon": base_paths.get("lon", "sample_data/WRF_CCSM_lat_lon.nc"),
        "mam": mam[i][0:i],
        "mat": mat[i][0:i],
        "mad": mad[i][0:i],

        # Mean Seasonal Max
        "msm_djf": msm.sel(season="DJF")[i][0:i],
        "msm_mam": msm.sel(season="MAM")[i][0:i],
        "msm_jja": msm.sel(season="JJA")[i][0:i],
        "msm_son": msm.sel(season="SON")[i][0:i],

        # Mean Seasonal Total
        "mst_djf": mst.sel(season="DJF")[i][0:i],
        "mst_mam": mst.sel(season="MAM")[i][0:i],
        "mst_jja": mst.sel(season="JJA")[i][0:i],
        "mst_son": mst.sel(season="SON")[i][0:i],

        # Mean Seasonal (unspecified)
        "msd_djf": msd.sel(season="DJF")[i][0:i],
        "msd_mam": msd.sel(season="MAM")[i][0:i],
        "msd_jja": msd.sel(season="JJA")[i][0:i],
        "msd_son": msd.sel(season="SON")[i][0:i]
    }
    
    names = {
        "elevation": "interpolated",
        "lat": "latitude", 
        "lon": "longitude",
    }
    
    # Initialize and fit standardizer to establish mask and grid structure
    logger.info("Initializing variable standardizer for remapping")
    standardizer = VariableStandardizer(paths, names)
    
    # Fit the standardizer to establish the mask and original shape
    logger.info("Fitting standardizer to establish grid structure")
    standardizer.fit_transform()
    
    logger.info(f"Grid structure established: {standardizer.original_shape}")
    logger.info(f"Valid points: {np.sum(standardizer.mask)}")
    
    return standardizer


def remap_cluster_variable(cluster_data: np.ndarray, standardizer: VariableStandardizer, 
                          variable_name: str) -> Tuple[Optional[np.ndarray], str]:
    """
    Remap a single cluster variable from 1D to 2D grid format.
    
    Parameters:
    -----------
    cluster_data : np.ndarray
        1D cluster assignments
    standardizer : VariableStandardizer
        Fitted standardizer with mask and grid information
    variable_name : str
        Name of the variable being remapped (for logging)
        
    Returns:
    --------
    tuple
        (remapped_data, status_message) where remapped_data is None if remapping failed
    """
    try:
        # Check if data length matches expected valid points
        expected_length = np.sum(standardizer.mask)
        if len(cluster_data) != expected_length:
            return None, f"Data length mismatch: got {len(cluster_data)}, expected {expected_length}"
        
        # Perform inverse transformation
        remapped = standardizer.inverse_transform_to_grid(cluster_data)
        
        return remapped, "Success"
        
    except Exception as e:
        return None, f"Remapping failed: {str(e)}"


def remap_clusters_file(ci_results_path: str, stats_file_path: str = None, 
                       output_suffix: str = "_remap") -> str:
    """
    Remap all cluster variables in a CI_results.nc file from 1D to 2D grid format.
    
    This function:
    1. Loads the CI_results.nc file
    2. Sets up the standardizer with the same configuration used during clustering
    3. Remaps each k=N variable from 1D cluster assignments to 2D grid
    4. Saves the remapped data to a new file
    
    Parameters:
    -----------
    ci_results_path : str
        Path to the CI_results.nc file containing 1D cluster assignments
    stats_file_path : str, optional
        Path to the cluster_precip_stats.nc file. If None, uses default location.
    output_suffix : str, optional
        Suffix to add to the output filename (default: "_remap")
        
    Returns:
    --------
    str
        Path to the output file with remapped clusters
        
    Raises:
    -------
    FileNotFoundError
        If input files are not found
    ValueError
        If cluster data cannot be remapped
    """
    # Validate input file
    if not os.path.exists(ci_results_path):
        raise FileNotFoundError(f"CI results file not found: {ci_results_path}")
    
    logger.info(f"Starting cluster remapping for: {ci_results_path}")
    
    # Load cluster data
    logger.info("Loading cluster data")
    cluster_ds = xr.open_dataset(ci_results_path)
    
    # Set up standardizer for remapping
    standardizer = setup_standardizer_for_remapping(stats_file_path)
    
    # Find cluster variables (k=N format)
    cluster_vars = [var for var in cluster_ds.data_vars if var.startswith('k=')]
    
    if not cluster_vars:
        raise ValueError("No cluster variables found in the dataset (expected format: k=N)")
    
    logger.info(f"Found {len(cluster_vars)} cluster variables: {cluster_vars}")
    
    # Create output dataset with same structure
    output_ds = cluster_ds.copy()
    
    # Track remapping results
    successful_remaps = []
    failed_remaps = []
    
    # Remap each cluster variable
    for var_name in cluster_vars:
        logger.info(f"Remapping variable: {var_name}")
        
        cluster_data = cluster_ds[var_name].values
        
        # Handle different data shapes
        if cluster_data.ndim > 1:
            logger.warning(f"Variable {var_name} has {cluster_data.ndim}D data, flattening")
            cluster_data = cluster_data.flatten()
        
        # Attempt remapping
        remapped_data, status = remap_cluster_variable(cluster_data, standardizer, var_name)
        
        if remapped_data is not None:
            # Update the dataset with remapped 2D data
            # Create new coordinates if needed
            if 'lat' not in output_ds.coords or 'lon' not in output_ds.coords:
                # Use standardizer's grid information to create coordinates
                lat_size, lon_size = standardizer.original_shape
                output_ds = output_ds.assign_coords({
                    'lat': np.arange(lat_size),
                    'lon': np.arange(lon_size)
                })
            
            # Replace the variable with remapped 2D data
            output_ds[var_name] = (('lat', 'lon'), remapped_data)
            
            successful_remaps.append(var_name)
            logger.info(f"✓ Successfully remapped {var_name}")
        else:
            failed_remaps.append((var_name, status))
            logger.warning(f"✗ Failed to remap {var_name}: {status}")
    
    # Generate output filename
    input_path = Path(ci_results_path)
    output_filename = input_path.stem + output_suffix + input_path.suffix
    output_path = input_path.parent / output_filename
    
    # Save remapped dataset
    logger.info(f"Saving remapped clusters to: {output_path}")
    output_ds.to_netcdf(output_path)
    
    # Log summary
    logger.info("=" * 60)
    logger.info("CLUSTER REMAPPING SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Input file: {ci_results_path}")
    logger.info(f"Output file: {output_path}")
    logger.info(f"Successfully remapped: {len(successful_remaps)} variables")
    if successful_remaps:
        logger.info(f"  - {', '.join(successful_remaps)}")
    
    if failed_remaps:
        logger.info(f"Failed to remap: {len(failed_remaps)} variables")
        for var_name, reason in failed_remaps:
            logger.info(f"  - {var_name}: {reason}")
    
    logger.info("=" * 60)
    
    return str(output_path)
