2025-07-24 16:37:18,282 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-07-24 16:37:18,284 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-24 16:37:18,286 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_wo_WT_twovariables/CI_results_hierarchical.nc
2025-07-24 16:37:18,288 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_wo_WT_twovariables
2025-07-24 16:37:18,289 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 150
2025-07-24 16:37:18,291 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 200
2025-07-24 16:37:18,292 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-07-24 16:37:18,306 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-07-24 16:37:18,310 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_wo_WT_twovariables/CI_results_hierarchical.nc
2025-07-24 16:37:18,312 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-24 16:37:22,492 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-24 16:37:23,811 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-24 16:37:23,940 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-24 16:37:24,021 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-24 16:37:24,053 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-24 16:37:24,112 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-24 16:37:24,176 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-24 16:37:24,245 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-24 16:37:24,298 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-24 16:37:24,482 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-24 16:37:24,667 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-24 16:37:24,692 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-24 16:37:24,843 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-24 16:37:24,858 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-24 16:37:24,882 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-24 16:37:24,899 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-24 16:37:25,119 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-24 16:37:25,134 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-24 16:37:25,159 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-24 16:37:25,189 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-24 16:37:25,191 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-24 16:37:25,220 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-24 16:37:25,223 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-24 16:37:25,225 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-24 16:37:25,266 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-24 16:37:25,291 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-24 16:37:25,316 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-24 16:37:25,357 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-24 16:37:25,385 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-24 16:37:25,426 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-24 16:37:25,467 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-24 16:37:25,508 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-24 16:37:25,549 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-24 16:37:25,577 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-24 16:37:25,605 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-24 16:37:25,633 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-24 16:37:25,674 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-24 16:37:25,715 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-24 16:37:25,756 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-24 16:37:25,878 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-07-24 16:37:25,880 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-24 16:37:25,884 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-24 16:37:25,893 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 150 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125', 'k=126', 'k=127', 'k=128', 'k=129', 'k=130', 'k=131', 'k=132', 'k=133', 'k=134', 'k=135', 'k=136', 'k=137', 'k=138', 'k=139', 'k=140', 'k=141', 'k=142', 'k=143', 'k=144', 'k=145', 'k=146', 'k=147', 'k=148', 'k=149', 'k=150']
2025-07-24 16:37:25,904 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-24 16:37:25,960 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-24 16:37:25,962 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-24 16:37:25,995 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-24 16:37:25,997 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-24 16:37:26,030 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-24 16:37:26,032 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-24 16:37:26,065 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-24 16:37:26,067 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-24 16:37:26,100 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-24 16:37:26,102 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-24 16:37:26,141 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-24 16:37:26,142 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-24 16:37:26,186 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-24 16:37:26,188 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-24 16:37:26,232 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-24 16:37:26,234 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-24 16:37:26,271 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-24 16:37:26,273 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-24 16:37:26,316 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-24 16:37:26,318 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-24 16:37:26,362 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-24 16:37:26,364 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-24 16:37:26,401 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-24 16:37:26,403 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-24 16:37:26,447 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-24 16:37:26,448 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-24 16:37:26,492 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-24 16:37:26,494 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-24 16:37:26,537 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-24 16:37:26,539 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-24 16:37:26,583 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-24 16:37:26,584 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-24 16:37:26,621 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-24 16:37:26,623 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-24 16:37:26,667 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-24 16:37:26,668 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-24 16:37:26,712 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-24 16:37:26,714 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-24 16:37:26,757 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-24 16:37:26,759 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-24 16:37:26,803 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-24 16:37:26,804 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-24 16:37:26,841 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-24 16:37:26,843 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-24 16:37:26,886 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-24 16:37:27,033 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-24 16:37:27,246 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-24 16:37:27,248 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-24 16:37:27,285 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-24 16:37:27,287 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-24 16:37:27,330 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-24 16:37:27,332 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-24 16:37:27,375 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-24 16:37:27,377 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-24 16:37:27,420 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-24 16:37:27,422 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-24 16:37:27,465 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-24 16:37:27,467 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-24 16:37:27,504 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-24 16:37:27,505 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-24 16:37:27,548 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-24 16:37:27,550 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-24 16:37:27,593 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-24 16:37:27,595 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-24 16:37:27,631 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-24 16:37:27,633 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-24 16:37:27,676 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-24 16:37:27,678 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-24 16:37:27,721 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-24 16:37:27,723 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-24 16:37:27,766 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-24 16:37:27,768 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-24 16:37:27,811 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-24 16:37:27,812 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-24 16:37:27,849 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-24 16:37:27,851 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-24 16:37:27,894 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-24 16:37:27,896 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-24 16:37:27,939 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-24 16:37:27,941 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-24 16:37:27,977 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-24 16:37:27,979 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-24 16:37:28,022 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-24 16:37:28,024 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-24 16:37:28,066 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-24 16:37:28,068 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-24 16:37:28,111 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-24 16:37:28,113 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-24 16:37:28,156 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-24 16:37:28,158 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-24 16:37:28,193 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-24 16:37:28,195 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-24 16:37:28,238 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-24 16:37:28,240 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-24 16:37:28,282 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-24 16:37:28,284 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-24 16:37:28,320 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-24 16:37:28,322 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-24 16:37:28,365 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-24 16:37:28,366 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-24 16:37:28,409 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-24 16:37:28,411 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-24 16:37:28,453 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-24 16:37:28,455 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-24 16:37:28,498 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-24 16:37:28,499 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-24 16:37:28,535 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-24 16:37:28,537 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-24 16:37:28,579 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-24 16:37:28,581 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-24 16:37:28,624 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-24 16:37:28,625 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-24 16:37:28,661 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-24 16:37:28,663 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-24 16:37:28,705 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-24 16:37:28,707 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-24 16:37:28,750 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-24 16:37:28,751 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-24 16:37:28,794 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-24 16:37:28,796 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-24 16:37:28,838 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-24 16:37:28,839 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-24 16:37:28,875 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-24 16:37:28,877 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-24 16:37:28,919 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-24 16:37:28,921 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-07-24 16:37:28,963 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-07-24 16:37:28,965 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-07-24 16:37:29,001 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-07-24 16:37:29,002 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-07-24 16:37:29,045 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-07-24 16:37:29,046 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-07-24 16:37:29,089 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-07-24 16:37:29,090 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-07-24 16:37:29,133 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-07-24 16:37:29,134 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-07-24 16:37:29,176 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-07-24 16:37:29,178 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-07-24 16:37:29,213 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-07-24 16:37:29,215 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-07-24 16:37:29,257 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-07-24 16:37:29,259 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-07-24 16:37:29,301 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-07-24 16:37:29,303 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-07-24 16:37:29,338 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-07-24 16:37:29,340 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-07-24 16:37:29,382 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-07-24 16:37:29,384 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-07-24 16:37:29,426 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-07-24 16:37:29,427 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-07-24 16:37:29,469 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-07-24 16:37:29,471 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-07-24 16:37:29,513 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-07-24 16:37:29,515 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-07-24 16:37:29,550 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-07-24 16:37:29,552 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-07-24 16:37:29,593 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-07-24 16:37:29,595 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-07-24 16:37:29,637 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-07-24 16:37:29,639 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-07-24 16:37:29,674 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-07-24 16:37:29,676 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-07-24 16:37:29,717 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-07-24 16:37:29,719 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-07-24 16:37:29,761 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-07-24 16:37:29,763 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-07-24 16:37:29,804 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-07-24 16:37:29,806 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-07-24 16:37:29,848 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-07-24 16:37:29,849 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-07-24 16:37:29,884 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-07-24 16:37:29,886 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-07-24 16:37:29,928 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-07-24 16:37:29,930 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-07-24 16:37:29,972 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-07-24 16:37:29,973 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-07-24 16:37:30,008 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-07-24 16:37:30,010 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-07-24 16:37:30,052 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-07-24 16:37:30,053 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-07-24 16:37:30,095 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-07-24 16:37:30,097 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-07-24 16:37:30,138 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-07-24 16:37:30,140 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-07-24 16:37:30,182 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-07-24 16:37:30,183 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-07-24 16:37:30,218 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-07-24 16:37:30,220 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-07-24 16:37:30,261 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-07-24 16:37:30,263 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-07-24 16:37:30,304 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-07-24 16:37:30,306 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-07-24 16:37:30,347 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-07-24 16:37:30,349 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-07-24 16:37:30,390 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-07-24 16:37:30,392 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-07-24 16:37:30,433 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-07-24 16:37:30,435 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-07-24 16:37:30,476 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-07-24 16:37:30,478 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-07-24 16:37:30,519 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-07-24 16:37:30,521 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-07-24 16:37:30,555 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-07-24 16:37:30,557 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-07-24 16:37:30,598 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-07-24 16:37:30,600 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-07-24 16:37:30,641 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-07-24 16:37:30,643 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-07-24 16:37:30,684 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-07-24 16:37:30,686 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-07-24 16:37:30,727 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-07-24 16:37:30,728 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-07-24 16:37:30,763 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-07-24 16:37:30,765 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-07-24 16:37:30,805 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-07-24 16:37:30,807 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-07-24 16:37:30,848 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-07-24 16:37:30,850 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-07-24 16:37:30,884 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-07-24 16:37:30,886 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-07-24 16:37:30,927 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-07-24 16:37:30,928 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-07-24 16:37:30,969 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-07-24 16:37:30,971 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-07-24 16:37:31,012 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-07-24 16:37:31,014 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-07-24 16:37:31,055 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-07-24 16:37:31,056 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-07-24 16:37:31,091 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-07-24 16:37:31,092 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-07-24 16:37:31,133 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-07-24 16:37:31,135 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-07-24 16:37:31,176 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-07-24 16:37:31,177 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-07-24 16:37:31,211 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-07-24 16:37:31,213 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-07-24 16:37:31,254 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-07-24 16:37:31,256 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-07-24 16:37:31,296 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-07-24 16:37:31,298 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-07-24 16:37:31,339 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-07-24 16:37:31,341 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-07-24 16:37:31,381 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-07-24 16:37:31,383 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-07-24 16:37:31,417 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-07-24 16:37:31,419 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-07-24 16:37:31,460 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-07-24 16:37:31,461 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-07-24 16:37:31,502 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-07-24 16:37:31,504 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=126
2025-07-24 16:37:31,537 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=126
2025-07-24 16:37:31,539 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=127
2025-07-24 16:37:31,580 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=127
2025-07-24 16:37:31,582 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=128
2025-07-24 16:37:31,622 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=128
2025-07-24 16:37:31,624 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=129
2025-07-24 16:37:31,664 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=129
2025-07-24 16:37:31,666 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=130
2025-07-24 16:37:31,707 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=130
2025-07-24 16:37:31,709 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=131
2025-07-24 16:37:31,742 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=131
2025-07-24 16:37:31,744 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=132
2025-07-24 16:37:31,784 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=132
2025-07-24 16:37:31,786 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=133
2025-07-24 16:37:31,826 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=133
2025-07-24 16:37:31,828 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=134
2025-07-24 16:37:31,862 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=134
2025-07-24 16:37:31,863 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=135
2025-07-24 16:37:31,904 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=135
2025-07-24 16:37:31,906 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=136
2025-07-24 16:37:31,946 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=136
2025-07-24 16:37:31,948 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=137
2025-07-24 16:37:31,988 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=137
2025-07-24 16:37:31,990 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=138
2025-07-24 16:37:32,030 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=138
2025-07-24 16:37:32,032 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=139
2025-07-24 16:37:32,065 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=139
2025-07-24 16:37:32,067 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=140
2025-07-24 16:37:32,107 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=140
2025-07-24 16:37:32,109 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=141
2025-07-24 16:37:32,149 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=141
2025-07-24 16:37:32,151 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=142
2025-07-24 16:37:32,184 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=142
2025-07-24 16:37:32,186 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=143
2025-07-24 16:37:32,226 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=143
2025-07-24 16:37:32,228 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=144
2025-07-24 16:37:32,268 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=144
2025-07-24 16:37:32,270 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=145
2025-07-24 16:37:32,310 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=145
2025-07-24 16:37:32,311 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=146
2025-07-24 16:37:32,351 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=146
2025-07-24 16:37:32,353 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=147
2025-07-24 16:37:32,386 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=147
2025-07-24 16:37:32,388 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=148
2025-07-24 16:37:32,428 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=148
2025-07-24 16:37:32,430 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=149
2025-07-24 16:37:32,470 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=149
2025-07-24 16:37:32,472 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=150
2025-07-24 16:37:32,505 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=150
2025-07-24 16:37:32,507 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_wo_WT_twovariables/CI_results_hierarchical_remap.nc
2025-07-24 16:37:38,978 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-07-24 16:37:38,980 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-07-24 16:37:38,981 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-07-24 16:37:38,983 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_wo_WT_twovariables/CI_results_hierarchical.nc
2025-07-24 16:37:38,984 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_wo_WT_twovariables/CI_results_hierarchical_remap.nc
2025-07-24 16:37:38,985 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 150 variables
2025-07-24 16:37:38,987 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125, k=126, k=127, k=128, k=129, k=130, k=131, k=132, k=133, k=134, k=135, k=136, k=137, k=138, k=139, k=140, k=141, k=142, k=143, k=144, k=145, k=146, k=147, k=148, k=149, k=150
2025-07-24 16:37:38,988 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-07-24 16:37:39,016 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_wo_WT_twovariables/CI_results_hierarchical_remap.nc
2025-07-24 16:37:39,018 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-07-24 16:37:39,020 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-24 16:37:39,429 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-07-24 16:38:23,481 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_wo_WT_twovariables/CI_results_hierarchical_remap.nc
2025-07-24 16:38:24,684 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-07-24 16:38:24,686 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-07-24 16:38:24,688 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-07-24 16:38:24,725 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-07-24 16:38:25,835 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 23516 sites
2025-07-24 16:41:38,260 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=202.369, H2=88.934, H3=17.578
2025-07-24 16:41:39,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 28843 sites
2025-07-24 16:45:34,323 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=247.819, H2=8.773, H3=1.144
2025-07-24 16:45:34,902 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-07-24 16:45:34,904 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-07-24 16:45:34,925 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-07-24 16:45:36,054 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 23516 sites
2025-07-24 16:48:48,841 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=202.369, H2=88.934, H3=17.578
2025-07-24 16:48:49,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11381 sites
2025-07-24 16:50:27,921 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=100.432, H2=-0.241, H3=-2.729
2025-07-24 16:50:28,914 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 17462 sites
2025-07-24 16:52:55,049 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=96.878, H2=15.125, H3=5.080
2025-07-24 16:52:55,624 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-07-24 16:52:55,626 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-07-24 16:52:55,653 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-07-24 16:52:56,658 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 18415 sites
2025-07-24 16:55:30,027 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=147.341, H2=20.945, H3=6.125
2025-07-24 16:55:30,726 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5101 sites
2025-07-24 16:56:20,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=22.888, H2=1.047, H3=-37.700
2025-07-24 16:56:21,321 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 11381 sites
2025-07-24 16:57:59,990 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=88.815, H2=-4.420, H3=-6.469
2025-07-24 16:58:00,989 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 17462 sites
2025-07-24 17:00:26,891 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=65.675, H2=26.210, H3=2.381
2025-07-24 17:00:27,454 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-07-24 17:00:27,456 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-07-24 17:00:27,486 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-07-24 17:00:28,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 18415 sites
2025-07-24 17:03:02,221 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=147.341, H2=20.945, H3=6.125
2025-07-24 17:03:02,813 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 938 sites
2025-07-24 17:03:20,607 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=51.964, H2=-1.764, H3=0.155
2025-07-24 17:03:21,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4163 sites
2025-07-24 17:04:03,762 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-9.417, H2=-41.356, H3=-30.851
2025-07-24 17:04:04,593 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 11381 sites
2025-07-24 17:05:43,226 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=62.959, H2=-0.864, H3=-4.763
2025-07-24 17:05:44,220 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 17462 sites
2025-07-24 17:08:12,953 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=227.184, H2=7.008, H3=3.611
2025-07-24 17:08:13,523 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-07-24 17:08:13,524 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-07-24 17:08:13,553 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-07-24 17:08:14,555 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 18415 sites
2025-07-24 17:10:51,897 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=147.341, H2=20.945, H3=6.125
2025-07-24 17:10:52,486 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 938 sites
2025-07-24 17:11:09,864 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=51.964, H2=-1.764, H3=0.155
2025-07-24 17:11:10,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4163 sites
2025-07-24 17:11:53,079 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-9.417, H2=-41.356, H3=-30.851
2025-07-24 17:11:53,913 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 11381 sites
2025-07-24 17:13:33,180 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=62.959, H2=-0.864, H3=-4.763
2025-07-24 17:13:33,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8408 sites
2025-07-24 17:14:49,414 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=28.717, H2=-1.045, H3=-1.094
2025-07-24 17:14:50,203 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 9054 sites
2025-07-24 17:16:10,813 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=46.167, H2=9.442, H3=9.621
2025-07-24 17:16:11,364 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-07-24 17:16:11,366 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-07-24 17:16:11,391 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-07-24 17:16:12,396 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 18415 sites
2025-07-24 17:18:47,384 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=147.341, H2=20.945, H3=6.125
2025-07-24 17:18:47,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 938 sites
2025-07-24 17:19:05,694 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=51.964, H2=-1.764, H3=0.155
2025-07-24 17:19:06,338 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4163 sites
2025-07-24 17:19:49,053 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-9.417, H2=-41.356, H3=-30.851
2025-07-24 17:19:49,803 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8153 sites
2025-07-24 17:21:04,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=68.785, H2=-8.899, H3=-4.631
2025-07-24 17:21:04,726 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3228 sites
2025-07-24 17:21:39,822 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=45.432, H2=1.345, H3=0.615
2025-07-24 17:21:40,573 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8408 sites
2025-07-24 17:22:56,187 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.923, H2=0.386, H3=1.555
2025-07-24 17:22:56,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 9054 sites
2025-07-24 17:24:17,913 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=81.146, H2=24.829, H3=4.653
2025-07-24 17:24:18,461 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-07-24 17:24:18,463 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-07-24 17:24:18,488 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-07-24 17:24:19,187 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6416 sites
2025-07-24 17:25:19,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=4.150, H2=-6.747, H3=-2.941
2025-07-24 17:25:20,442 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11999 sites
2025-07-24 17:27:04,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.214, H2=8.800, H3=1.997
2025-07-24 17:27:05,310 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 938 sites
2025-07-24 17:27:23,245 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=5.331, H2=-2.731, H3=1.721
2025-07-24 17:27:23,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 4163 sites
2025-07-24 17:28:06,806 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-5.541, H2=-11.562, H3=-16.380
2025-07-24 17:28:07,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8153 sites
2025-07-24 17:29:21,509 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=72.618, H2=-6.094, H3=-9.562
2025-07-24 17:29:22,141 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3228 sites
2025-07-24 17:29:57,552 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=34.944, H2=1.963, H3=4.184
2025-07-24 17:29:58,311 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8408 sites
2025-07-24 17:31:13,828 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=19.610, H2=1.888, H3=0.004
2025-07-24 17:31:14,623 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 9054 sites
2025-07-24 17:32:35,397 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=34.067, H2=6.384, H3=4.475
2025-07-24 17:32:35,970 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-07-24 17:32:35,976 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-07-24 17:32:36,001 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-07-24 17:32:36,701 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6416 sites
2025-07-24 17:33:37,326 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=4.150, H2=-6.747, H3=-2.941
2025-07-24 17:33:38,201 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11999 sites
2025-07-24 17:35:22,446 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.214, H2=8.800, H3=1.997
2025-07-24 17:35:23,054 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 938 sites
2025-07-24 17:35:41,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=5.331, H2=-2.731, H3=1.721
2025-07-24 17:35:42,002 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 4163 sites
2025-07-24 17:36:24,601 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-5.541, H2=-11.562, H3=-16.380
2025-07-24 17:36:25,379 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8153 sites
2025-07-24 17:37:38,517 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=72.618, H2=-6.094, H3=-9.562
2025-07-24 17:37:39,166 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3228 sites
2025-07-24 17:38:14,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=34.944, H2=1.963, H3=4.184
2025-07-24 17:38:14,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8408 sites
2025-07-24 17:39:30,295 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=19.610, H2=1.888, H3=0.004
2025-07-24 17:39:30,905 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1532 sites
2025-07-24 17:39:53,054 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=24.368, H2=-2.863, H3=-4.982
2025-07-24 17:39:53,815 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 7522 sites
2025-07-24 17:41:02,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=42.262, H2=11.295, H3=8.340
2025-07-24 17:41:02,905 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-07-24 17:41:02,911 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-07-24 17:41:02,937 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-07-24 17:41:03,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6416 sites
2025-07-24 17:42:04,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=4.150, H2=-6.747, H3=-2.941
2025-07-24 17:42:05,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11999 sites
2025-07-24 17:43:48,445 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.214, H2=8.800, H3=1.997
2025-07-24 17:43:49,052 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 938 sites
2025-07-24 17:44:06,908 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=5.331, H2=-2.731, H3=1.721
2025-07-24 17:44:07,569 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 4163 sites
2025-07-24 17:44:50,370 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-5.541, H2=-11.562, H3=-16.380
2025-07-24 17:44:51,145 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8153 sites
2025-07-24 17:46:05,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=72.618, H2=-6.094, H3=-9.562
2025-07-24 17:46:05,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1742 sites
2025-07-24 17:46:29,759 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=28.925, H2=-1.484, H3=1.171
2025-07-24 17:46:30,357 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1486 sites
2025-07-24 17:46:51,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=46.635, H2=1.958, H3=-0.869
2025-07-24 17:46:52,499 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8408 sites
2025-07-24 17:48:08,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=12.144, H2=-0.084, H3=-0.797
2025-07-24 17:48:08,693 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1532 sites
2025-07-24 17:48:30,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=80.570, H2=-2.401, H3=-4.154
2025-07-24 17:48:31,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 7522 sites
2025-07-24 17:49:40,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=82.802, H2=6.272, H3=2.621
2025-07-24 17:49:41,084 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-07-24 17:49:41,087 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-07-24 17:49:41,114 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-07-24 17:49:41,819 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6416 sites
2025-07-24 17:50:42,366 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=4.150, H2=-6.747, H3=-2.941
2025-07-24 17:50:43,231 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11999 sites
2025-07-24 17:52:26,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.214, H2=8.800, H3=1.997
2025-07-24 17:52:27,192 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 938 sites
2025-07-24 17:52:44,462 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=5.331, H2=-2.731, H3=1.721
2025-07-24 17:52:45,039 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 728 sites
2025-07-24 17:53:00,713 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-9.605, H2=-46.036, H3=-20.284
2025-07-24 17:53:01,358 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3435 sites
2025-07-24 17:53:38,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-6.343, H2=-15.804, H3=-22.002
2025-07-24 17:53:38,817 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8153 sites
2025-07-24 17:54:53,191 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=35.161, H2=-1.551, H3=-24.297
2025-07-24 17:54:53,803 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1742 sites
2025-07-24 17:55:17,589 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=45.012, H2=-0.498, H3=2.214
2025-07-24 17:55:18,184 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1486 sites
2025-07-24 17:55:40,128 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=62.824, H2=2.242, H3=-3.085
2025-07-24 17:55:40,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8408 sites
2025-07-24 17:56:56,829 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=15.733, H2=-0.705, H3=-0.188
2025-07-24 17:56:57,429 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1532 sites
2025-07-24 17:57:19,608 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=76.518, H2=-6.748, H3=-6.950
2025-07-24 17:57:20,353 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 7522 sites
2025-07-24 17:58:29,449 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=99.259, H2=12.365, H3=10.410
2025-07-24 17:58:30,122 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-07-24 17:58:30,130 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-07-24 17:58:30,156 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-07-24 17:58:30,860 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6416 sites
2025-07-24 17:59:31,111 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=4.150, H2=-6.747, H3=-2.941
2025-07-24 17:59:31,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11999 sites
2025-07-24 18:01:15,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.214, H2=8.800, H3=1.997
2025-07-24 18:01:16,294 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 605 sites
2025-07-24 18:01:31,366 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.162, H2=1.703, H3=2.709
2025-07-24 18:01:31,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 333 sites
2025-07-24 18:01:44,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=7.423, H2=-15.557, H3=-1.859
2025-07-24 18:01:45,502 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 728 sites
2025-07-24 18:02:03,355 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-4.726, H2=-18.966, H3=-17.211
2025-07-24 18:02:04,006 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3435 sites
2025-07-24 18:02:40,783 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.414, H2=-15.554, H3=-21.847
2025-07-24 18:02:41,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8153 sites
2025-07-24 18:03:56,479 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=38.877, H2=-6.444, H3=-3.968
2025-07-24 18:03:57,096 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1742 sites
2025-07-24 18:04:20,774 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=69.364, H2=-1.244, H3=0.890
2025-07-24 18:04:21,376 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1486 sites
2025-07-24 18:04:43,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=107.537, H2=1.303, H3=-1.248
2025-07-24 18:04:43,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 8408 sites
2025-07-24 18:05:59,604 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.000, H2=-0.333, H3=-0.570
2025-07-24 18:06:00,216 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1532 sites
2025-07-24 18:06:22,178 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=66.998, H2=-3.827, H3=-1.736
2025-07-24 18:06:22,931 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 7522 sites
2025-07-24 18:07:31,379 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=63.825, H2=28.538, H3=7.365
2025-07-24 18:07:31,946 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-07-24 18:07:31,951 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-07-24 18:07:31,978 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-07-24 18:07:32,684 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6416 sites
2025-07-24 18:08:32,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=4.150, H2=-6.747, H3=-2.941
2025-07-24 18:08:33,774 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11999 sites
2025-07-24 18:10:18,553 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.214, H2=8.800, H3=1.997
2025-07-24 18:10:19,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 605 sites
2025-07-24 18:10:34,614 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.162, H2=1.703, H3=2.709
2025-07-24 18:10:35,182 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 333 sites
2025-07-24 18:10:48,657 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=7.423, H2=-15.557, H3=-1.859
2025-07-24 18:10:49,350 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 728 sites
2025-07-24 18:11:06,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-4.726, H2=-18.966, H3=-17.211
2025-07-24 18:11:06,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3435 sites
2025-07-24 18:11:44,435 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.414, H2=-15.554, H3=-21.847
2025-07-24 18:11:45,288 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8153 sites
2025-07-24 18:12:59,365 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=38.877, H2=-6.444, H3=-3.968
2025-07-24 18:13:00,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1742 sites
2025-07-24 18:13:24,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=69.364, H2=-1.244, H3=0.890
2025-07-24 18:13:24,962 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1486 sites
2025-07-24 18:13:47,267 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=107.537, H2=1.303, H3=-1.248
2025-07-24 18:13:48,291 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 8408 sites
2025-07-24 18:15:04,740 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.000, H2=-0.333, H3=-0.570
2025-07-24 18:15:05,524 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1532 sites
2025-07-24 18:15:27,823 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=66.998, H2=-3.827, H3=-1.736
2025-07-24 18:15:28,582 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3111 sites
2025-07-24 18:16:03,556 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=98.082, H2=-0.881, H3=-4.864
2025-07-24 18:16:04,342 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4411 sites
2025-07-24 18:16:50,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=60.564, H2=40.203, H3=9.517
2025-07-24 18:16:51,405 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-07-24 18:16:51,413 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-07-24 18:16:51,444 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-07-24 18:16:52,145 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6416 sites
2025-07-24 18:17:52,385 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=4.150, H2=-6.747, H3=-2.941
2025-07-24 18:17:53,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 11999 sites
2025-07-24 18:19:38,085 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=25.214, H2=8.800, H3=1.997
2025-07-24 18:19:38,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 605 sites
2025-07-24 18:19:53,586 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.162, H2=1.703, H3=2.709
2025-07-24 18:19:54,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 333 sites
2025-07-24 18:20:07,154 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=7.423, H2=-15.557, H3=-1.859
2025-07-24 18:20:07,711 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 728 sites
2025-07-24 18:20:23,499 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-4.726, H2=-18.966, H3=-17.211
2025-07-24 18:20:24,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3435 sites
2025-07-24 18:21:01,137 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.414, H2=-15.554, H3=-21.847
2025-07-24 18:21:01,766 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3344 sites
2025-07-24 18:21:37,960 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=25.031, H2=-0.713, H3=1.039
2025-07-24 18:21:38,623 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4809 sites
2025-07-24 18:22:26,430 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=21.902, H2=-4.034, H3=-12.449
2025-07-24 18:22:27,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1742 sites
2025-07-24 18:22:50,714 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=68.825, H2=-1.276, H3=2.993
2025-07-24 18:22:51,292 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1486 sites
2025-07-24 18:23:12,920 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=110.018, H2=3.428, H3=-4.287
2025-07-24 18:23:13,671 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 8408 sites
2025-07-24 18:24:29,406 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=115.526, H2=0.324, H3=2.080
2025-07-24 18:24:29,994 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1532 sites
2025-07-24 18:24:52,144 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=45.024, H2=-2.489, H3=-1.735
2025-07-24 18:24:52,760 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3111 sites
2025-07-24 18:25:27,202 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=13.266, H2=-1.124, H3=-1.308
2025-07-24 18:25:27,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4411 sites
2025-07-24 18:26:12,666 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=57.819, H2=7.503, H3=4.068
2025-07-24 18:26:13,203 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-07-24 18:26:13,206 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-07-24 18:26:13,237 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-07-24 18:26:13,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 6416 sites
2025-07-24 18:27:14,158 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=4.150, H2=-6.747, H3=-2.941
2025-07-24 18:27:14,865 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6468 sites
2025-07-24 18:28:15,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=4.624, H2=14.455, H3=7.815
2025-07-24 18:28:16,247 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5531 sites
2025-07-24 18:29:09,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=17.601, H2=14.760, H3=-1.152
2025-07-24 18:29:10,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 605 sites
2025-07-24 18:29:25,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=4.710, H2=-0.045, H3=-0.744
2025-07-24 18:29:25,644 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 333 sites
2025-07-24 18:29:38,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=10.592, H2=-10.470, H3=0.928
2025-07-24 18:29:39,102 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 728 sites
2025-07-24 18:29:55,034 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-8.995, H2=-34.736, H3=-32.068
2025-07-24 18:29:55,660 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3435 sites
2025-07-24 18:30:33,002 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-6.484, H2=-10.519, H3=-31.300
2025-07-24 18:30:33,623 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3344 sites
2025-07-24 18:31:09,721 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=37.665, H2=-8.422, H3=-1.874
2025-07-24 18:31:10,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4809 sites
2025-07-24 18:32:00,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=45.778, H2=-4.916, H3=-10.569
2025-07-24 18:32:00,648 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1742 sites
2025-07-24 18:32:24,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=111.571, H2=-2.559, H3=-3.792
2025-07-24 18:32:25,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1486 sites
2025-07-24 18:32:46,830 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=46.533, H2=5.130, H3=-0.500
2025-07-24 18:32:47,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 8408 sites
2025-07-24 18:34:03,662 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=28.335, H2=-0.524, H3=-1.081
2025-07-24 18:34:04,252 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1532 sites
2025-07-24 18:34:28,511 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=19.004, H2=-2.924, H3=-1.619
2025-07-24 18:34:29,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3111 sites
2025-07-24 18:35:03,633 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=9.416, H2=-1.772, H3=-1.293
2025-07-24 18:35:04,290 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4411 sites
2025-07-24 18:35:48,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=57.107, H2=14.222, H3=7.302
2025-07-24 18:35:49,396 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-07-24 18:35:49,398 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-07-24 18:35:49,429 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-07-24 18:35:50,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 18:36:18,163 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 18:36:18,819 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 18:37:04,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 18:37:05,450 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 18:38:05,675 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 18:38:06,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5531 sites
2025-07-24 18:38:59,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=19.071, H2=8.277, H3=-1.793
2025-07-24 18:39:00,156 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 605 sites
2025-07-24 18:39:15,079 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=5.554, H2=0.959, H3=1.146
2025-07-24 18:39:15,623 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 333 sites
2025-07-24 18:39:28,458 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.028, H2=-17.794, H3=1.598
2025-07-24 18:39:29,014 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 728 sites
2025-07-24 18:39:44,948 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-6.221, H2=-28.222, H3=-56.666
2025-07-24 18:39:45,567 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3435 sites
2025-07-24 18:40:22,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-11.462, H2=-55.590, H3=-37.247
2025-07-24 18:40:23,180 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3344 sites
2025-07-24 18:40:59,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.489, H2=-5.830, H3=-0.558
2025-07-24 18:41:00,066 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4809 sites
2025-07-24 18:41:47,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=28.081, H2=-4.194, H3=-3.942
2025-07-24 18:41:48,316 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1742 sites
2025-07-24 18:42:11,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=43.120, H2=-0.595, H3=1.037
2025-07-24 18:42:12,569 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1486 sites
2025-07-24 18:42:34,405 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=59.074, H2=2.218, H3=-0.836
2025-07-24 18:42:35,156 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 8408 sites
2025-07-24 18:43:50,880 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=17.107, H2=-0.808, H3=-1.085
2025-07-24 18:43:51,466 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1532 sites
2025-07-24 18:44:13,666 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=10.969, H2=-5.411, H3=-5.885
2025-07-24 18:44:14,282 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3111 sites
2025-07-24 18:44:51,021 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=36.275, H2=-0.709, H3=-1.016
2025-07-24 18:44:51,675 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4411 sites
2025-07-24 18:45:38,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=60.761, H2=23.049, H3=7.924
2025-07-24 18:45:38,853 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-07-24 18:45:38,855 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-07-24 18:45:38,886 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-07-24 18:45:39,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 18:46:07,479 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 18:46:08,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 18:46:52,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 18:46:52,835 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 18:47:53,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 18:47:53,962 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5531 sites
2025-07-24 18:48:47,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=19.071, H2=8.277, H3=-1.793
2025-07-24 18:48:47,761 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 605 sites
2025-07-24 18:49:02,702 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=5.554, H2=0.959, H3=1.146
2025-07-24 18:49:03,243 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 333 sites
2025-07-24 18:49:16,125 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.028, H2=-17.794, H3=1.598
2025-07-24 18:49:16,680 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 728 sites
2025-07-24 18:49:32,575 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-6.221, H2=-28.222, H3=-56.666
2025-07-24 18:49:33,201 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3435 sites
2025-07-24 18:50:10,285 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-11.462, H2=-55.590, H3=-37.247
2025-07-24 18:50:10,912 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3344 sites
2025-07-24 18:50:47,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.489, H2=-5.830, H3=-0.558
2025-07-24 18:50:47,941 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4809 sites
2025-07-24 18:51:35,654 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=28.081, H2=-4.194, H3=-3.942
2025-07-24 18:51:36,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1742 sites
2025-07-24 18:52:00,083 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=43.120, H2=-0.595, H3=1.037
2025-07-24 18:52:00,660 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1486 sites
2025-07-24 18:52:22,558 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=59.074, H2=2.218, H3=-0.836
2025-07-24 18:52:23,185 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3539 sites
2025-07-24 18:53:01,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=7.472, H2=7.032, H3=3.421
2025-07-24 18:53:01,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4869 sites
2025-07-24 18:53:49,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=8.150, H2=-4.067, H3=-1.948
2025-07-24 18:53:50,392 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1532 sites
2025-07-24 18:54:12,549 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=18.536, H2=-2.369, H3=-2.053
2025-07-24 18:54:13,167 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3111 sites
2025-07-24 18:54:47,583 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=11.311, H2=-3.237, H3=-2.583
2025-07-24 18:54:48,230 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4411 sites
2025-07-24 18:55:32,941 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=78.803, H2=12.933, H3=10.269
2025-07-24 18:55:33,482 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-07-24 18:55:33,484 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-07-24 18:55:33,521 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-07-24 18:55:34,113 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 18:56:00,406 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 18:56:01,061 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 18:56:45,345 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 18:56:46,054 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 18:57:47,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 18:57:47,618 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 18:58:02,260 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 18:58:02,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4967 sites
2025-07-24 18:58:52,186 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=40.134, H2=11.607, H3=-0.261
2025-07-24 18:58:52,745 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 605 sites
2025-07-24 18:59:07,689 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=7.993, H2=5.080, H3=1.338
2025-07-24 18:59:08,232 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 333 sites
2025-07-24 18:59:21,068 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.006, H2=-6.841, H3=0.318
2025-07-24 18:59:21,620 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 728 sites
2025-07-24 18:59:37,547 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-5.707, H2=-22.400, H3=-65.549
2025-07-24 18:59:38,170 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3435 sites
2025-07-24 19:00:15,265 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-6.144, H2=-65.522, H3=-37.818
2025-07-24 19:00:15,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3344 sites
2025-07-24 19:00:52,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=42.870, H2=-3.591, H3=-2.200
2025-07-24 19:00:52,813 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4809 sites
2025-07-24 19:01:40,475 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=29.777, H2=-3.002, H3=-10.893
2025-07-24 19:01:41,062 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1742 sites
2025-07-24 19:02:04,901 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=167.017, H2=-0.221, H3=0.293
2025-07-24 19:02:05,482 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1486 sites
2025-07-24 19:02:27,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=19.323, H2=3.508, H3=-0.492
2025-07-24 19:02:27,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3539 sites
2025-07-24 19:03:05,491 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=8.842, H2=2.430, H3=2.390
2025-07-24 19:03:06,158 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4869 sites
2025-07-24 19:03:54,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=14.685, H2=-8.093, H3=-2.429
2025-07-24 19:03:55,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1532 sites
2025-07-24 19:04:17,308 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=24.716, H2=-11.756, H3=-0.970
2025-07-24 19:04:17,921 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3111 sites
2025-07-24 19:04:52,239 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=17.593, H2=-0.879, H3=-1.186
2025-07-24 19:04:52,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 4411 sites
2025-07-24 19:05:37,268 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=33.550, H2=19.754, H3=6.203
2025-07-24 19:05:37,809 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-07-24 19:05:37,811 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-07-24 19:05:37,840 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-07-24 19:05:38,431 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 19:06:06,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 19:06:07,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 19:06:51,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 19:06:52,061 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 19:07:52,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 19:07:53,122 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 19:08:07,750 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 19:08:08,411 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4967 sites
2025-07-24 19:08:57,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=40.134, H2=11.607, H3=-0.261
2025-07-24 19:08:57,629 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 605 sites
2025-07-24 19:09:12,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=7.993, H2=5.080, H3=1.338
2025-07-24 19:09:13,094 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 333 sites
2025-07-24 19:09:25,881 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.006, H2=-6.841, H3=0.318
2025-07-24 19:09:26,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 728 sites
2025-07-24 19:09:42,313 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-5.707, H2=-22.400, H3=-65.549
2025-07-24 19:09:42,937 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3435 sites
2025-07-24 19:10:19,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-6.144, H2=-65.522, H3=-37.818
2025-07-24 19:10:20,372 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3344 sites
2025-07-24 19:10:56,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=42.870, H2=-3.591, H3=-2.200
2025-07-24 19:10:57,252 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4809 sites
2025-07-24 19:11:44,693 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=29.777, H2=-3.002, H3=-10.893
2025-07-24 19:11:45,281 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1742 sites
2025-07-24 19:12:08,953 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=167.017, H2=-0.221, H3=0.293
2025-07-24 19:12:09,528 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1486 sites
2025-07-24 19:12:31,383 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=19.323, H2=3.508, H3=-0.492
2025-07-24 19:12:32,008 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3539 sites
2025-07-24 19:13:09,683 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=8.842, H2=2.430, H3=2.390
2025-07-24 19:13:10,293 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2561 sites
2025-07-24 19:13:40,375 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-2.119, H2=-5.154, H3=-1.563
2025-07-24 19:13:40,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2308 sites
2025-07-24 19:14:09,223 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=16.217, H2=-14.791, H3=-3.616
2025-07-24 19:14:09,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1532 sites
2025-07-24 19:14:31,875 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=144.539, H2=-3.885, H3=-3.069
2025-07-24 19:14:32,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3111 sites
2025-07-24 19:15:06,934 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=8.535, H2=-1.547, H3=-2.046
2025-07-24 19:15:07,588 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 4411 sites
2025-07-24 19:15:51,992 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=105.278, H2=14.123, H3=15.844
2025-07-24 19:15:52,536 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-07-24 19:15:52,539 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-07-24 19:15:52,568 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-07-24 19:15:53,164 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 19:16:19,248 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 19:16:19,899 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 19:17:04,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 19:17:04,789 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 19:18:05,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 19:18:05,737 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 19:18:20,336 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 19:18:20,903 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1186 sites
2025-07-24 19:18:40,345 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.565, H2=-1.996, H3=-1.910
2025-07-24 19:18:40,978 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3781 sites
2025-07-24 19:19:20,377 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.838, H2=6.078, H3=1.251
2025-07-24 19:19:20,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 605 sites
2025-07-24 19:19:35,872 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.421, H2=1.294, H3=2.913
2025-07-24 19:19:36,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 333 sites
2025-07-24 19:19:49,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.091, H2=-9.295, H3=-0.245
2025-07-24 19:19:49,796 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 728 sites
2025-07-24 19:20:05,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-12.717, H2=-12.262, H3=-12.680
2025-07-24 19:20:06,315 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3435 sites
2025-07-24 19:20:43,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-13.214, H2=-54.969, H3=-30.629
2025-07-24 19:20:43,899 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3344 sites
2025-07-24 19:21:20,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=37.455, H2=-2.757, H3=1.680
2025-07-24 19:21:20,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4809 sites
2025-07-24 19:22:08,786 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=80.560, H2=-4.095, H3=-23.633
2025-07-24 19:22:09,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1742 sites
2025-07-24 19:22:33,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=30.749, H2=-0.436, H3=0.757
2025-07-24 19:22:33,820 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1486 sites
2025-07-24 19:22:55,779 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=14.328, H2=1.507, H3=-3.037
2025-07-24 19:22:56,406 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3539 sites
2025-07-24 19:23:34,217 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=22.603, H2=3.384, H3=2.913
2025-07-24 19:23:34,823 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2561 sites
2025-07-24 19:24:04,914 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-3.523, H2=-5.059, H3=-1.246
2025-07-24 19:24:05,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2308 sites
2025-07-24 19:24:33,755 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=13.775, H2=-5.840, H3=-3.463
2025-07-24 19:24:34,337 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1532 sites
2025-07-24 19:24:56,454 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=118.418, H2=-4.562, H3=-1.577
2025-07-24 19:24:57,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3111 sites
2025-07-24 19:25:31,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=108.614, H2=-0.627, H3=-2.539
2025-07-24 19:25:32,281 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 4411 sites
2025-07-24 19:26:16,819 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=37.346, H2=8.241, H3=4.331
2025-07-24 19:26:17,359 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-07-24 19:26:17,360 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-07-24 19:26:17,389 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-07-24 19:26:17,982 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 19:26:44,149 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 19:26:44,797 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 19:27:29,331 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 19:27:30,032 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 19:28:31,048 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 19:28:31,609 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 19:28:46,231 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 19:28:46,796 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1186 sites
2025-07-24 19:29:06,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.565, H2=-1.996, H3=-1.910
2025-07-24 19:29:06,876 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3781 sites
2025-07-24 19:29:46,582 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.838, H2=6.078, H3=1.251
2025-07-24 19:29:47,142 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 605 sites
2025-07-24 19:30:02,062 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.421, H2=1.294, H3=2.913
2025-07-24 19:30:02,607 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 333 sites
2025-07-24 19:30:15,450 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.091, H2=-9.295, H3=-0.245
2025-07-24 19:30:16,005 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 728 sites
2025-07-24 19:30:31,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-12.717, H2=-12.262, H3=-12.680
2025-07-24 19:30:32,605 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3435 sites
2025-07-24 19:31:09,511 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-13.214, H2=-54.969, H3=-30.629
2025-07-24 19:31:10,134 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3344 sites
2025-07-24 19:31:46,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=37.455, H2=-2.757, H3=1.680
2025-07-24 19:31:47,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4809 sites
2025-07-24 19:32:34,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=80.560, H2=-4.095, H3=-23.633
2025-07-24 19:32:35,578 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1742 sites
2025-07-24 19:32:59,410 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=30.749, H2=-0.436, H3=0.757
2025-07-24 19:32:59,973 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 836 sites
2025-07-24 19:33:16,723 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=12.556, H2=-3.229, H3=-0.625
2025-07-24 19:33:17,277 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 650 sites
2025-07-24 19:33:32,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=24.008, H2=3.275, H3=-2.927
2025-07-24 19:33:33,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3539 sites
2025-07-24 19:34:11,044 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=11.137, H2=8.896, H3=9.673
2025-07-24 19:34:11,653 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2561 sites
2025-07-24 19:34:41,931 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.835, H2=-1.720, H3=-1.832
2025-07-24 19:34:42,529 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2308 sites
2025-07-24 19:35:10,630 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=17.800, H2=-8.235, H3=-4.755
2025-07-24 19:35:11,208 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1532 sites
2025-07-24 19:35:33,350 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=106.348, H2=-6.901, H3=-1.923
2025-07-24 19:35:33,966 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3111 sites
2025-07-24 19:36:08,204 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=11.833, H2=-0.807, H3=-1.333
2025-07-24 19:36:08,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 4411 sites
2025-07-24 19:36:53,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=43.473, H2=11.824, H3=30.049
2025-07-24 19:36:53,677 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-07-24 19:36:53,679 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-07-24 19:36:53,708 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-07-24 19:36:54,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 19:37:20,365 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 19:37:21,019 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 19:38:05,169 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 19:38:05,873 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 19:39:06,599 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 19:39:07,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 19:39:21,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 19:39:22,354 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1186 sites
2025-07-24 19:39:41,742 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.565, H2=-1.996, H3=-1.910
2025-07-24 19:39:42,373 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3781 sites
2025-07-24 19:40:21,972 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.838, H2=6.078, H3=1.251
2025-07-24 19:40:22,524 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 605 sites
2025-07-24 19:40:37,382 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.421, H2=1.294, H3=2.913
2025-07-24 19:40:37,925 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 333 sites
2025-07-24 19:40:50,718 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.091, H2=-9.295, H3=-0.245
2025-07-24 19:40:51,275 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 728 sites
2025-07-24 19:41:07,112 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-12.717, H2=-12.262, H3=-12.680
2025-07-24 19:41:07,740 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3435 sites
2025-07-24 19:41:44,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-13.214, H2=-54.969, H3=-30.629
2025-07-24 19:41:45,247 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3344 sites
2025-07-24 19:42:21,580 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=37.455, H2=-2.757, H3=1.680
2025-07-24 19:42:22,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4809 sites
2025-07-24 19:43:10,055 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=80.560, H2=-4.095, H3=-23.633
2025-07-24 19:43:10,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1742 sites
2025-07-24 19:43:34,320 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=30.749, H2=-0.436, H3=0.757
2025-07-24 19:43:34,883 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 836 sites
2025-07-24 19:43:51,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=12.556, H2=-3.229, H3=-0.625
2025-07-24 19:43:52,120 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 650 sites
2025-07-24 19:44:07,399 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=24.008, H2=3.275, H3=-2.927
2025-07-24 19:44:08,029 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3539 sites
2025-07-24 19:44:45,640 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=11.137, H2=8.896, H3=9.673
2025-07-24 19:44:46,248 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2561 sites
2025-07-24 19:45:16,347 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.835, H2=-1.720, H3=-1.832
2025-07-24 19:45:16,947 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2308 sites
2025-07-24 19:45:45,041 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=17.800, H2=-8.235, H3=-4.755
2025-07-24 19:45:45,621 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1532 sites
2025-07-24 19:46:07,752 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=106.348, H2=-6.901, H3=-1.923
2025-07-24 19:46:08,369 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3111 sites
2025-07-24 19:46:42,879 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=11.833, H2=-0.807, H3=-1.333
2025-07-24 19:46:43,450 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1234 sites
2025-07-24 19:47:03,251 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=70.363, H2=-0.644, H3=2.253
2025-07-24 19:47:03,863 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3177 sites
2025-07-24 19:47:38,786 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=43.721, H2=43.556, H3=6.745
2025-07-24 19:47:39,330 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-07-24 19:47:39,332 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-07-24 19:47:39,361 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-07-24 19:47:39,960 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 19:48:06,033 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 19:48:06,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 19:48:51,297 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 19:48:52,001 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 19:49:52,896 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 19:49:53,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 19:50:08,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 19:50:08,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1186 sites
2025-07-24 19:50:28,165 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.565, H2=-1.996, H3=-1.910
2025-07-24 19:50:28,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3781 sites
2025-07-24 19:51:08,535 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.838, H2=6.078, H3=1.251
2025-07-24 19:51:09,088 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 605 sites
2025-07-24 19:51:24,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.421, H2=1.294, H3=2.913
2025-07-24 19:51:24,625 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 74 sites
2025-07-24 19:51:35,415 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.540, H2=-4.225, H3=-0.318
2025-07-24 19:51:35,955 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 259 sites
2025-07-24 19:51:48,229 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.937, H2=-10.161, H3=0.217
2025-07-24 19:51:48,780 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 728 sites
2025-07-24 19:52:04,693 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-7.071, H2=-22.165, H3=-19.837
2025-07-24 19:52:05,316 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3435 sites
2025-07-24 19:52:44,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-8.556, H2=-26.666, H3=-61.418
2025-07-24 19:52:45,118 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3344 sites
2025-07-24 19:53:21,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=41.574, H2=-1.208, H3=-2.956
2025-07-24 19:53:22,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4809 sites
2025-07-24 19:54:11,890 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=44.001, H2=-3.995, H3=-5.254
2025-07-24 19:54:12,480 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1742 sites
2025-07-24 19:54:37,715 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=23.387, H2=-1.697, H3=-1.080
2025-07-24 19:54:38,276 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 836 sites
2025-07-24 19:54:56,990 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=13.198, H2=-0.812, H3=3.481
2025-07-24 19:54:57,542 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 650 sites
2025-07-24 19:55:12,801 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=29.142, H2=6.409, H3=-2.390
2025-07-24 19:55:13,426 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3539 sites
2025-07-24 19:55:51,294 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=11.410, H2=3.476, H3=2.919
2025-07-24 19:55:51,898 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2561 sites
2025-07-24 19:56:22,182 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-0.433, H2=-2.451, H3=-1.626
2025-07-24 19:56:22,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2308 sites
2025-07-24 19:56:51,290 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=41.729, H2=-5.439, H3=-15.151
2025-07-24 19:56:51,866 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1532 sites
2025-07-24 19:57:14,068 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=22.348, H2=-4.470, H3=-4.212
2025-07-24 19:57:14,684 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3111 sites
2025-07-24 19:57:49,153 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=18.104, H2=-0.633, H3=-2.990
2025-07-24 19:57:49,722 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1234 sites
2025-07-24 19:58:09,613 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=12.852, H2=0.917, H3=2.092
2025-07-24 19:58:10,234 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3177 sites
2025-07-24 19:58:45,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=10.105, H2=17.434, H3=8.190
2025-07-24 19:58:45,785 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-07-24 19:58:45,787 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-07-24 19:58:45,816 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-07-24 19:58:46,409 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 19:59:12,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 19:59:13,339 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 19:59:57,469 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 19:59:58,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 20:00:58,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 20:00:59,514 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 20:01:14,130 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 20:01:14,696 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1186 sites
2025-07-24 20:01:34,029 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.565, H2=-1.996, H3=-1.910
2025-07-24 20:01:34,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3781 sites
2025-07-24 20:02:14,226 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.838, H2=6.078, H3=1.251
2025-07-24 20:02:14,788 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 605 sites
2025-07-24 20:02:29,726 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.421, H2=1.294, H3=2.913
2025-07-24 20:02:30,267 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 74 sites
2025-07-24 20:02:41,056 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.540, H2=-4.225, H3=-0.318
2025-07-24 20:02:41,599 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 259 sites
2025-07-24 20:02:53,854 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.937, H2=-10.161, H3=0.217
2025-07-24 20:02:54,409 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 728 sites
2025-07-24 20:03:10,421 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-7.071, H2=-22.165, H3=-19.837
2025-07-24 20:03:11,044 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3435 sites
2025-07-24 20:03:48,082 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-8.556, H2=-26.666, H3=-61.418
2025-07-24 20:03:48,708 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3344 sites
2025-07-24 20:04:24,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=41.574, H2=-1.208, H3=-2.956
2025-07-24 20:04:25,457 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4809 sites
2025-07-24 20:05:13,527 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=44.001, H2=-3.995, H3=-5.254
2025-07-24 20:05:14,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 410 sites
2025-07-24 20:05:27,478 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=0.027, H2=-0.395, H3=-3.946
2025-07-24 20:05:28,050 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1332 sites
2025-07-24 20:05:48,570 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=14.899, H2=-4.604, H3=-2.915
2025-07-24 20:05:49,130 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 836 sites
2025-07-24 20:06:05,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=16.914, H2=-2.848, H3=0.703
2025-07-24 20:06:06,375 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 650 sites
2025-07-24 20:06:21,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=21.038, H2=4.313, H3=-3.033
2025-07-24 20:06:22,281 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3539 sites
2025-07-24 20:07:00,147 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=7.143, H2=12.214, H3=7.304
2025-07-24 20:07:00,754 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2561 sites
2025-07-24 20:07:30,815 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-1.602, H2=-1.876, H3=-2.169
2025-07-24 20:07:31,415 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2308 sites
2025-07-24 20:07:59,562 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=8.725, H2=-5.524, H3=-2.891
2025-07-24 20:08:00,144 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1532 sites
2025-07-24 20:08:22,294 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=31.074, H2=-9.474, H3=-1.950
2025-07-24 20:08:22,909 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3111 sites
2025-07-24 20:08:57,180 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=20.086, H2=0.406, H3=-1.746
2025-07-24 20:08:57,750 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1234 sites
2025-07-24 20:09:17,820 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=48.304, H2=-0.166, H3=2.200
2025-07-24 20:09:18,435 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 3177 sites
2025-07-24 20:09:53,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=34.435, H2=142.968, H3=18.011
2025-07-24 20:09:53,877 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-07-24 20:09:53,879 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-07-24 20:09:53,906 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-07-24 20:09:54,499 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 20:10:20,820 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 20:10:21,471 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 20:11:06,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 20:11:06,922 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 20:12:07,964 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 20:12:08,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 20:12:23,191 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 20:12:23,763 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1186 sites
2025-07-24 20:12:43,201 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.565, H2=-1.996, H3=-1.910
2025-07-24 20:12:43,833 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3781 sites
2025-07-24 20:13:23,606 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.838, H2=6.078, H3=1.251
2025-07-24 20:13:24,155 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 228 sites
2025-07-24 20:13:36,183 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=0.551, H2=3.421, H3=-1.446
2025-07-24 20:13:36,729 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 377 sites
2025-07-24 20:13:49,867 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.239, H2=-5.483, H3=1.929
2025-07-24 20:13:50,404 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 74 sites
2025-07-24 20:14:01,209 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=2.244, H2=-7.506, H3=-0.220
2025-07-24 20:14:01,748 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 259 sites
2025-07-24 20:14:14,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.837, H2=-5.130, H3=-1.550
2025-07-24 20:14:14,628 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 728 sites
2025-07-24 20:14:30,822 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-5.284, H2=-17.838, H3=-14.950
2025-07-24 20:14:31,451 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3435 sites
2025-07-24 20:15:08,686 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-27.586, H2=-13.348, H3=-30.083
2025-07-24 20:15:09,310 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3344 sites
2025-07-24 20:15:45,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=23.890, H2=-1.828, H3=-0.208
2025-07-24 20:15:46,448 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4809 sites
2025-07-24 20:16:34,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=22.694, H2=-2.252, H3=-2.998
2025-07-24 20:16:34,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 410 sites
2025-07-24 20:16:48,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.596, H2=0.845, H3=-0.069
2025-07-24 20:16:48,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1332 sites
2025-07-24 20:17:09,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=16.648, H2=-11.792, H3=-1.632
2025-07-24 20:17:09,835 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 836 sites
2025-07-24 20:17:26,559 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=22.506, H2=-2.333, H3=0.412
2025-07-24 20:17:27,113 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 650 sites
2025-07-24 20:17:42,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=27.061, H2=5.683, H3=-1.236
2025-07-24 20:17:43,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3539 sites
2025-07-24 20:18:20,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=22.123, H2=3.627, H3=4.222
2025-07-24 20:18:21,574 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2561 sites
2025-07-24 20:18:51,904 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-0.277, H2=-1.322, H3=-1.554
2025-07-24 20:18:52,504 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2308 sites
2025-07-24 20:19:20,801 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=21.786, H2=-15.159, H3=-3.084
2025-07-24 20:19:21,385 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1532 sites
2025-07-24 20:19:43,578 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=20.213, H2=-5.048, H3=-1.678
2025-07-24 20:19:44,194 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3111 sites
2025-07-24 20:20:18,824 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=9.024, H2=-1.092, H3=-2.408
2025-07-24 20:20:19,396 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1234 sites
2025-07-24 20:20:39,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=13.624, H2=-0.807, H3=7.832
2025-07-24 20:20:39,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 3177 sites
2025-07-24 20:21:14,651 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=33.575, H2=11.518, H3=8.124
2025-07-24 20:21:15,194 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-07-24 20:21:15,196 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-07-24 20:21:15,220 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-07-24 20:21:15,815 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 20:21:41,993 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 20:21:42,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4372 sites
2025-07-24 20:22:27,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-1.426, H2=-5.100, H3=-8.559
2025-07-24 20:22:27,770 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6468 sites
2025-07-24 20:23:28,689 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.558, H2=11.770, H3=3.497
2025-07-24 20:23:29,250 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 564 sites
2025-07-24 20:23:43,858 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-2.452, H2=12.300, H3=-3.674
2025-07-24 20:23:44,428 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1186 sites
2025-07-24 20:24:03,898 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.565, H2=-1.996, H3=-1.910
2025-07-24 20:24:04,529 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3781 sites
2025-07-24 20:24:44,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.838, H2=6.078, H3=1.251
2025-07-24 20:24:44,784 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 228 sites
2025-07-24 20:24:56,822 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=0.551, H2=3.421, H3=-1.446
2025-07-24 20:24:57,370 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 377 sites
2025-07-24 20:25:10,527 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.239, H2=-5.483, H3=1.929
2025-07-24 20:25:11,063 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 74 sites
2025-07-24 20:25:21,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=2.244, H2=-7.506, H3=-0.220
2025-07-24 20:25:22,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 259 sites
2025-07-24 20:25:34,765 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.837, H2=-5.130, H3=-1.550
2025-07-24 20:25:35,320 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 728 sites
2025-07-24 20:25:51,281 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-5.284, H2=-17.838, H3=-14.950
2025-07-24 20:25:51,859 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1593 sites
2025-07-24 20:26:14,485 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-19.582, H2=-9.918, H3=-9.391
2025-07-24 20:26:15,072 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1842 sites
2025-07-24 20:26:39,602 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-14.698, H2=-19.090, H3=-21.423
2025-07-24 20:26:40,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3344 sites
2025-07-24 20:27:16,690 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=17.430, H2=-2.563, H3=-0.708
2025-07-24 20:27:17,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4809 sites
2025-07-24 20:28:05,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=49.634, H2=-4.296, H3=-4.038
2025-07-24 20:28:05,632 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 410 sites
2025-07-24 20:28:19,046 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.357, H2=2.772, H3=1.167
2025-07-24 20:28:19,619 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1332 sites
2025-07-24 20:28:40,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=17.545, H2=-7.995, H3=-4.646
2025-07-24 20:28:40,876 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 836 sites
2025-07-24 20:28:57,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=24.441, H2=-1.506, H3=0.853
2025-07-24 20:28:58,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 650 sites
2025-07-24 20:29:13,428 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=30.671, H2=12.000, H3=-1.823
2025-07-24 20:29:14,050 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3539 sites
2025-07-24 20:29:51,964 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=6.678, H2=3.262, H3=2.094
2025-07-24 20:29:52,570 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2561 sites
2025-07-24 20:30:22,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-0.818, H2=-2.362, H3=-1.442
2025-07-24 20:30:23,359 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2308 sites
2025-07-24 20:30:51,548 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=22.372, H2=-31.572, H3=-6.039
2025-07-24 20:30:52,130 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1532 sites
2025-07-24 20:31:14,171 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=46.536, H2=-6.270, H3=-3.873
2025-07-24 20:31:14,791 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 3111 sites
2025-07-24 20:31:49,252 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=21.769, H2=-2.910, H3=-0.637
2025-07-24 20:31:49,821 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1234 sites
2025-07-24 20:32:09,625 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=17.753, H2=-1.025, H3=7.985
2025-07-24 20:32:10,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 3177 sites
2025-07-24 20:32:45,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=28.488, H2=17.243, H3=6.741
2025-07-24 20:32:45,542 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-07-24 20:32:45,544 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-07-24 20:32:45,572 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-07-24 20:32:46,165 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 20:33:12,188 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 20:33:12,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 20:33:34,083 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 20:33:34,696 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 20:34:07,639 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 20:34:08,341 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6468 sites
2025-07-24 20:35:08,865 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.139, H2=6.584, H3=4.824
2025-07-24 20:35:09,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 564 sites
2025-07-24 20:35:24,001 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-1.854, H2=5.788, H3=-2.847
2025-07-24 20:35:24,567 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1186 sites
2025-07-24 20:35:43,950 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-1.376, H2=-5.629, H3=-0.631
2025-07-24 20:35:44,587 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3781 sites
2025-07-24 20:36:24,462 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.585, H2=6.510, H3=1.304
2025-07-24 20:36:25,009 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 228 sites
2025-07-24 20:36:36,926 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.741, H2=5.916, H3=-9.266
2025-07-24 20:36:37,473 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 377 sites
2025-07-24 20:36:50,619 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.329, H2=-1.023, H3=3.303
2025-07-24 20:36:51,156 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 74 sites
2025-07-24 20:37:02,017 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.851, H2=-3.674, H3=-0.349
2025-07-24 20:37:02,562 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 259 sites
2025-07-24 20:37:14,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.902, H2=-5.722, H3=-1.237
2025-07-24 20:37:15,368 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 728 sites
2025-07-24 20:37:31,232 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-8.347, H2=-12.017, H3=-14.888
2025-07-24 20:37:31,810 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1593 sites
2025-07-24 20:37:54,346 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-7.107, H2=-9.337, H3=-11.903
2025-07-24 20:37:54,930 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1842 sites
2025-07-24 20:38:19,373 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-8.133, H2=-25.616, H3=-51.159
2025-07-24 20:38:19,999 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3344 sites
2025-07-24 20:38:56,353 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=83.396, H2=-1.517, H3=-0.220
2025-07-24 20:38:57,025 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4809 sites
2025-07-24 20:39:44,667 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=20.996, H2=-3.857, H3=-5.184
2025-07-24 20:39:45,225 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 410 sites
2025-07-24 20:39:58,632 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.284, H2=0.655, H3=-1.779
2025-07-24 20:39:59,198 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1332 sites
2025-07-24 20:40:19,776 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=36.968, H2=-5.486, H3=-0.956
2025-07-24 20:40:20,339 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 836 sites
2025-07-24 20:40:37,039 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=31.405, H2=-4.238, H3=0.815
2025-07-24 20:40:37,592 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 650 sites
2025-07-24 20:40:52,902 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=27.772, H2=6.185, H3=-8.760
2025-07-24 20:40:53,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3539 sites
2025-07-24 20:41:31,283 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=24.512, H2=9.696, H3=6.486
2025-07-24 20:41:31,888 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2561 sites
2025-07-24 20:42:02,154 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-1.082, H2=-2.410, H3=-3.436
2025-07-24 20:42:02,754 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2308 sites
2025-07-24 20:42:30,911 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=18.094, H2=-4.073, H3=-2.700
2025-07-24 20:42:31,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1532 sites
2025-07-24 20:42:53,611 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=20.297, H2=-7.842, H3=-1.039
2025-07-24 20:42:54,229 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 3111 sites
2025-07-24 20:43:28,837 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=35.739, H2=-1.448, H3=-0.816
2025-07-24 20:43:29,407 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1234 sites
2025-07-24 20:43:49,186 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=18.631, H2=-0.397, H3=21.577
2025-07-24 20:43:49,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 3177 sites
2025-07-24 20:44:24,822 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=14.906, H2=5.948, H3=6.026
2025-07-24 20:44:25,362 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-07-24 20:44:25,364 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-07-24 20:44:25,393 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-07-24 20:44:25,989 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 20:44:52,122 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 20:44:52,702 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 20:45:14,032 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 20:45:14,645 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 20:45:47,742 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 20:45:48,444 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6468 sites
2025-07-24 20:46:49,559 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.139, H2=6.584, H3=4.824
2025-07-24 20:46:50,118 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 564 sites
2025-07-24 20:47:04,751 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-1.854, H2=5.788, H3=-2.847
2025-07-24 20:47:05,319 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1186 sites
2025-07-24 20:47:24,803 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-1.376, H2=-5.629, H3=-0.631
2025-07-24 20:47:25,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3781 sites
2025-07-24 20:48:05,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.585, H2=6.510, H3=1.304
2025-07-24 20:48:05,651 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 228 sites
2025-07-24 20:48:17,695 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.741, H2=5.916, H3=-9.266
2025-07-24 20:48:18,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 377 sites
2025-07-24 20:48:31,431 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.329, H2=-1.023, H3=3.303
2025-07-24 20:48:31,969 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 74 sites
2025-07-24 20:48:42,806 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.851, H2=-3.674, H3=-0.349
2025-07-24 20:48:43,344 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 259 sites
2025-07-24 20:48:55,889 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.902, H2=-5.722, H3=-1.237
2025-07-24 20:48:56,427 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 103 sites
2025-07-24 20:49:07,054 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 12: L-Moments invalid
2025-07-24 20:49:07,608 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 625 sites
2025-07-24 20:49:22,682 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-4.615, H2=-12.851, H3=-10.543
2025-07-24 20:49:23,262 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1593 sites
2025-07-24 20:49:45,826 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-10.452, H2=-16.240, H3=-32.205
2025-07-24 20:49:46,411 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1842 sites
2025-07-24 20:50:10,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-21.578, H2=-18.288, H3=-43.112
2025-07-24 20:50:11,531 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3344 sites
2025-07-24 20:50:47,846 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=17.126, H2=-6.861, H3=-0.575
2025-07-24 20:50:48,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4809 sites
2025-07-24 20:51:38,091 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.982, H2=-2.182, H3=-5.192
2025-07-24 20:51:38,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 410 sites
2025-07-24 20:51:52,045 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.283, H2=3.405, H3=0.180
2025-07-24 20:51:52,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1332 sites
2025-07-24 20:52:13,190 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=28.950, H2=-10.195, H3=-2.322
2025-07-24 20:52:13,752 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 836 sites
2025-07-24 20:52:30,430 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=23.434, H2=-0.929, H3=-0.866
2025-07-24 20:52:30,984 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 650 sites
2025-07-24 20:52:46,231 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=73.018, H2=2.806, H3=-3.001
2025-07-24 20:52:46,858 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3539 sites
2025-07-24 20:53:24,469 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=31.043, H2=34.461, H3=4.097
2025-07-24 20:53:25,078 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2561 sites
2025-07-24 20:53:55,059 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-0.276, H2=-1.762, H3=-1.425
2025-07-24 20:53:55,653 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2308 sites
2025-07-24 20:54:23,728 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=9.490, H2=-13.350, H3=-4.626
2025-07-24 20:54:24,305 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1532 sites
2025-07-24 20:54:46,331 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=31.447, H2=-3.784, H3=-1.384
2025-07-24 20:54:46,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 3111 sites
2025-07-24 20:55:21,204 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=15.337, H2=-2.118, H3=-1.516
2025-07-24 20:55:21,779 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1234 sites
2025-07-24 20:55:41,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=20.173, H2=0.577, H3=4.221
2025-07-24 20:55:42,217 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 3177 sites
2025-07-24 20:56:16,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=33.538, H2=48.179, H3=11.390
2025-07-24 20:56:17,526 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-07-24 20:56:17,528 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-07-24 20:56:17,557 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-07-24 20:56:18,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 20:56:44,197 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 20:56:44,772 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 20:57:06,160 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 20:57:06,776 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 20:57:39,872 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 20:57:40,576 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6468 sites
2025-07-24 20:58:40,834 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.139, H2=6.584, H3=4.824
2025-07-24 20:58:41,395 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 564 sites
2025-07-24 20:58:55,960 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-1.854, H2=5.788, H3=-2.847
2025-07-24 20:58:56,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1186 sites
2025-07-24 20:59:15,934 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-1.376, H2=-5.629, H3=-0.631
2025-07-24 20:59:16,570 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3781 sites
2025-07-24 20:59:56,321 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.585, H2=6.510, H3=1.304
2025-07-24 20:59:56,869 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 228 sites
2025-07-24 21:00:08,842 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=0.741, H2=5.916, H3=-9.266
2025-07-24 21:00:09,390 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 377 sites
2025-07-24 21:00:22,509 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.329, H2=-1.023, H3=3.303
2025-07-24 21:00:23,046 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 74 sites
2025-07-24 21:00:33,854 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.851, H2=-3.674, H3=-0.349
2025-07-24 21:00:34,399 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 259 sites
2025-07-24 21:00:46,610 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.902, H2=-5.722, H3=-1.237
2025-07-24 21:00:47,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 103 sites
2025-07-24 21:00:57,532 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 12: L-Moments invalid
2025-07-24 21:00:58,085 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 625 sites
2025-07-24 21:01:13,208 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-4.615, H2=-12.851, H3=-10.543
2025-07-24 21:01:13,784 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1593 sites
2025-07-24 21:01:36,344 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-10.452, H2=-16.240, H3=-32.205
2025-07-24 21:01:36,929 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1842 sites
2025-07-24 21:02:01,447 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-21.578, H2=-18.288, H3=-43.112
2025-07-24 21:02:02,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3344 sites
2025-07-24 21:02:38,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=17.126, H2=-6.861, H3=-0.575
2025-07-24 21:02:39,117 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1176 sites
2025-07-24 21:02:58,437 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=1.020, H2=-2.929, H3=-5.212
2025-07-24 21:02:59,067 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3633 sites
2025-07-24 21:03:37,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=22.659, H2=-16.002, H3=-7.295
2025-07-24 21:03:38,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 410 sites
2025-07-24 21:03:51,559 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=3.501, H2=2.169, H3=-0.874
2025-07-24 21:03:52,128 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1332 sites
2025-07-24 21:04:12,571 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=11.484, H2=-6.604, H3=-3.400
2025-07-24 21:04:13,130 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 836 sites
2025-07-24 21:04:29,779 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=14.071, H2=-1.993, H3=0.368
2025-07-24 21:04:30,333 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 650 sites
2025-07-24 21:04:45,654 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=10.268, H2=3.934, H3=-1.547
2025-07-24 21:04:46,282 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3539 sites
2025-07-24 21:05:23,844 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=5.981, H2=12.369, H3=2.996
2025-07-24 21:05:24,451 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2561 sites
2025-07-24 21:05:54,445 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-0.744, H2=-3.267, H3=-1.133
2025-07-24 21:05:55,045 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2308 sites
2025-07-24 21:06:23,167 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=15.812, H2=-6.622, H3=-5.968
2025-07-24 21:06:23,746 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1532 sites
2025-07-24 21:06:45,811 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=23.481, H2=-2.882, H3=-4.341
2025-07-24 21:06:46,429 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 3111 sites
2025-07-24 21:07:20,914 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=9.184, H2=-0.464, H3=-1.797
2025-07-24 21:07:21,482 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1234 sites
2025-07-24 21:07:41,262 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=13.246, H2=-1.435, H3=1.910
2025-07-24 21:07:41,877 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 3177 sites
2025-07-24 21:08:16,802 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=33.491, H2=25.197, H3=30.646
2025-07-24 21:08:17,344 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-07-24 21:08:17,346 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-07-24 21:08:17,375 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-07-24 21:08:17,971 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 21:08:44,256 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 21:08:44,832 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 21:09:06,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 21:09:06,686 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 21:09:39,641 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 21:09:40,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2689 sites
2025-07-24 21:10:11,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=0.403, H2=-0.845, H3=-2.497
2025-07-24 21:10:11,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3779 sites
2025-07-24 21:10:51,457 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.115, H2=18.895, H3=9.196
2025-07-24 21:10:52,017 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 564 sites
2025-07-24 21:11:06,598 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.827, H2=13.928, H3=-1.020
2025-07-24 21:11:07,168 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1186 sites
2025-07-24 21:11:26,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.457, H2=-3.172, H3=-2.183
2025-07-24 21:11:27,197 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3781 sites
2025-07-24 21:12:07,121 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=32.254, H2=29.760, H3=-1.866
2025-07-24 21:12:07,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 228 sites
2025-07-24 21:12:19,721 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=0.685, H2=11.480, H3=-3.837
2025-07-24 21:12:20,266 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 377 sites
2025-07-24 21:12:33,417 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.816, H2=-5.300, H3=3.468
2025-07-24 21:12:33,957 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 74 sites
2025-07-24 21:12:44,741 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.521, H2=-9.815, H3=-0.220
2025-07-24 21:12:45,282 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 259 sites
2025-07-24 21:12:57,535 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.066, H2=-5.104, H3=-0.677
2025-07-24 21:12:58,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 103 sites
2025-07-24 21:13:08,476 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 13: L-Moments invalid
2025-07-24 21:13:09,029 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 625 sites
2025-07-24 21:13:24,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-15.865, H2=-19.684, H3=-53.243
2025-07-24 21:13:24,678 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1593 sites
2025-07-24 21:13:47,217 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-10.112, H2=-9.762, H3=-20.477
2025-07-24 21:13:47,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1842 sites
2025-07-24 21:14:12,285 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.126, H2=-21.566, H3=-17.417
2025-07-24 21:14:12,910 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3344 sites
2025-07-24 21:14:49,167 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.770, H2=-1.075, H3=-0.131
2025-07-24 21:14:49,738 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1176 sites
2025-07-24 21:15:09,005 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.009, H2=-0.683, H3=-0.929
2025-07-24 21:15:09,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3633 sites
2025-07-24 21:15:48,009 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=90.603, H2=-2.060, H3=-9.398
2025-07-24 21:15:48,558 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 410 sites
2025-07-24 21:16:01,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=14.554, H2=2.064, H3=-2.065
2025-07-24 21:16:02,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1332 sites
2025-07-24 21:16:23,030 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=59.215, H2=-5.445, H3=-2.468
2025-07-24 21:16:23,590 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 836 sites
2025-07-24 21:16:40,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=8.668, H2=-0.503, H3=0.914
2025-07-24 21:16:40,771 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 650 sites
2025-07-24 21:16:55,968 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=31.216, H2=5.710, H3=-3.144
2025-07-24 21:16:56,593 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 3539 sites
2025-07-24 21:17:34,386 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=12.453, H2=19.455, H3=12.219
2025-07-24 21:17:34,991 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2561 sites
2025-07-24 21:18:05,106 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.554, H2=-6.243, H3=-2.716
2025-07-24 21:18:05,708 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2308 sites
2025-07-24 21:18:33,920 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=12.321, H2=-6.295, H3=-3.676
2025-07-24 21:18:34,498 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1532 sites
2025-07-24 21:18:56,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=19.304, H2=-3.387, H3=-1.241
2025-07-24 21:18:57,288 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 3111 sites
2025-07-24 21:19:31,575 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=22.673, H2=-5.061, H3=-1.941
2025-07-24 21:19:32,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1234 sites
2025-07-24 21:19:51,911 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=26.321, H2=-0.293, H3=3.190
2025-07-24 21:19:52,533 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 3177 sites
2025-07-24 21:20:27,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=30.088, H2=17.902, H3=5.726
2025-07-24 21:20:28,021 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=30 has insufficient data
2025-07-24 21:20:28,023 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=31
2025-07-24 21:20:28,051 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31 -9223372036854775808]
2025-07-24 21:20:28,643 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 21:20:54,702 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 21:20:55,280 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 21:21:16,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 21:21:17,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 21:21:50,226 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 21:21:50,832 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2689 sites
2025-07-24 21:22:21,879 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=0.403, H2=-0.845, H3=-2.497
2025-07-24 21:22:22,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3779 sites
2025-07-24 21:23:02,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.115, H2=18.895, H3=9.196
2025-07-24 21:23:02,661 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 564 sites
2025-07-24 21:23:17,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.827, H2=13.928, H3=-1.020
2025-07-24 21:23:17,812 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1186 sites
2025-07-24 21:23:37,304 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.457, H2=-3.172, H3=-2.183
2025-07-24 21:23:37,934 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3781 sites
2025-07-24 21:24:17,664 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=32.254, H2=29.760, H3=-1.866
2025-07-24 21:24:18,209 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 228 sites
2025-07-24 21:24:30,218 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=0.685, H2=11.480, H3=-3.837
2025-07-24 21:24:30,764 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 377 sites
2025-07-24 21:24:43,929 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.816, H2=-5.300, H3=3.468
2025-07-24 21:24:44,469 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 74 sites
2025-07-24 21:24:55,262 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.521, H2=-9.815, H3=-0.220
2025-07-24 21:24:55,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 259 sites
2025-07-24 21:25:08,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.066, H2=-5.104, H3=-0.677
2025-07-24 21:25:08,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 103 sites
2025-07-24 21:25:19,103 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 13: L-Moments invalid
2025-07-24 21:25:19,653 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 625 sites
2025-07-24 21:25:34,711 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-15.865, H2=-19.684, H3=-53.243
2025-07-24 21:25:35,288 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1593 sites
2025-07-24 21:25:57,919 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-10.112, H2=-9.762, H3=-20.477
2025-07-24 21:25:58,505 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1842 sites
2025-07-24 21:26:23,153 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.126, H2=-21.566, H3=-17.417
2025-07-24 21:26:23,772 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3344 sites
2025-07-24 21:27:00,288 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.770, H2=-1.075, H3=-0.131
2025-07-24 21:27:00,860 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1176 sites
2025-07-24 21:27:20,254 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.009, H2=-0.683, H3=-0.929
2025-07-24 21:27:20,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3633 sites
2025-07-24 21:27:59,485 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=90.603, H2=-2.060, H3=-9.398
2025-07-24 21:28:00,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 410 sites
2025-07-24 21:28:13,454 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=14.554, H2=2.064, H3=-2.065
2025-07-24 21:28:14,023 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1332 sites
2025-07-24 21:28:34,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=59.215, H2=-5.445, H3=-2.468
2025-07-24 21:28:35,255 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 836 sites
2025-07-24 21:28:51,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=8.668, H2=-0.503, H3=0.914
2025-07-24 21:28:52,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 650 sites
2025-07-24 21:29:07,828 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=31.216, H2=5.710, H3=-3.144
2025-07-24 21:29:08,390 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 997 sites
2025-07-24 21:29:26,385 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-18.921, H2=-36.053, H3=-70.569
2025-07-24 21:29:26,986 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2542 sites
2025-07-24 21:29:56,941 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=4.137, H2=-0.310, H3=-2.588
2025-07-24 21:29:57,546 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2561 sites
2025-07-24 21:30:27,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-4.349, H2=-1.534, H3=-1.816
2025-07-24 21:30:28,310 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2308 sites
2025-07-24 21:30:56,376 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=16.143, H2=-4.656, H3=-2.605
2025-07-24 21:30:56,953 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1532 sites
2025-07-24 21:31:19,062 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=61.344, H2=-6.681, H3=-6.317
2025-07-24 21:31:19,678 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 3111 sites
2025-07-24 21:31:54,099 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=23.041, H2=-2.312, H3=-4.315
2025-07-24 21:31:54,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1234 sites
2025-07-24 21:32:14,495 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=65.918, H2=-0.544, H3=1.646
2025-07-24 21:32:15,111 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 3177 sites
2025-07-24 21:32:49,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=36.467, H2=27.428, H3=11.005
2025-07-24 21:32:50,500 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=31 has insufficient data
2025-07-24 21:32:50,503 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=32
2025-07-24 21:32:50,527 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32 -9223372036854775808]
2025-07-24 21:32:51,124 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 21:33:17,340 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 21:33:17,917 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 21:33:39,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 21:33:39,887 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 21:34:12,882 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 21:34:13,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2689 sites
2025-07-24 21:34:44,562 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=0.403, H2=-0.845, H3=-2.497
2025-07-24 21:34:45,196 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3779 sites
2025-07-24 21:35:24,849 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.115, H2=18.895, H3=9.196
2025-07-24 21:35:25,403 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 564 sites
2025-07-24 21:35:40,020 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.827, H2=13.928, H3=-1.020
2025-07-24 21:35:40,590 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1186 sites
2025-07-24 21:36:00,019 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.457, H2=-3.172, H3=-2.183
2025-07-24 21:36:00,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3781 sites
2025-07-24 21:36:40,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=32.254, H2=29.760, H3=-1.866
2025-07-24 21:36:41,109 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 228 sites
2025-07-24 21:36:53,114 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=0.685, H2=11.480, H3=-3.837
2025-07-24 21:36:53,660 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 377 sites
2025-07-24 21:37:06,909 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.816, H2=-5.300, H3=3.468
2025-07-24 21:37:07,446 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 74 sites
2025-07-24 21:37:18,249 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.521, H2=-9.815, H3=-0.220
2025-07-24 21:37:18,791 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 259 sites
2025-07-24 21:37:31,004 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.066, H2=-5.104, H3=-0.677
2025-07-24 21:37:31,542 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 103 sites
2025-07-24 21:37:41,955 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 13: L-Moments invalid
2025-07-24 21:37:42,510 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 625 sites
2025-07-24 21:37:57,653 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-15.865, H2=-19.684, H3=-53.243
2025-07-24 21:37:58,229 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1593 sites
2025-07-24 21:38:20,760 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-10.112, H2=-9.762, H3=-20.477
2025-07-24 21:38:21,342 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1842 sites
2025-07-24 21:38:45,811 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.126, H2=-21.566, H3=-17.417
2025-07-24 21:38:46,436 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3344 sites
2025-07-24 21:39:22,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.770, H2=-1.075, H3=-0.131
2025-07-24 21:39:23,200 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1176 sites
2025-07-24 21:39:42,524 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.009, H2=-0.683, H3=-0.929
2025-07-24 21:39:43,155 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3633 sites
2025-07-24 21:40:21,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=90.603, H2=-2.060, H3=-9.398
2025-07-24 21:40:22,275 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 410 sites
2025-07-24 21:40:35,673 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=14.554, H2=2.064, H3=-2.065
2025-07-24 21:40:36,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1332 sites
2025-07-24 21:40:56,930 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=59.215, H2=-5.445, H3=-2.468
2025-07-24 21:40:57,491 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 836 sites
2025-07-24 21:41:14,216 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=8.668, H2=-0.503, H3=0.914
2025-07-24 21:41:14,768 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 650 sites
2025-07-24 21:41:30,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=31.216, H2=5.710, H3=-3.144
2025-07-24 21:41:30,643 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 997 sites
2025-07-24 21:41:48,637 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-18.921, H2=-36.053, H3=-70.569
2025-07-24 21:41:49,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2542 sites
2025-07-24 21:42:19,232 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=4.137, H2=-0.310, H3=-2.588
2025-07-24 21:42:19,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2561 sites
2025-07-24 21:42:50,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-4.349, H2=-1.534, H3=-1.816
2025-07-24 21:42:50,943 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2308 sites
2025-07-24 21:43:19,249 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=16.143, H2=-4.656, H3=-2.605
2025-07-24 21:43:19,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1532 sites
2025-07-24 21:43:42,006 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=61.344, H2=-6.681, H3=-6.317
2025-07-24 21:43:42,578 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1394 sites
2025-07-24 21:44:03,763 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=0.312, H2=-4.371, H3=-4.996
2025-07-24 21:44:04,349 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1717 sites
2025-07-24 21:44:28,129 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=17.388, H2=1.664, H3=-0.616
2025-07-24 21:44:28,702 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1234 sites
2025-07-24 21:44:48,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=52.638, H2=1.190, H3=3.412
2025-07-24 21:44:49,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 3177 sites
2025-07-24 21:45:24,288 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=26.531, H2=11.389, H3=7.481
2025-07-24 21:45:24,824 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=32 has insufficient data
2025-07-24 21:45:24,827 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=33
2025-07-24 21:45:24,856 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
 -9223372036854775808]
2025-07-24 21:45:25,454 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 21:45:51,615 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 21:45:52,186 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 21:46:13,571 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 21:46:14,179 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 21:46:47,413 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 21:46:48,022 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2689 sites
2025-07-24 21:47:19,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=0.403, H2=-0.845, H3=-2.497
2025-07-24 21:47:19,769 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3779 sites
2025-07-24 21:47:59,423 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.115, H2=18.895, H3=9.196
2025-07-24 21:47:59,981 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 564 sites
2025-07-24 21:48:14,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.827, H2=13.928, H3=-1.020
2025-07-24 21:48:15,124 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1186 sites
2025-07-24 21:48:34,612 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.457, H2=-3.172, H3=-2.183
2025-07-24 21:48:35,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3781 sites
2025-07-24 21:49:14,834 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=32.254, H2=29.760, H3=-1.866
2025-07-24 21:49:15,387 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 228 sites
2025-07-24 21:49:27,344 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=0.685, H2=11.480, H3=-3.837
2025-07-24 21:49:27,891 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 377 sites
2025-07-24 21:49:41,044 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.816, H2=-5.300, H3=3.468
2025-07-24 21:49:41,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 74 sites
2025-07-24 21:49:52,392 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.521, H2=-9.815, H3=-0.220
2025-07-24 21:49:52,934 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 259 sites
2025-07-24 21:50:05,149 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.066, H2=-5.104, H3=-0.677
2025-07-24 21:50:05,687 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 103 sites
2025-07-24 21:50:16,098 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 13: L-Moments invalid
2025-07-24 21:50:16,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 625 sites
2025-07-24 21:50:31,728 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-15.865, H2=-19.684, H3=-53.243
2025-07-24 21:50:32,320 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1593 sites
2025-07-24 21:50:54,825 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-10.112, H2=-9.762, H3=-20.477
2025-07-24 21:50:55,410 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1842 sites
2025-07-24 21:51:19,907 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.126, H2=-21.566, H3=-17.417
2025-07-24 21:51:20,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3344 sites
2025-07-24 21:51:56,673 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.770, H2=-1.075, H3=-0.131
2025-07-24 21:51:57,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1176 sites
2025-07-24 21:52:16,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.009, H2=-0.683, H3=-0.929
2025-07-24 21:52:17,217 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3633 sites
2025-07-24 21:52:55,712 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=90.603, H2=-2.060, H3=-9.398
2025-07-24 21:52:56,265 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 410 sites
2025-07-24 21:53:09,642 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=14.554, H2=2.064, H3=-2.065
2025-07-24 21:53:10,215 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1332 sites
2025-07-24 21:53:30,780 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=59.215, H2=-5.445, H3=-2.468
2025-07-24 21:53:31,341 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 836 sites
2025-07-24 21:53:48,059 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=8.668, H2=-0.503, H3=0.914
2025-07-24 21:53:48,613 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 650 sites
2025-07-24 21:54:03,880 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=31.216, H2=5.710, H3=-3.144
2025-07-24 21:54:04,443 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 997 sites
2025-07-24 21:54:22,419 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-18.921, H2=-36.053, H3=-70.569
2025-07-24 21:54:23,018 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2542 sites
2025-07-24 21:54:52,763 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=4.137, H2=-0.310, H3=-2.588
2025-07-24 21:54:53,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2561 sites
2025-07-24 21:55:23,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-4.349, H2=-1.534, H3=-1.816
2025-07-24 21:55:23,906 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2308 sites
2025-07-24 21:55:51,971 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=16.143, H2=-4.656, H3=-2.605
2025-07-24 21:55:52,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 903 sites
2025-07-24 21:56:09,772 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=50.057, H2=-7.692, H3=-0.312
2025-07-24 21:56:10,323 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 629 sites
2025-07-24 21:56:25,379 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=4.429, H2=-3.338, H3=-3.113
2025-07-24 21:56:25,954 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1394 sites
2025-07-24 21:56:46,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=1.417, H2=-1.804, H3=-1.317
2025-07-24 21:56:47,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1717 sites
2025-07-24 21:57:10,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=41.209, H2=4.032, H3=1.190
2025-07-24 21:57:11,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1234 sites
2025-07-24 21:57:31,260 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=29.624, H2=-0.212, H3=2.783
2025-07-24 21:57:31,877 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 3177 sites
2025-07-24 21:58:06,641 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=16.341, H2=14.789, H3=3.255
2025-07-24 21:58:07,182 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=33 has insufficient data
2025-07-24 21:58:07,184 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=34
2025-07-24 21:58:07,213 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34 -9223372036854775808]
2025-07-24 21:58:07,810 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 21:58:33,805 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 21:58:34,379 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 21:58:55,685 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 21:58:56,299 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 21:59:29,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 21:59:29,887 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2689 sites
2025-07-24 22:00:00,837 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=0.403, H2=-0.845, H3=-2.497
2025-07-24 22:00:01,472 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3779 sites
2025-07-24 22:00:41,077 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.115, H2=18.895, H3=9.196
2025-07-24 22:00:41,630 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 564 sites
2025-07-24 22:00:56,154 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.827, H2=13.928, H3=-1.020
2025-07-24 22:00:56,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1186 sites
2025-07-24 22:01:16,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.457, H2=-3.172, H3=-2.183
2025-07-24 22:01:16,675 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3781 sites
2025-07-24 22:01:56,113 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=32.254, H2=29.760, H3=-1.866
2025-07-24 22:01:56,663 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 228 sites
2025-07-24 22:02:08,713 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=0.685, H2=11.480, H3=-3.837
2025-07-24 22:02:09,257 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 377 sites
2025-07-24 22:02:22,443 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.816, H2=-5.300, H3=3.468
2025-07-24 22:02:22,977 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 74 sites
2025-07-24 22:02:33,709 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.521, H2=-9.815, H3=-0.220
2025-07-24 22:02:34,246 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 259 sites
2025-07-24 22:02:46,434 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.066, H2=-5.104, H3=-0.677
2025-07-24 22:02:46,971 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 103 sites
2025-07-24 22:02:57,299 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 13: L-Moments invalid
2025-07-24 22:02:57,854 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 625 sites
2025-07-24 22:03:12,920 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-15.865, H2=-19.684, H3=-53.243
2025-07-24 22:03:13,497 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1593 sites
2025-07-24 22:03:35,938 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-10.112, H2=-9.762, H3=-20.477
2025-07-24 22:03:36,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1842 sites
2025-07-24 22:04:00,945 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.126, H2=-21.566, H3=-17.417
2025-07-24 22:04:01,570 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3344 sites
2025-07-24 22:04:37,599 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.770, H2=-1.075, H3=-0.131
2025-07-24 22:04:38,174 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1176 sites
2025-07-24 22:04:57,447 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.009, H2=-0.683, H3=-0.929
2025-07-24 22:04:58,075 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3633 sites
2025-07-24 22:05:36,348 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=90.603, H2=-2.060, H3=-9.398
2025-07-24 22:05:36,899 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 410 sites
2025-07-24 22:05:50,261 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=14.554, H2=2.064, H3=-2.065
2025-07-24 22:05:50,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 328 sites
2025-07-24 22:06:03,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=20.201, H2=-1.624, H3=-0.717
2025-07-24 22:06:04,079 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1004 sites
2025-07-24 22:06:22,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.678, H2=-2.374, H3=-1.229
2025-07-24 22:06:22,605 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 836 sites
2025-07-24 22:06:39,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=38.890, H2=-2.566, H3=-0.139
2025-07-24 22:06:39,870 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 650 sites
2025-07-24 22:06:55,131 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=27.524, H2=7.372, H3=-4.424
2025-07-24 22:06:55,695 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 997 sites
2025-07-24 22:07:13,645 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-33.847, H2=-25.127, H3=-12.811
2025-07-24 22:07:14,247 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2542 sites
2025-07-24 22:07:44,210 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=7.905, H2=0.626, H3=-2.017
2025-07-24 22:07:44,817 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2561 sites
2025-07-24 22:08:14,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.431, H2=-0.953, H3=-1.308
2025-07-24 22:08:15,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2308 sites
2025-07-24 22:08:43,491 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=11.530, H2=-7.980, H3=-4.039
2025-07-24 22:08:44,051 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 903 sites
2025-07-24 22:09:01,327 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=29.768, H2=-7.028, H3=-1.373
2025-07-24 22:09:01,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 629 sites
2025-07-24 22:09:16,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=4.217, H2=-2.516, H3=-2.050
2025-07-24 22:09:17,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1394 sites
2025-07-24 22:09:38,560 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=0.801, H2=-1.898, H3=-0.143
2025-07-24 22:09:39,144 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1717 sites
2025-07-24 22:10:02,676 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=27.320, H2=1.618, H3=0.392
2025-07-24 22:10:03,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1234 sites
2025-07-24 22:10:23,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=45.007, H2=-0.566, H3=1.074
2025-07-24 22:10:23,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 3177 sites
2025-07-24 22:10:58,427 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=39.323, H2=18.074, H3=8.146
2025-07-24 22:10:58,966 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=34 has insufficient data
2025-07-24 22:10:58,968 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=35
2025-07-24 22:10:58,997 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35 -9223372036854775808]
2025-07-24 22:10:59,588 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 22:11:25,724 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 22:11:26,299 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 22:11:47,519 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 22:11:48,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 22:12:21,269 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 22:12:21,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2689 sites
2025-07-24 22:12:53,190 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=0.403, H2=-0.845, H3=-2.497
2025-07-24 22:12:53,824 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3779 sites
2025-07-24 22:13:33,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.115, H2=18.895, H3=9.196
2025-07-24 22:13:34,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 564 sites
2025-07-24 22:13:48,751 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.827, H2=13.928, H3=-1.020
2025-07-24 22:13:49,319 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1186 sites
2025-07-24 22:14:08,774 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.457, H2=-3.172, H3=-2.183
2025-07-24 22:14:09,407 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3781 sites
2025-07-24 22:14:49,147 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=32.254, H2=29.760, H3=-1.866
2025-07-24 22:14:49,697 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 228 sites
2025-07-24 22:15:01,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=0.685, H2=11.480, H3=-3.837
2025-07-24 22:15:02,233 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 377 sites
2025-07-24 22:15:15,352 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.816, H2=-5.300, H3=3.468
2025-07-24 22:15:15,893 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 74 sites
2025-07-24 22:15:26,731 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.521, H2=-9.815, H3=-0.220
2025-07-24 22:15:27,271 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 259 sites
2025-07-24 22:15:39,498 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.066, H2=-5.104, H3=-0.677
2025-07-24 22:15:40,037 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 103 sites
2025-07-24 22:15:50,464 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 13: L-Moments invalid
2025-07-24 22:15:51,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 625 sites
2025-07-24 22:16:06,045 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-15.865, H2=-19.684, H3=-53.243
2025-07-24 22:16:06,625 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1593 sites
2025-07-24 22:16:29,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-10.112, H2=-9.762, H3=-20.477
2025-07-24 22:16:29,763 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1842 sites
2025-07-24 22:16:54,256 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.126, H2=-21.566, H3=-17.417
2025-07-24 22:16:54,876 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3344 sites
2025-07-24 22:17:31,161 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.770, H2=-1.075, H3=-0.131
2025-07-24 22:17:31,730 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1176 sites
2025-07-24 22:17:51,124 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.009, H2=-0.683, H3=-0.929
2025-07-24 22:17:51,752 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3633 sites
2025-07-24 22:18:30,224 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=90.603, H2=-2.060, H3=-9.398
2025-07-24 22:18:30,777 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 410 sites
2025-07-24 22:18:44,225 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=14.554, H2=2.064, H3=-2.065
2025-07-24 22:18:44,769 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 328 sites
2025-07-24 22:18:57,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=20.201, H2=-1.624, H3=-0.717
2025-07-24 22:18:58,117 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1004 sites
2025-07-24 22:19:16,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.678, H2=-2.374, H3=-1.229
2025-07-24 22:19:16,762 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 836 sites
2025-07-24 22:19:33,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=38.890, H2=-2.566, H3=-0.139
2025-07-24 22:19:33,995 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 650 sites
2025-07-24 22:19:49,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=27.524, H2=7.372, H3=-4.424
2025-07-24 22:19:49,819 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 997 sites
2025-07-24 22:20:07,727 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-33.847, H2=-25.127, H3=-12.811
2025-07-24 22:20:08,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2542 sites
2025-07-24 22:20:38,201 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=7.905, H2=0.626, H3=-2.017
2025-07-24 22:20:38,799 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2561 sites
2025-07-24 22:21:08,810 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.431, H2=-0.953, H3=-1.308
2025-07-24 22:21:09,406 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2308 sites
2025-07-24 22:21:37,591 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=11.530, H2=-7.980, H3=-4.039
2025-07-24 22:21:38,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 903 sites
2025-07-24 22:21:55,317 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=29.768, H2=-7.028, H3=-1.373
2025-07-24 22:21:55,869 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 629 sites
2025-07-24 22:22:10,978 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=4.217, H2=-2.516, H3=-2.050
2025-07-24 22:22:11,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1394 sites
2025-07-24 22:22:32,552 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=0.801, H2=-1.898, H3=-0.143
2025-07-24 22:22:33,134 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1717 sites
2025-07-24 22:22:56,792 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=27.320, H2=1.618, H3=0.392
2025-07-24 22:22:57,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1234 sites
2025-07-24 22:23:17,130 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=45.007, H2=-0.566, H3=1.074
2025-07-24 22:23:17,701 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1363 sites
2025-07-24 22:23:38,496 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=5.193, H2=4.411, H3=-2.073
2025-07-24 22:23:39,078 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1814 sites
2025-07-24 22:24:03,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=15.808, H2=13.116, H3=13.750
2025-07-24 22:24:03,942 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=35 has insufficient data
2025-07-24 22:24:03,944 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=36
2025-07-24 22:24:03,973 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
 -9223372036854775808]
2025-07-24 22:24:04,563 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 22:24:30,743 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 22:24:31,315 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 22:24:52,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 22:24:53,247 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 22:25:26,457 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 22:25:27,068 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2689 sites
2025-07-24 22:25:58,140 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=0.403, H2=-0.845, H3=-2.497
2025-07-24 22:25:58,775 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3779 sites
2025-07-24 22:26:38,580 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.115, H2=18.895, H3=9.196
2025-07-24 22:26:39,139 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 564 sites
2025-07-24 22:26:53,745 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.827, H2=13.928, H3=-1.020
2025-07-24 22:26:54,315 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1186 sites
2025-07-24 22:27:13,778 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.457, H2=-3.172, H3=-2.183
2025-07-24 22:27:14,364 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1823 sites
2025-07-24 22:27:38,885 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-13.266, H2=-9.223, H3=-11.070
2025-07-24 22:27:39,478 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1958 sites
2025-07-24 22:28:04,871 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.397, H2=14.756, H3=-0.488
2025-07-24 22:28:05,419 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 228 sites
2025-07-24 22:28:17,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=0.665, H2=5.896, H3=-8.343
2025-07-24 22:28:17,960 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 377 sites
2025-07-24 22:28:31,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=11.967, H2=-4.188, H3=2.825
2025-07-24 22:28:31,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 74 sites
2025-07-24 22:28:42,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=1.378, H2=-5.002, H3=0.470
2025-07-24 22:28:43,001 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 259 sites
2025-07-24 22:28:55,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.542, H2=-5.753, H3=1.025
2025-07-24 22:28:56,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 103 sites
2025-07-24 22:29:06,438 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 14: L-Moments invalid
2025-07-24 22:29:06,992 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 625 sites
2025-07-24 22:29:22,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-6.424, H2=-11.435, H3=-11.522
2025-07-24 22:29:22,666 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1593 sites
2025-07-24 22:29:45,256 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.219, H2=-17.233, H3=-8.701
2025-07-24 22:29:45,843 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1842 sites
2025-07-24 22:30:10,362 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-19.392, H2=-19.120, H3=-36.804
2025-07-24 22:30:10,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3344 sites
2025-07-24 22:30:47,362 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=14.228, H2=-2.900, H3=-0.543
2025-07-24 22:30:47,931 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1176 sites
2025-07-24 22:31:07,309 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=1.396, H2=0.522, H3=-1.427
2025-07-24 22:31:07,940 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3633 sites
2025-07-24 22:31:46,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=16.600, H2=-2.083, H3=-4.449
2025-07-24 22:31:46,873 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 410 sites
2025-07-24 22:32:00,328 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=1.897, H2=0.820, H3=-0.297
2025-07-24 22:32:00,872 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 328 sites
2025-07-24 22:32:13,650 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=16.126, H2=-4.728, H3=-4.806
2025-07-24 22:32:14,214 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1004 sites
2025-07-24 22:32:32,311 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=11.347, H2=-4.118, H3=-1.749
2025-07-24 22:32:32,872 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 836 sites
2025-07-24 22:32:49,580 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=18.835, H2=-2.018, H3=2.650
2025-07-24 22:32:50,135 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 650 sites
2025-07-24 22:33:05,414 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=22.085, H2=5.384, H3=-3.418
2025-07-24 22:33:05,977 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 997 sites
2025-07-24 22:33:23,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-10.131, H2=-11.250, H3=-9.268
2025-07-24 22:33:24,549 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2542 sites
2025-07-24 22:33:54,609 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=6.458, H2=1.011, H3=-1.427
2025-07-24 22:33:55,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2561 sites
2025-07-24 22:34:25,569 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-1.567, H2=-5.520, H3=-1.879
2025-07-24 22:34:26,166 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2308 sites
2025-07-24 22:34:54,281 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=9.853, H2=-7.374, H3=-4.516
2025-07-24 22:34:54,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 903 sites
2025-07-24 22:35:12,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=30.805, H2=-1.803, H3=-0.859
2025-07-24 22:35:12,751 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 629 sites
2025-07-24 22:35:27,780 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=8.439, H2=-3.953, H3=-0.527
2025-07-24 22:35:28,350 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1394 sites
2025-07-24 22:35:49,308 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=9.408, H2=-3.373, H3=-1.975
2025-07-24 22:35:49,888 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1717 sites
2025-07-24 22:36:13,358 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=59.158, H2=2.952, H3=-1.502
2025-07-24 22:36:13,925 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1234 sites
2025-07-24 22:36:33,685 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=12.981, H2=-0.268, H3=5.001
2025-07-24 22:36:34,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1363 sites
2025-07-24 22:36:55,032 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=24.230, H2=1.429, H3=-3.241
2025-07-24 22:36:55,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1814 sites
2025-07-24 22:37:19,942 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=10.662, H2=28.443, H3=7.999
2025-07-24 22:37:20,478 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=36 has insufficient data
2025-07-24 22:37:20,480 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=37
2025-07-24 22:37:20,509 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37 -9223372036854775808]
2025-07-24 22:37:21,103 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2044 sites
2025-07-24 22:37:47,110 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-5.801, H2=-8.325, H3=-8.531
2025-07-24 22:37:47,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1423 sites
2025-07-24 22:38:10,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-20.606, H2=-16.235, H3=-26.764
2025-07-24 22:38:11,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2949 sites
2025-07-24 22:38:44,771 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=8.695, H2=-10.097, H3=-5.729
2025-07-24 22:38:45,379 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2689 sites
2025-07-24 22:39:16,364 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=0.403, H2=-0.845, H3=-2.497
2025-07-24 22:39:16,998 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3779 sites
2025-07-24 22:39:56,349 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.115, H2=18.895, H3=9.196
2025-07-24 22:39:56,906 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 564 sites
2025-07-24 22:40:11,520 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.827, H2=13.928, H3=-1.020
2025-07-24 22:40:12,086 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1186 sites
2025-07-24 22:40:31,500 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-0.457, H2=-3.172, H3=-2.183
2025-07-24 22:40:32,082 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1823 sites
2025-07-24 22:40:56,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-13.266, H2=-9.223, H3=-11.070
2025-07-24 22:40:57,157 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1958 sites
2025-07-24 22:41:22,472 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.397, H2=14.756, H3=-0.488
2025-07-24 22:41:23,017 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 228 sites
2025-07-24 22:41:35,031 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=0.665, H2=5.896, H3=-8.343
2025-07-24 22:41:35,576 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 377 sites
2025-07-24 22:41:48,726 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=11.967, H2=-4.188, H3=2.825
2025-07-24 22:41:49,264 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 74 sites
2025-07-24 22:42:00,075 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=1.378, H2=-5.002, H3=0.470
2025-07-24 22:42:00,618 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 259 sites
2025-07-24 22:42:12,802 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.542, H2=-5.753, H3=1.025
2025-07-24 22:42:13,339 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 103 sites
2025-07-24 22:42:23,757 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 14: L-Moments invalid
2025-07-24 22:42:24,313 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 625 sites
2025-07-24 22:42:39,375 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-6.424, H2=-11.435, H3=-11.522
2025-07-24 22:42:39,952 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1593 sites
2025-07-24 22:43:02,545 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.219, H2=-17.233, H3=-8.701
2025-07-24 22:43:03,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1842 sites
2025-07-24 22:43:27,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-19.392, H2=-19.120, H3=-36.804
2025-07-24 22:43:28,231 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1898 sites
2025-07-24 22:43:53,193 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=18.300, H2=-1.707, H3=-4.207
2025-07-24 22:43:53,768 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1446 sites
2025-07-24 22:44:15,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=207.634, H2=1.088, H3=2.338
2025-07-24 22:44:15,718 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1176 sites
2025-07-24 22:44:34,977 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.909, H2=1.658, H3=-4.132
2025-07-24 22:44:35,606 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3633 sites
2025-07-24 22:45:14,067 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=41.814, H2=-4.201, H3=-11.730
2025-07-24 22:45:14,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 410 sites
2025-07-24 22:45:28,052 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=0.652, H2=0.917, H3=-2.504
2025-07-24 22:45:28,595 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 328 sites
2025-07-24 22:45:41,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=18.346, H2=-1.965, H3=-1.079
2025-07-24 22:45:41,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1004 sites
2025-07-24 22:45:59,917 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=12.012, H2=-8.476, H3=-16.586
2025-07-24 22:46:00,477 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 836 sites
2025-07-24 22:46:17,220 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=13.378, H2=-3.101, H3=4.632
2025-07-24 22:46:17,774 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 650 sites
2025-07-24 22:46:33,046 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=13.757, H2=5.361, H3=-6.521
2025-07-24 22:46:33,609 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 997 sites
2025-07-24 22:46:51,570 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-47.169, H2=-19.705, H3=-10.110
2025-07-24 22:46:52,172 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2542 sites
2025-07-24 22:47:22,312 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=3.595, H2=1.025, H3=-1.949
2025-07-24 22:47:22,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2561 sites
2025-07-24 22:47:53,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-0.880, H2=-2.668, H3=-5.965
2025-07-24 22:47:53,715 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2308 sites
2025-07-24 22:48:21,747 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=48.976, H2=-9.758, H3=-3.680
2025-07-24 22:48:22,310 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 903 sites
2025-07-24 22:48:39,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=42.650, H2=-4.781, H3=1.673
2025-07-24 22:48:40,160 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 629 sites
2025-07-24 22:48:55,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=7.699, H2=-6.208, H3=-3.030
2025-07-24 22:48:55,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1394 sites
2025-07-24 22:49:16,841 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=0.377, H2=-2.427, H3=-3.151
2025-07-24 22:49:17,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1717 sites
2025-07-24 22:49:41,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=16.513, H2=3.973, H3=-0.141
2025-07-24 22:49:41,747 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1234 sites
2025-07-24 22:50:01,585 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=54.166, H2=-0.655, H3=2.997
2025-07-24 22:50:02,157 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1363 sites
2025-07-24 22:50:22,964 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=27.726, H2=0.749, H3=-0.501
2025-07-24 22:50:23,545 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 1814 sites
2025-07-24 22:50:47,817 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=15.422, H2=31.409, H3=33.759
2025-07-24 22:50:48,351 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=37 has insufficient data
2025-07-24 22:50:48,354 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=38
2025-07-24 22:50:48,382 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38 -9223372036854775808]
2025-07-24 22:50:48,959 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1301 sites
2025-07-24 22:51:09,241 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-4.924, H2=-10.068, H3=-23.594
2025-07-24 22:51:09,798 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 743 sites
2025-07-24 22:51:25,842 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-5.099, H2=-5.858, H3=-4.397
2025-07-24 22:51:26,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1423 sites
2025-07-24 22:51:47,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-24.360, H2=-18.652, H3=-8.527
2025-07-24 22:51:48,344 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2949 sites
2025-07-24 22:52:21,657 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.236, H2=-4.736, H3=-10.487
2025-07-24 22:52:22,266 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2689 sites
2025-07-24 22:52:53,649 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.536, H2=-0.780, H3=-4.463
2025-07-24 22:52:54,287 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3779 sites
2025-07-24 22:53:33,845 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-0.546, H2=5.816, H3=6.465
2025-07-24 22:53:34,398 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 564 sites
2025-07-24 22:53:49,072 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.456, H2=10.178, H3=-4.879
2025-07-24 22:53:49,637 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1186 sites
2025-07-24 22:54:09,117 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-0.119, H2=-4.009, H3=-2.796
2025-07-24 22:54:09,698 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1823 sites
2025-07-24 22:54:34,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-10.676, H2=-14.917, H3=-10.060
2025-07-24 22:54:34,832 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1958 sites
2025-07-24 22:55:00,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=24.634, H2=14.053, H3=-4.223
2025-07-24 22:55:01,033 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 228 sites
2025-07-24 22:55:13,049 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.450, H2=3.605, H3=-10.273
2025-07-24 22:55:13,589 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 377 sites
2025-07-24 22:55:26,763 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.924, H2=-5.026, H3=1.274
2025-07-24 22:55:27,298 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 74 sites
2025-07-24 22:55:38,101 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.988, H2=-7.730, H3=0.639
2025-07-24 22:55:38,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 259 sites
2025-07-24 22:55:50,898 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.647, H2=-8.824, H3=-7.336
2025-07-24 22:55:51,433 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 103 sites
2025-07-24 22:56:02,041 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 15: L-Moments invalid
2025-07-24 22:56:02,597 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 625 sites
2025-07-24 22:56:17,759 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-9.407, H2=-14.415, H3=-11.825
2025-07-24 22:56:18,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1593 sites
2025-07-24 22:56:40,912 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-15.436, H2=-17.345, H3=-19.701
2025-07-24 22:56:41,506 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1842 sites
2025-07-24 22:57:06,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-8.311, H2=-25.090, H3=-36.489
2025-07-24 22:57:06,726 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1898 sites
2025-07-24 22:57:31,631 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=57.177, H2=-3.999, H3=-2.628
2025-07-24 22:57:32,207 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1446 sites
2025-07-24 22:57:53,580 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=20.258, H2=1.761, H3=0.698
2025-07-24 22:57:54,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1176 sites
2025-07-24 22:58:13,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=0.677, H2=-2.367, H3=-1.374
2025-07-24 22:58:14,076 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3633 sites
2025-07-24 22:58:52,594 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=49.951, H2=-6.684, H3=-7.588
2025-07-24 22:58:53,151 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 410 sites
2025-07-24 22:59:06,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=3.457, H2=2.027, H3=-0.821
2025-07-24 22:59:07,107 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 328 sites
2025-07-24 22:59:19,859 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=23.522, H2=-7.845, H3=-0.474
2025-07-24 22:59:20,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1004 sites
2025-07-24 22:59:38,473 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=64.271, H2=-7.229, H3=-1.255
2025-07-24 22:59:39,032 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 836 sites
2025-07-24 22:59:55,731 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=21.126, H2=-1.584, H3=0.391
2025-07-24 22:59:56,289 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 650 sites
2025-07-24 23:00:11,557 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=24.304, H2=5.093, H3=-2.577
2025-07-24 23:00:12,125 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 997 sites
2025-07-24 23:00:30,163 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-20.061, H2=-16.775, H3=-9.542
2025-07-24 23:00:30,763 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2542 sites
2025-07-24 23:01:00,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=3.029, H2=0.115, H3=-2.864
2025-07-24 23:01:01,460 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2561 sites
2025-07-24 23:01:31,597 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-4.208, H2=-1.695, H3=-0.955
2025-07-24 23:01:32,202 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 2308 sites
2025-07-24 23:02:00,408 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=31.176, H2=-7.615, H3=-0.924
2025-07-24 23:02:00,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 903 sites
2025-07-24 23:02:18,217 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=37.654, H2=-2.484, H3=-0.019
2025-07-24 23:02:18,771 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 629 sites
2025-07-24 23:02:33,886 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=16.031, H2=-13.720, H3=-11.358
2025-07-24 23:02:34,460 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1394 sites
2025-07-24 23:02:55,571 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=0.607, H2=-4.459, H3=-6.433
2025-07-24 23:02:56,156 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1717 sites
2025-07-24 23:03:19,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=38.591, H2=1.685, H3=-1.009
2025-07-24 23:03:20,406 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1234 sites
2025-07-24 23:03:40,204 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=30.609, H2=-1.051, H3=3.956
2025-07-24 23:03:40,775 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 1363 sites
2025-07-24 23:04:01,710 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=8.896, H2=2.581, H3=-1.715
2025-07-24 23:04:02,292 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 1814 sites
2025-07-24 23:04:26,741 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=34.475, H2=14.719, H3=11.711
2025-07-24 23:04:27,277 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=38 has insufficient data
2025-07-24 23:04:27,279 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=39
2025-07-24 23:04:27,303 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
 -9223372036854775808]
2025-07-24 23:04:27,879 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1301 sites
2025-07-24 23:04:48,262 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-4.924, H2=-10.068, H3=-23.594
2025-07-24 23:04:48,820 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 743 sites
2025-07-24 23:05:04,852 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-5.099, H2=-5.858, H3=-4.397
2025-07-24 23:05:05,427 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1423 sites
2025-07-24 23:05:26,848 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-24.360, H2=-18.652, H3=-8.527
2025-07-24 23:05:27,461 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2949 sites
2025-07-24 23:06:00,809 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.236, H2=-4.736, H3=-10.487
2025-07-24 23:06:01,419 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2689 sites
2025-07-24 23:06:32,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.536, H2=-0.780, H3=-4.463
2025-07-24 23:06:33,239 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3779 sites
2025-07-24 23:07:12,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-0.546, H2=5.816, H3=6.465
2025-07-24 23:07:13,503 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 564 sites
2025-07-24 23:07:28,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.456, H2=10.178, H3=-4.879
2025-07-24 23:07:28,671 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1186 sites
2025-07-24 23:07:48,103 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-0.119, H2=-4.009, H3=-2.796
2025-07-24 23:07:48,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1823 sites
2025-07-24 23:08:13,066 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-10.676, H2=-14.917, H3=-10.060
2025-07-24 23:08:13,654 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1958 sites
2025-07-24 23:08:39,043 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=24.634, H2=14.053, H3=-4.223
2025-07-24 23:08:39,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 228 sites
2025-07-24 23:08:51,601 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.450, H2=3.605, H3=-10.273
2025-07-24 23:08:52,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 377 sites
2025-07-24 23:09:05,406 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.924, H2=-5.026, H3=1.274
2025-07-24 23:09:05,945 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 74 sites
2025-07-24 23:09:16,718 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.988, H2=-7.730, H3=0.639
2025-07-24 23:09:17,258 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 259 sites
2025-07-24 23:09:29,492 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.647, H2=-8.824, H3=-7.336
2025-07-24 23:09:30,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 103 sites
2025-07-24 23:09:40,415 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 15: L-Moments invalid
2025-07-24 23:09:40,966 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 625 sites
2025-07-24 23:09:56,027 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-9.407, H2=-14.415, H3=-11.825
2025-07-24 23:09:56,604 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1593 sites
2025-07-24 23:10:19,194 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-15.436, H2=-17.345, H3=-19.701
2025-07-24 23:10:19,777 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1842 sites
2025-07-24 23:10:44,313 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-8.311, H2=-25.090, H3=-36.489
2025-07-24 23:10:44,902 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1898 sites
2025-07-24 23:11:09,817 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=57.177, H2=-3.999, H3=-2.628
2025-07-24 23:11:10,395 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1446 sites
2025-07-24 23:11:31,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=20.258, H2=1.761, H3=0.698
2025-07-24 23:11:32,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1176 sites
2025-07-24 23:11:51,612 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=0.677, H2=-2.367, H3=-1.374
2025-07-24 23:11:52,242 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3633 sites
2025-07-24 23:12:30,535 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=49.951, H2=-6.684, H3=-7.588
2025-07-24 23:12:31,085 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 410 sites
2025-07-24 23:12:44,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=3.457, H2=2.027, H3=-0.821
2025-07-24 23:12:45,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 328 sites
2025-07-24 23:12:57,866 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=23.522, H2=-7.845, H3=-0.474
2025-07-24 23:12:58,428 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1004 sites
2025-07-24 23:13:16,521 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=64.271, H2=-7.229, H3=-1.255
2025-07-24 23:13:17,082 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 836 sites
2025-07-24 23:13:33,837 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=21.126, H2=-1.584, H3=0.391
2025-07-24 23:13:34,389 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 650 sites
2025-07-24 23:13:49,725 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=24.304, H2=5.093, H3=-2.577
2025-07-24 23:13:50,290 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 997 sites
2025-07-24 23:14:08,233 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-20.061, H2=-16.775, H3=-9.542
2025-07-24 23:14:08,828 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2542 sites
2025-07-24 23:14:38,681 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=3.029, H2=0.115, H3=-2.864
2025-07-24 23:14:39,287 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2561 sites
2025-07-24 23:15:09,529 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-4.208, H2=-1.695, H3=-0.955
2025-07-24 23:15:10,127 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 2308 sites
2025-07-24 23:15:38,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=31.176, H2=-7.615, H3=-0.924
2025-07-24 23:15:39,000 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 903 sites
2025-07-24 23:15:56,274 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=37.654, H2=-2.484, H3=-0.019
2025-07-24 23:15:56,829 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 629 sites
2025-07-24 23:16:11,970 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=16.031, H2=-13.720, H3=-11.358
2025-07-24 23:16:12,541 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1394 sites
2025-07-24 23:16:33,564 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=0.607, H2=-4.459, H3=-6.433
2025-07-24 23:16:34,143 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1717 sites
2025-07-24 23:16:57,754 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=38.591, H2=1.685, H3=-1.009
2025-07-24 23:16:58,302 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 427 sites
2025-07-24 23:17:11,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=-0.814, H2=-7.417, H3=-5.155
2025-07-24 23:17:12,391 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 807 sites
2025-07-24 23:17:28,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=9.570, H2=2.945, H3=7.742
2025-07-24 23:17:29,429 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 1363 sites
2025-07-24 23:17:50,168 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=10.470, H2=3.515, H3=-0.856
2025-07-24 23:17:50,752 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 1814 sites
2025-07-24 23:18:15,035 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=10.056, H2=9.060, H3=8.788
2025-07-24 23:18:15,567 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=39 has insufficient data
2025-07-24 23:18:15,569 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=40
2025-07-24 23:18:15,598 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40 -9223372036854775808]
2025-07-24 23:18:16,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1301 sites
2025-07-24 23:18:36,597 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-4.924, H2=-10.068, H3=-23.594
2025-07-24 23:18:37,154 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 743 sites
2025-07-24 23:18:53,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-5.099, H2=-5.858, H3=-4.397
2025-07-24 23:18:53,775 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1423 sites
2025-07-24 23:19:15,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-24.360, H2=-18.652, H3=-8.527
2025-07-24 23:19:15,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2949 sites
2025-07-24 23:19:48,738 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.236, H2=-4.736, H3=-10.487
2025-07-24 23:19:49,348 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2689 sites
2025-07-24 23:20:20,419 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.536, H2=-0.780, H3=-4.463
2025-07-24 23:20:21,050 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3779 sites
2025-07-24 23:21:00,661 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-0.546, H2=5.816, H3=6.465
2025-07-24 23:21:01,213 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 564 sites
2025-07-24 23:21:15,808 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.456, H2=10.178, H3=-4.879
2025-07-24 23:21:16,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1186 sites
2025-07-24 23:21:35,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-0.119, H2=-4.009, H3=-2.796
2025-07-24 23:21:36,322 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1823 sites
2025-07-24 23:22:00,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-10.676, H2=-14.917, H3=-10.060
2025-07-24 23:22:01,410 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1958 sites
2025-07-24 23:22:26,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=24.634, H2=14.053, H3=-4.223
2025-07-24 23:22:27,404 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 228 sites
2025-07-24 23:22:39,434 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.450, H2=3.605, H3=-10.273
2025-07-24 23:22:39,977 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 377 sites
2025-07-24 23:22:53,220 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.924, H2=-5.026, H3=1.274
2025-07-24 23:22:53,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 74 sites
2025-07-24 23:23:04,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.988, H2=-7.730, H3=0.639
2025-07-24 23:23:05,081 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 259 sites
2025-07-24 23:23:17,319 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.647, H2=-8.824, H3=-7.336
2025-07-24 23:23:17,858 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 103 sites
2025-07-24 23:23:28,320 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 15: L-Moments invalid
2025-07-24 23:23:28,875 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 625 sites
2025-07-24 23:23:44,031 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-9.407, H2=-14.415, H3=-11.825
2025-07-24 23:23:44,611 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1593 sites
2025-07-24 23:24:07,321 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-15.436, H2=-17.345, H3=-19.701
2025-07-24 23:24:07,908 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1842 sites
2025-07-24 23:24:32,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-8.311, H2=-25.090, H3=-36.489
2025-07-24 23:24:33,098 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1898 sites
2025-07-24 23:24:58,212 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=57.177, H2=-3.999, H3=-2.628
2025-07-24 23:24:58,789 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1446 sites
2025-07-24 23:25:20,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=20.258, H2=1.761, H3=0.698
2025-07-24 23:25:20,976 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1176 sites
2025-07-24 23:25:40,391 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=0.677, H2=-2.367, H3=-1.374
2025-07-24 23:25:41,024 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3633 sites
2025-07-24 23:26:19,715 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=49.951, H2=-6.684, H3=-7.588
2025-07-24 23:26:20,269 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 410 sites
2025-07-24 23:26:33,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=3.457, H2=2.027, H3=-0.821
2025-07-24 23:26:34,246 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 328 sites
2025-07-24 23:26:47,041 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=23.522, H2=-7.845, H3=-0.474
2025-07-24 23:26:47,605 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1004 sites
2025-07-24 23:27:05,764 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=64.271, H2=-7.229, H3=-1.255
2025-07-24 23:27:06,324 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 836 sites
2025-07-24 23:27:23,056 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=21.126, H2=-1.584, H3=0.391
2025-07-24 23:27:23,608 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 650 sites
2025-07-24 23:27:38,983 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=24.304, H2=5.093, H3=-2.577
2025-07-24 23:27:39,546 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 997 sites
2025-07-24 23:27:57,631 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-20.061, H2=-16.775, H3=-9.542
2025-07-24 23:27:58,231 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2542 sites
2025-07-24 23:28:28,368 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=3.029, H2=0.115, H3=-2.864
2025-07-24 23:28:28,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2561 sites
2025-07-24 23:28:59,271 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-4.208, H2=-1.695, H3=-0.955
2025-07-24 23:28:59,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 728 sites
2025-07-24 23:29:15,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-24.425, H2=-6.853, H3=-1.118
2025-07-24 23:29:16,316 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1580 sites
2025-07-24 23:29:38,896 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=7.548, H2=-3.410, H3=-3.408
2025-07-24 23:29:39,460 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 903 sites
2025-07-24 23:29:56,794 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=26.325, H2=-3.449, H3=-3.913
2025-07-24 23:29:57,347 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 629 sites
2025-07-24 23:30:12,472 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=12.062, H2=-4.080, H3=-8.315
2025-07-24 23:30:13,044 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1394 sites
2025-07-24 23:30:34,153 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=2.700, H2=-3.173, H3=-4.271
2025-07-24 23:30:34,737 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1717 sites
2025-07-24 23:30:58,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=17.458, H2=0.160, H3=0.351
2025-07-24 23:30:58,959 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 427 sites
2025-07-24 23:31:12,522 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=-0.359, H2=-6.517, H3=-4.214
2025-07-24 23:31:13,079 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 807 sites
2025-07-24 23:31:29,657 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=105.986, H2=1.441, H3=9.198
2025-07-24 23:31:30,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 1363 sites
2025-07-24 23:31:51,144 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=15.660, H2=2.935, H3=-0.367
2025-07-24 23:31:51,727 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 40 with 1814 sites
2025-07-24 23:32:16,236 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 40: H1=7.987, H2=10.586, H3=5.100
2025-07-24 23:32:16,771 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=40 has insufficient data
2025-07-24 23:32:16,773 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=41
2025-07-24 23:32:16,802 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40                   41 -9223372036854775808]
2025-07-24 23:32:17,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1301 sites
2025-07-24 23:32:37,719 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-4.924, H2=-10.068, H3=-23.594
2025-07-24 23:32:38,279 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 743 sites
2025-07-24 23:32:54,359 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-5.099, H2=-5.858, H3=-4.397
2025-07-24 23:32:54,930 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1423 sites
2025-07-24 23:33:16,249 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-24.360, H2=-18.652, H3=-8.527
2025-07-24 23:33:16,865 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2949 sites
2025-07-24 23:33:50,044 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.236, H2=-4.736, H3=-10.487
2025-07-24 23:33:50,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2689 sites
2025-07-24 23:34:21,851 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.536, H2=-0.780, H3=-4.463
2025-07-24 23:34:22,480 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3779 sites
2025-07-24 23:35:02,231 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-0.546, H2=5.816, H3=6.465
2025-07-24 23:35:02,785 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 564 sites
2025-07-24 23:35:17,357 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.456, H2=10.178, H3=-4.879
2025-07-24 23:35:17,926 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1186 sites
2025-07-24 23:35:37,357 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-0.119, H2=-4.009, H3=-2.796
2025-07-24 23:35:37,941 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1823 sites
2025-07-24 23:36:02,377 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-10.676, H2=-14.917, H3=-10.060
2025-07-24 23:36:02,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1958 sites
2025-07-24 23:36:28,476 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=24.634, H2=14.053, H3=-4.223
2025-07-24 23:36:29,020 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 228 sites
2025-07-24 23:36:41,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.450, H2=3.605, H3=-10.273
2025-07-24 23:36:41,584 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 377 sites
2025-07-24 23:36:54,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.924, H2=-5.026, H3=1.274
2025-07-24 23:36:55,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 74 sites
2025-07-24 23:37:06,171 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.988, H2=-7.730, H3=0.639
2025-07-24 23:37:06,714 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 259 sites
2025-07-24 23:37:19,004 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.647, H2=-8.824, H3=-7.336
2025-07-24 23:37:19,543 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 103 sites
2025-07-24 23:37:29,972 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 15: L-Moments invalid
2025-07-24 23:37:30,531 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 625 sites
2025-07-24 23:37:45,672 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-9.407, H2=-14.415, H3=-11.825
2025-07-24 23:37:46,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1593 sites
2025-07-24 23:38:08,766 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-15.436, H2=-17.345, H3=-19.701
2025-07-24 23:38:09,353 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1842 sites
2025-07-24 23:38:33,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-8.311, H2=-25.090, H3=-36.489
2025-07-24 23:38:34,445 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1898 sites
2025-07-24 23:39:01,393 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=57.177, H2=-3.999, H3=-2.628
2025-07-24 23:39:01,968 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1446 sites
2025-07-24 23:39:23,452 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=20.258, H2=1.761, H3=0.698
2025-07-24 23:39:24,020 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1176 sites
2025-07-24 23:39:43,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=0.677, H2=-2.367, H3=-1.374
2025-07-24 23:39:43,895 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1773 sites
2025-07-24 23:40:07,923 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=63.603, H2=0.204, H3=-9.036
2025-07-24 23:40:08,509 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1860 sites
2025-07-24 23:40:33,114 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=17.682, H2=-3.395, H3=-2.827
2025-07-24 23:40:33,665 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 410 sites
2025-07-24 23:40:47,120 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=4.552, H2=1.705, H3=-0.286
2025-07-24 23:40:47,663 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 328 sites
2025-07-24 23:41:00,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=17.654, H2=-7.486, H3=0.118
2025-07-24 23:41:01,010 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1004 sites
2025-07-24 23:41:19,117 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=5.261, H2=-3.037, H3=-2.965
2025-07-24 23:41:19,674 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 836 sites
2025-07-24 23:41:36,429 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=29.118, H2=-0.397, H3=1.230
2025-07-24 23:41:36,981 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 650 sites
2025-07-24 23:41:52,204 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=45.910, H2=3.817, H3=-1.169
2025-07-24 23:41:52,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 997 sites
2025-07-24 23:42:10,661 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-20.358, H2=-19.090, H3=-13.891
2025-07-24 23:42:11,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2542 sites
2025-07-24 23:42:41,425 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=11.942, H2=0.725, H3=-0.660
2025-07-24 23:42:42,033 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 2561 sites
2025-07-24 23:43:12,196 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-1.037, H2=-1.522, H3=0.922
2025-07-24 23:43:12,754 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 728 sites
2025-07-24 23:43:28,706 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-6.154, H2=-6.784, H3=-3.727
2025-07-24 23:43:29,281 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1580 sites
2025-07-24 23:43:51,785 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=7.353, H2=-4.915, H3=-2.277
2025-07-24 23:43:52,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 903 sites
2025-07-24 23:44:09,591 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=26.376, H2=-3.095, H3=-1.085
2025-07-24 23:44:10,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 629 sites
2025-07-24 23:44:25,247 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=4.496, H2=-6.030, H3=-4.494
2025-07-24 23:44:25,821 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1394 sites
2025-07-24 23:44:46,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=4.644, H2=-2.101, H3=-1.653
2025-07-24 23:44:47,469 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 1717 sites
2025-07-24 23:45:11,001 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=31.163, H2=3.050, H3=-1.185
2025-07-24 23:45:11,549 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 427 sites
2025-07-24 23:45:25,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=-0.508, H2=-4.901, H3=-4.085
2025-07-24 23:45:25,734 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 807 sites
2025-07-24 23:45:42,305 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=17.859, H2=3.826, H3=13.115
2025-07-24 23:45:42,877 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 40 with 1363 sites
2025-07-24 23:46:03,689 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 40: H1=4.392, H2=1.363, H3=-0.612
2025-07-24 23:46:04,271 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 41 with 1814 sites
2025-07-24 23:46:28,557 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 41: H1=11.361, H2=11.043, H3=15.799
2025-07-24 23:46:29,089 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=41 has insufficient data
2025-07-24 23:46:29,091 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=42
2025-07-24 23:46:29,120 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40                   41                   42
 -9223372036854775808]
2025-07-24 23:46:29,692 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1301 sites
2025-07-24 23:46:49,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-4.924, H2=-10.068, H3=-23.594
2025-07-24 23:46:50,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 743 sites
2025-07-24 23:47:06,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-5.099, H2=-5.858, H3=-4.397
2025-07-24 23:47:07,135 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1423 sites
2025-07-24 23:47:28,395 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-24.360, H2=-18.652, H3=-8.527
2025-07-24 23:47:29,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2949 sites
2025-07-24 23:48:01,992 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.236, H2=-4.736, H3=-10.487
2025-07-24 23:48:02,603 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2689 sites
2025-07-24 23:48:33,754 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=0.536, H2=-0.780, H3=-4.463
2025-07-24 23:48:34,390 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3779 sites
2025-07-24 23:49:13,948 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-0.546, H2=5.816, H3=6.465
2025-07-24 23:49:14,504 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 564 sites
2025-07-24 23:49:29,131 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-1.456, H2=10.178, H3=-4.879
2025-07-24 23:49:29,697 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1186 sites
2025-07-24 23:49:49,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-0.119, H2=-4.009, H3=-2.796
2025-07-24 23:49:49,721 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1823 sites
2025-07-24 23:50:14,133 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-10.676, H2=-14.917, H3=-10.060
2025-07-24 23:50:14,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1958 sites
2025-07-24 23:50:40,142 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=24.634, H2=14.053, H3=-4.223
2025-07-24 23:50:40,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 228 sites
2025-07-24 23:50:52,697 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.450, H2=3.605, H3=-10.273
2025-07-24 23:50:53,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 377 sites
2025-07-24 23:51:06,368 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.924, H2=-5.026, H3=1.274
2025-07-24 23:51:06,903 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 74 sites
2025-07-24 23:51:17,695 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.988, H2=-7.730, H3=0.639
2025-07-24 23:51:18,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 259 sites
2025-07-24 23:51:30,492 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.647, H2=-8.824, H3=-7.336
2025-07-24 23:51:31,030 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 103 sites
2025-07-24 23:51:41,702 - __main__ - ERROR - run_heterogeneity_analysis.py:263 - Error processing region 15: L-Moments invalid
2025-07-24 23:51:42,255 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 625 sites
2025-07-24 23:51:57,438 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-9.407, H2=-14.415, H3=-11.825
2025-07-24 23:51:58,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1593 sites
2025-07-24 23:52:22,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-15.436, H2=-17.345, H3=-19.701
2025-07-24 23:52:23,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1842 sites
