2025-07-22 09:10:16,304 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-07-22 09:10:16,305 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-22 09:10:16,305 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_8_hierarchical/CI_results_hierarchical.nc
2025-07-22 09:10:16,305 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_8_hierarchical
2025-07-22 09:10:16,305 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-07-22 09:10:16,305 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-07-22 09:10:16,306 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-07-22 09:10:16,307 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-07-22 09:10:16,309 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_8_hierarchical/CI_results_hierarchical.nc
2025-07-22 09:10:16,309 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-22 09:10:18,692 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-22 09:10:19,130 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-22 09:10:19,171 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-22 09:10:19,197 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-22 09:10:19,199 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-22 09:10:19,221 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-22 09:10:19,254 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-22 09:10:19,279 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-22 09:10:19,301 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-22 09:10:19,350 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-22 09:10:19,352 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-22 09:10:19,354 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-22 09:10:19,381 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-22 09:10:19,383 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-22 09:10:19,385 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-22 09:10:19,387 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-22 09:10:19,441 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-22 09:10:19,442 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-22 09:10:19,444 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-22 09:10:19,448 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-22 09:10:19,448 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-22 09:10:19,450 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-22 09:10:19,451 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-22 09:10:19,451 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-22 09:10:19,453 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-22 09:10:19,456 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-22 09:10:19,458 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-22 09:10:19,460 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-22 09:10:19,462 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-22 09:10:19,464 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-22 09:10:19,467 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-22 09:10:19,469 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-22 09:10:19,471 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-22 09:10:19,473 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-22 09:10:19,475 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-22 09:10:19,476 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-22 09:10:19,478 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-22 09:10:19,481 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-22 09:10:19,483 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-22 09:10:19,493 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-07-22 09:10:19,493 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-22 09:10:19,493 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-22 09:10:19,494 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 100 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100']
2025-07-22 09:10:19,495 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-22 09:10:19,498 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-22 09:10:19,498 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-22 09:10:19,500 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-22 09:10:19,500 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-22 09:10:19,502 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-22 09:10:19,502 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-22 09:10:19,504 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-22 09:10:19,504 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-22 09:10:19,506 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-22 09:10:19,506 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-22 09:10:19,509 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-22 09:10:19,509 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-22 09:10:19,512 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-22 09:10:19,512 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-22 09:10:19,514 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-22 09:10:19,515 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-22 09:10:19,517 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-22 09:10:19,518 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-22 09:10:19,520 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-22 09:10:19,521 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-22 09:10:19,523 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-22 09:10:19,523 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-22 09:10:19,526 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-22 09:10:19,526 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-22 09:10:19,529 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-22 09:10:19,529 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-22 09:10:19,531 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-22 09:10:19,532 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-22 09:10:19,534 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-22 09:10:19,535 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-22 09:10:19,537 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-22 09:10:19,537 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-22 09:10:19,540 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-22 09:10:19,540 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-22 09:10:19,543 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-22 09:10:19,543 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-22 09:10:19,545 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-22 09:10:19,545 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-22 09:10:19,548 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-22 09:10:19,548 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-22 09:10:19,551 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-22 09:10:19,551 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-22 09:10:19,554 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-22 09:10:19,554 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-22 09:10:19,556 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-22 09:10:19,557 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-22 09:10:19,559 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-22 09:10:19,559 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-22 09:10:19,562 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-22 09:10:19,562 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-22 09:10:19,565 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-22 09:10:19,565 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-22 09:10:19,567 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-22 09:10:19,567 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-22 09:10:19,570 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-22 09:10:19,570 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-22 09:10:19,573 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-22 09:10:19,573 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-22 09:10:19,576 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-22 09:10:19,576 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-22 09:10:19,578 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-22 09:10:19,579 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-22 09:10:19,581 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-22 09:10:19,581 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-22 09:10:19,584 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-22 09:10:19,584 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-22 09:10:19,586 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-22 09:10:19,587 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-22 09:10:19,589 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-22 09:10:19,589 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-22 09:10:19,592 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-22 09:10:19,592 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-22 09:10:19,594 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-22 09:10:19,595 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-22 09:10:19,597 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-22 09:10:19,598 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-22 09:10:19,600 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-22 09:10:19,600 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-22 09:10:19,603 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-22 09:10:19,603 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-22 09:10:19,605 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-22 09:10:19,606 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-22 09:10:19,608 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-22 09:10:19,608 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-22 09:10:19,611 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-22 09:10:19,611 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-22 09:10:19,613 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-22 09:10:19,614 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-22 09:10:19,616 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-22 09:10:19,616 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-22 09:10:19,619 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-22 09:10:19,619 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-22 09:10:19,622 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-22 09:10:19,689 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-22 09:10:19,692 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-22 09:10:19,692 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-22 09:10:19,694 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-22 09:10:19,695 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-22 09:10:19,697 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-22 09:10:19,697 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-22 09:10:19,699 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-22 09:10:19,700 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-22 09:10:19,702 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-22 09:10:19,702 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-22 09:10:19,705 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-22 09:10:19,705 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-22 09:10:19,708 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-22 09:10:19,708 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-22 09:10:19,711 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-22 09:10:19,711 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-22 09:10:19,713 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-22 09:10:19,713 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-22 09:10:19,716 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-22 09:10:19,716 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-22 09:10:19,719 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-22 09:10:19,719 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-22 09:10:19,721 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-22 09:10:19,721 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-22 09:10:19,724 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-22 09:10:19,724 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-22 09:10:19,727 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-22 09:10:19,727 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-22 09:10:19,729 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-22 09:10:19,730 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-22 09:10:19,732 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-22 09:10:19,732 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-07-22 09:10:19,735 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-07-22 09:10:19,735 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-07-22 09:10:19,737 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-07-22 09:10:19,737 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-07-22 09:10:19,740 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-07-22 09:10:19,740 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-07-22 09:10:19,742 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-07-22 09:10:19,743 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-07-22 09:10:19,745 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-07-22 09:10:19,745 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-07-22 09:10:19,748 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-07-22 09:10:19,748 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-07-22 09:10:19,751 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-07-22 09:10:19,751 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-07-22 09:10:19,754 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-07-22 09:10:19,754 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-07-22 09:10:19,756 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-07-22 09:10:19,756 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-07-22 09:10:19,759 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-07-22 09:10:19,759 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-07-22 09:10:19,761 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-07-22 09:10:19,761 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-07-22 09:10:19,764 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-07-22 09:10:19,764 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-07-22 09:10:19,766 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-07-22 09:10:19,766 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-07-22 09:10:19,769 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-07-22 09:10:19,769 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-07-22 09:10:19,772 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-07-22 09:10:19,772 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-07-22 09:10:19,775 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-07-22 09:10:19,775 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-07-22 09:10:19,777 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-07-22 09:10:19,777 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-07-22 09:10:19,780 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-07-22 09:10:19,780 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-07-22 09:10:19,782 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-07-22 09:10:19,782 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-07-22 09:10:19,784 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-07-22 09:10:19,785 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-07-22 09:10:19,787 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-07-22 09:10:19,787 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-07-22 09:10:19,790 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-07-22 09:10:19,790 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-07-22 09:10:19,793 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-07-22 09:10:19,793 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-07-22 09:10:19,795 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-07-22 09:10:19,796 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-07-22 09:10:19,798 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-07-22 09:10:19,798 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-07-22 09:10:19,800 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-07-22 09:10:19,801 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-07-22 09:10:19,803 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-07-22 09:10:19,803 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-07-22 09:10:19,806 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-07-22 09:10:19,806 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-07-22 09:10:19,809 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-07-22 09:10:19,809 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-07-22 09:10:19,811 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-07-22 09:10:19,812 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-07-22 09:10:19,814 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-07-22 09:10:19,814 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-07-22 09:10:19,817 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-07-22 09:10:19,817 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-07-22 09:10:19,819 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-07-22 09:10:19,819 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-07-22 09:10:19,822 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-07-22 09:10:19,822 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-07-22 09:10:19,824 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-07-22 09:10:19,825 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-07-22 09:10:19,827 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-07-22 09:10:19,827 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-07-22 09:10:19,830 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-07-22 09:10:19,830 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_8_hierarchical/CI_results_hierarchical_remap.nc
2025-07-22 09:10:23,033 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-07-22 09:10:23,033 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-07-22 09:10:23,033 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-07-22 09:10:23,033 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_8_hierarchical/CI_results_hierarchical.nc
2025-07-22 09:10:23,033 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_8_hierarchical/CI_results_hierarchical_remap.nc
2025-07-22 09:10:23,033 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 100 variables
2025-07-22 09:10:23,034 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100
2025-07-22 09:10:23,034 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-07-22 09:10:23,036 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_8_hierarchical/CI_results_hierarchical_remap.nc
2025-07-22 09:10:23,036 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-07-22 09:10:23,036 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-22 09:10:23,075 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-07-22 09:10:28,047 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_8_hierarchical/CI_results_hierarchical_remap.nc
2025-07-22 09:10:28,084 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-07-22 09:10:28,085 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-07-22 09:10:28,085 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-07-22 09:10:28,139 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-07-22 09:10:28,203 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 21756 sites
2025-07-22 09:11:06,657 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=139.750, H2=4.272, H3=-0.644
2025-07-22 09:11:06,738 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 30603 sites
2025-07-22 09:12:01,148 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=75.608, H2=23.116, H3=7.379
2025-07-22 09:12:01,174 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-07-22 09:12:01,174 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-07-22 09:12:01,176 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-07-22 09:12:01,239 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 21756 sites
2025-07-22 09:12:39,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=139.750, H2=4.272, H3=-0.644
2025-07-22 09:12:40,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 12415 sites
2025-07-22 09:13:01,792 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=71.172, H2=22.076, H3=6.337
2025-07-22 09:13:01,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 18188 sites
2025-07-22 09:13:33,712 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=56.013, H2=18.278, H3=4.706
2025-07-22 09:13:33,738 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-07-22 09:13:33,738 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-07-22 09:13:33,739 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-07-22 09:13:33,772 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:13:41,778 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:13:41,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 17205 sites
2025-07-22 09:14:11,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=97.009, H2=1.086, H3=-0.248
2025-07-22 09:14:11,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 12415 sites
2025-07-22 09:14:33,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=76.543, H2=24.597, H3=7.539
2025-07-22 09:14:33,835 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 18188 sites
2025-07-22 09:15:05,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=36.087, H2=12.653, H3=4.926
2025-07-22 09:15:05,647 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-07-22 09:15:05,647 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-07-22 09:15:05,648 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-07-22 09:15:05,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:15:13,582 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:15:13,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 17205 sites
2025-07-22 09:15:43,765 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=97.009, H2=1.086, H3=-0.248
2025-07-22 09:15:43,814 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 12415 sites
2025-07-22 09:16:05,429 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=76.543, H2=24.597, H3=7.539
2025-07-22 09:16:05,470 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8552 sites
2025-07-22 09:16:20,308 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=28.620, H2=15.961, H3=3.322
2025-07-22 09:16:20,351 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9636 sites
2025-07-22 09:16:37,163 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.186, H2=4.313, H3=2.285
2025-07-22 09:16:37,188 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-07-22 09:16:37,188 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-07-22 09:16:37,190 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-07-22 09:16:37,224 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:16:45,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:16:45,269 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 17205 sites
2025-07-22 09:17:15,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=97.009, H2=1.086, H3=-0.248
2025-07-22 09:17:15,363 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 10565 sites
2025-07-22 09:17:33,699 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=50.687, H2=18.321, H3=7.998
2025-07-22 09:17:33,729 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1850 sites
2025-07-22 09:17:36,945 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=32.289, H2=0.064, H3=-3.784
2025-07-22 09:17:36,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8552 sites
2025-07-22 09:17:51,885 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=37.071, H2=11.008, H3=2.493
2025-07-22 09:17:51,928 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 9636 sites
2025-07-22 09:18:08,831 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.859, H2=4.989, H3=2.717
2025-07-22 09:18:08,856 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-07-22 09:18:08,856 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-07-22 09:18:08,858 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-07-22 09:18:08,890 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:18:16,816 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:18:16,871 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 17205 sites
2025-07-22 09:18:47,148 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=97.009, H2=1.086, H3=-0.248
2025-07-22 09:18:47,193 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 10565 sites
2025-07-22 09:19:05,703 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=50.687, H2=18.321, H3=7.998
2025-07-22 09:19:05,730 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 603 sites
2025-07-22 09:19:06,782 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.346, H2=-6.601, H3=0.900
2025-07-22 09:19:06,810 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1247 sites
2025-07-22 09:19:08,989 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=20.588, H2=4.169, H3=-5.693
2025-07-22 09:19:09,029 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8552 sites
2025-07-22 09:19:24,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=28.297, H2=13.026, H3=2.715
2025-07-22 09:19:24,056 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 9636 sites
2025-07-22 09:19:40,891 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.491, H2=4.739, H3=2.357
2025-07-22 09:19:40,916 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-07-22 09:19:40,916 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-07-22 09:19:40,918 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-07-22 09:19:40,951 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:19:48,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:19:48,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3685 sites
2025-07-22 09:19:55,339 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=73.343, H2=-1.104, H3=-0.804
2025-07-22 09:19:55,388 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13520 sites
2025-07-22 09:20:19,002 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.738, H2=1.524, H3=-0.565
2025-07-22 09:20:19,047 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 10565 sites
2025-07-22 09:20:37,607 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=50.897, H2=16.645, H3=7.427
2025-07-22 09:20:37,634 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 603 sites
2025-07-22 09:20:38,690 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=2.429, H2=-7.469, H3=2.067
2025-07-22 09:20:38,717 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1247 sites
2025-07-22 09:20:40,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.579, H2=4.642, H3=-5.565
2025-07-22 09:20:40,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8552 sites
2025-07-22 09:20:55,906 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=22.258, H2=12.101, H3=2.851
2025-07-22 09:20:55,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 9636 sites
2025-07-22 09:21:12,854 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.487, H2=5.770, H3=2.476
2025-07-22 09:21:12,880 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-07-22 09:21:12,880 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-07-22 09:21:12,881 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-07-22 09:21:12,914 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:21:20,849 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:21:20,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3685 sites
2025-07-22 09:21:27,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=73.343, H2=-1.104, H3=-0.804
2025-07-22 09:21:27,364 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13520 sites
2025-07-22 09:21:51,166 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.738, H2=1.524, H3=-0.565
2025-07-22 09:21:51,197 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2665 sites
2025-07-22 09:21:55,850 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=8.346, H2=-2.963, H3=-9.786
2025-07-22 09:21:55,889 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7900 sites
2025-07-22 09:22:09,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=51.237, H2=16.124, H3=9.757
2025-07-22 09:22:09,782 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 603 sites
2025-07-22 09:22:10,836 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.106, H2=-6.397, H3=1.848
2025-07-22 09:22:10,863 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1247 sites
2025-07-22 09:22:13,046 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=24.178, H2=3.890, H3=-4.792
2025-07-22 09:22:13,086 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8552 sites
2025-07-22 09:22:28,038 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=31.550, H2=11.665, H3=2.670
2025-07-22 09:22:28,081 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 9636 sites
2025-07-22 09:22:45,005 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=20.140, H2=5.634, H3=3.048
2025-07-22 09:22:45,029 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-07-22 09:22:45,030 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-07-22 09:22:45,031 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-07-22 09:22:45,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:22:53,039 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:22:53,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3685 sites
2025-07-22 09:22:59,505 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=73.343, H2=-1.104, H3=-0.804
2025-07-22 09:22:59,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13520 sites
2025-07-22 09:23:23,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.738, H2=1.524, H3=-0.565
2025-07-22 09:23:23,365 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2665 sites
2025-07-22 09:23:28,037 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=8.346, H2=-2.963, H3=-9.786
2025-07-22 09:23:28,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4491 sites
2025-07-22 09:23:35,928 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=52.024, H2=17.035, H3=10.023
2025-07-22 09:23:35,960 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3409 sites
2025-07-22 09:23:41,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=3.865, H2=2.179, H3=2.459
2025-07-22 09:23:41,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 603 sites
2025-07-22 09:23:43,000 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=2.974, H2=-4.745, H3=2.321
2025-07-22 09:23:43,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1247 sites
2025-07-22 09:23:45,208 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=24.228, H2=3.421, H3=-4.924
2025-07-22 09:23:45,248 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8552 sites
2025-07-22 09:24:00,190 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=26.152, H2=16.413, H3=3.383
2025-07-22 09:24:00,233 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 9636 sites
2025-07-22 09:24:17,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=26.351, H2=5.299, H3=1.929
2025-07-22 09:24:17,168 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-07-22 09:24:17,168 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-07-22 09:24:17,170 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-07-22 09:24:17,203 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:24:25,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:24:25,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3685 sites
2025-07-22 09:24:31,622 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=73.343, H2=-1.104, H3=-0.804
2025-07-22 09:24:31,671 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13520 sites
2025-07-22 09:24:55,540 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.738, H2=1.524, H3=-0.565
2025-07-22 09:24:55,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2665 sites
2025-07-22 09:25:00,259 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=8.346, H2=-2.963, H3=-9.786
2025-07-22 09:25:00,293 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4491 sites
2025-07-22 09:25:08,196 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=52.024, H2=17.035, H3=10.023
2025-07-22 09:25:08,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3409 sites
2025-07-22 09:25:14,252 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=3.865, H2=2.179, H3=2.459
2025-07-22 09:25:14,279 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 603 sites
2025-07-22 09:25:15,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=2.974, H2=-4.745, H3=2.321
2025-07-22 09:25:15,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1247 sites
2025-07-22 09:25:17,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=24.228, H2=3.421, H3=-4.924
2025-07-22 09:25:17,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8552 sites
2025-07-22 09:25:32,719 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=26.152, H2=16.413, H3=3.383
2025-07-22 09:25:32,751 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3536 sites
2025-07-22 09:25:38,997 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=11.271, H2=0.596, H3=-2.089
2025-07-22 09:25:39,033 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6100 sites
2025-07-22 09:25:49,695 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=12.190, H2=7.065, H3=5.177
2025-07-22 09:25:49,719 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-07-22 09:25:49,720 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-07-22 09:25:49,721 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-07-22 09:25:49,754 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4551 sites
2025-07-22 09:25:57,722 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=12.154, H2=0.797, H3=-1.121
2025-07-22 09:25:57,754 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3685 sites
2025-07-22 09:26:04,182 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=73.343, H2=-1.104, H3=-0.804
2025-07-22 09:26:04,217 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 5863 sites
2025-07-22 09:26:14,496 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=26.193, H2=3.223, H3=0.248
2025-07-22 09:26:14,535 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7657 sites
2025-07-22 09:26:27,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=73.472, H2=-1.278, H3=-1.942
2025-07-22 09:26:27,988 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2665 sites
2025-07-22 09:26:32,630 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.499, H2=-2.482, H3=-6.113
2025-07-22 09:26:32,663 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4491 sites
2025-07-22 09:26:40,503 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=35.048, H2=15.014, H3=11.630
2025-07-22 09:26:40,535 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3409 sites
2025-07-22 09:26:46,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=4.073, H2=2.107, H3=3.113
2025-07-22 09:26:46,520 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 603 sites
2025-07-22 09:26:47,574 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.260, H2=-7.428, H3=1.323
2025-07-22 09:26:47,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1247 sites
2025-07-22 09:26:49,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.785, H2=3.950, H3=-4.369
2025-07-22 09:26:49,818 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 8552 sites
2025-07-22 09:27:04,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=28.919, H2=14.455, H3=2.327
2025-07-22 09:27:04,887 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3536 sites
2025-07-22 09:27:11,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=7.984, H2=0.531, H3=-1.957
2025-07-22 09:27:11,125 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6100 sites
2025-07-22 09:27:21,837 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.763, H2=6.826, H3=4.161
2025-07-22 09:27:21,862 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-07-22 09:27:21,862 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-07-22 09:27:21,863 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-07-22 09:27:21,893 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:27:26,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:27:26,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:27:29,835 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:27:29,867 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-07-22 09:27:36,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=84.820, H2=-1.912, H3=-1.036
2025-07-22 09:27:36,350 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5863 sites
2025-07-22 09:27:46,604 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=28.620, H2=2.213, H3=-0.160
2025-07-22 09:27:46,643 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 7657 sites
2025-07-22 09:27:59,975 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=89.074, H2=-1.249, H3=-1.378
2025-07-22 09:28:00,006 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2665 sites
2025-07-22 09:28:04,668 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=7.420, H2=-1.892, H3=-6.030
2025-07-22 09:28:04,702 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4491 sites
2025-07-22 09:28:12,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=34.127, H2=16.604, H3=11.907
2025-07-22 09:28:12,608 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3409 sites
2025-07-22 09:28:18,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.394, H2=1.916, H3=2.126
2025-07-22 09:28:18,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 603 sites
2025-07-22 09:28:19,644 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.802, H2=-7.674, H3=1.366
2025-07-22 09:28:19,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1247 sites
2025-07-22 09:28:21,845 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.700, H2=4.049, H3=-4.155
2025-07-22 09:28:21,886 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 8552 sites
2025-07-22 09:28:36,916 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=29.094, H2=14.840, H3=2.957
2025-07-22 09:28:36,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3536 sites
2025-07-22 09:28:43,126 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.858, H2=0.837, H3=-2.025
2025-07-22 09:28:43,162 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 6100 sites
2025-07-22 09:28:53,905 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=12.956, H2=5.015, H3=4.743
2025-07-22 09:28:53,930 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-07-22 09:28:53,931 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-07-22 09:28:53,932 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-07-22 09:28:53,962 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:28:58,223 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:28:58,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:29:01,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:29:01,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-07-22 09:29:08,424 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=84.820, H2=-1.912, H3=-1.036
2025-07-22 09:29:08,460 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5863 sites
2025-07-22 09:29:18,728 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=28.620, H2=2.213, H3=-0.160
2025-07-22 09:29:18,761 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3540 sites
2025-07-22 09:29:24,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=33.241, H2=-6.385, H3=-4.369
2025-07-22 09:29:25,009 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4117 sites
2025-07-22 09:29:32,211 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=59.008, H2=1.190, H3=2.401
2025-07-22 09:29:32,242 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2665 sites
2025-07-22 09:29:36,897 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.876, H2=-2.241, H3=-9.474
2025-07-22 09:29:36,931 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4491 sites
2025-07-22 09:29:44,733 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=40.326, H2=16.137, H3=11.759
2025-07-22 09:29:44,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3409 sites
2025-07-22 09:29:50,718 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.739, H2=2.120, H3=2.043
2025-07-22 09:29:50,744 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 603 sites
2025-07-22 09:29:51,796 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.264, H2=-6.993, H3=0.888
2025-07-22 09:29:51,824 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1247 sites
2025-07-22 09:29:53,999 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=20.411, H2=3.216, H3=-6.980
2025-07-22 09:29:54,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 8552 sites
2025-07-22 09:30:08,961 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=28.069, H2=15.703, H3=3.562
2025-07-22 09:30:08,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3536 sites
2025-07-22 09:30:15,183 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=9.871, H2=0.496, H3=-2.055
2025-07-22 09:30:15,219 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 6100 sites
2025-07-22 09:30:25,890 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=16.456, H2=3.995, H3=5.126
2025-07-22 09:30:25,915 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-07-22 09:30:25,916 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-07-22 09:30:25,917 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-07-22 09:30:25,947 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:30:30,210 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:30:30,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:30:33,931 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:30:33,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-07-22 09:30:40,452 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=84.820, H2=-1.912, H3=-1.036
2025-07-22 09:30:40,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5863 sites
2025-07-22 09:30:50,734 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=28.620, H2=2.213, H3=-0.160
2025-07-22 09:30:50,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3540 sites
2025-07-22 09:30:56,974 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=33.241, H2=-6.385, H3=-4.369
2025-07-22 09:30:57,007 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4117 sites
2025-07-22 09:31:04,218 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=59.008, H2=1.190, H3=2.401
2025-07-22 09:31:04,248 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2665 sites
2025-07-22 09:31:08,930 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.876, H2=-2.241, H3=-9.474
2025-07-22 09:31:08,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4491 sites
2025-07-22 09:31:16,794 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=40.326, H2=16.137, H3=11.759
2025-07-22 09:31:16,826 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3409 sites
2025-07-22 09:31:22,782 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.739, H2=2.120, H3=2.043
2025-07-22 09:31:22,808 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 603 sites
2025-07-22 09:31:23,860 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.264, H2=-6.993, H3=0.888
2025-07-22 09:31:23,887 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1247 sites
2025-07-22 09:31:26,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=20.411, H2=3.216, H3=-6.980
2025-07-22 09:31:26,118 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3868 sites
2025-07-22 09:31:32,952 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=17.211, H2=9.297, H3=5.730
2025-07-22 09:31:32,986 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4684 sites
2025-07-22 09:31:41,260 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=17.984, H2=9.475, H3=-2.934
2025-07-22 09:31:41,292 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3536 sites
2025-07-22 09:31:47,526 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=12.088, H2=0.249, H3=-1.822
2025-07-22 09:31:47,562 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 6100 sites
2025-07-22 09:31:58,437 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=19.195, H2=5.281, H3=3.940
2025-07-22 09:31:58,462 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-07-22 09:31:58,462 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-07-22 09:31:58,463 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-07-22 09:31:58,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:32:02,797 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:32:02,826 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:32:06,573 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:32:06,605 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3685 sites
2025-07-22 09:32:13,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=84.820, H2=-1.912, H3=-1.036
2025-07-22 09:32:13,171 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5863 sites
2025-07-22 09:32:23,515 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=28.620, H2=2.213, H3=-0.160
2025-07-22 09:32:23,547 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3540 sites
2025-07-22 09:32:29,755 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=33.241, H2=-6.385, H3=-4.369
2025-07-22 09:32:29,788 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4117 sites
2025-07-22 09:32:36,974 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=59.008, H2=1.190, H3=2.401
2025-07-22 09:32:37,001 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 824 sites
2025-07-22 09:32:38,443 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-8.041, H2=-5.391, H3=-10.411
2025-07-22 09:32:38,471 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1841 sites
2025-07-22 09:32:41,676 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=13.036, H2=-0.081, H3=-4.726
2025-07-22 09:32:41,709 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4491 sites
2025-07-22 09:32:49,625 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=47.409, H2=17.572, H3=11.089
2025-07-22 09:32:49,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3409 sites
2025-07-22 09:32:55,623 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.939, H2=2.425, H3=1.529
2025-07-22 09:32:55,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 603 sites
2025-07-22 09:32:56,699 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.148, H2=-6.252, H3=1.791
2025-07-22 09:32:56,726 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1247 sites
2025-07-22 09:32:58,896 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=38.199, H2=3.675, H3=-5.708
2025-07-22 09:32:58,928 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3868 sites
2025-07-22 09:33:05,674 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=12.752, H2=7.580, H3=4.842
2025-07-22 09:33:05,707 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4684 sites
2025-07-22 09:33:13,824 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=25.610, H2=9.551, H3=-2.209
2025-07-22 09:33:13,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3536 sites
2025-07-22 09:33:20,024 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=16.361, H2=0.522, H3=-1.613
2025-07-22 09:33:20,061 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 6100 sites
2025-07-22 09:33:30,734 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=13.658, H2=6.537, H3=4.646
2025-07-22 09:33:30,760 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-07-22 09:33:30,760 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-07-22 09:33:30,761 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-07-22 09:33:30,791 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:33:35,034 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:33:35,064 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:33:38,749 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:33:38,777 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1253 sites
2025-07-22 09:33:40,951 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.822, H2=-2.843, H3=-0.518
2025-07-22 09:33:40,980 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2432 sites
2025-07-22 09:33:45,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=44.504, H2=-0.751, H3=-0.878
2025-07-22 09:33:45,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 5863 sites
2025-07-22 09:33:55,523 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=25.319, H2=1.978, H3=-0.395
2025-07-22 09:33:55,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3540 sites
2025-07-22 09:34:01,732 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=23.828, H2=-5.300, H3=-3.814
2025-07-22 09:34:01,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4117 sites
2025-07-22 09:34:08,945 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=58.173, H2=1.505, H3=2.499
2025-07-22 09:34:08,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 824 sites
2025-07-22 09:34:10,410 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-6.578, H2=-6.533, H3=-7.891
2025-07-22 09:34:10,439 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1841 sites
2025-07-22 09:34:13,648 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.794, H2=-0.425, H3=-4.139
2025-07-22 09:34:13,681 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4491 sites
2025-07-22 09:34:21,512 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=40.514, H2=15.321, H3=8.531
2025-07-22 09:34:21,543 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3409 sites
2025-07-22 09:34:27,508 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.195, H2=2.230, H3=2.653
2025-07-22 09:34:27,535 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 603 sites
2025-07-22 09:34:28,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.177, H2=-8.073, H3=1.331
2025-07-22 09:34:28,611 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1247 sites
2025-07-22 09:34:30,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=17.877, H2=4.449, H3=-5.344
2025-07-22 09:34:30,813 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3868 sites
2025-07-22 09:34:37,571 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=14.917, H2=7.550, H3=5.542
2025-07-22 09:34:37,605 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4684 sites
2025-07-22 09:34:45,794 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=21.191, H2=9.862, H3=-2.147
2025-07-22 09:34:45,826 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3536 sites
2025-07-22 09:34:51,981 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=10.231, H2=0.529, H3=-2.437
2025-07-22 09:34:52,017 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 6100 sites
2025-07-22 09:35:02,657 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.265, H2=4.841, H3=4.864
2025-07-22 09:35:02,683 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-07-22 09:35:02,683 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-07-22 09:35:02,684 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-07-22 09:35:02,714 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:35:06,962 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:35:06,992 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:35:10,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:35:10,693 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1253 sites
2025-07-22 09:35:12,865 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.822, H2=-2.843, H3=-0.518
2025-07-22 09:35:12,895 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2432 sites
2025-07-22 09:35:17,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=44.504, H2=-0.751, H3=-0.878
2025-07-22 09:35:17,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3458 sites
2025-07-22 09:35:23,224 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.858, H2=-0.330, H3=-3.209
2025-07-22 09:35:23,254 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2405 sites
2025-07-22 09:35:27,494 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=18.028, H2=3.823, H3=3.070
2025-07-22 09:35:27,525 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3540 sites
2025-07-22 09:35:33,752 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.144, H2=-5.713, H3=-4.578
2025-07-22 09:35:33,785 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4117 sites
2025-07-22 09:35:40,998 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=63.680, H2=1.069, H3=2.246
2025-07-22 09:35:41,025 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 824 sites
2025-07-22 09:35:42,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-9.822, H2=-8.039, H3=-8.456
2025-07-22 09:35:42,496 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1841 sites
2025-07-22 09:35:45,733 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=15.326, H2=-0.394, H3=-4.073
2025-07-22 09:35:45,767 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4491 sites
2025-07-22 09:35:53,630 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=34.858, H2=18.774, H3=11.136
2025-07-22 09:35:53,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3409 sites
2025-07-22 09:35:59,618 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.777, H2=2.630, H3=3.436
2025-07-22 09:35:59,645 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 603 sites
2025-07-22 09:36:00,694 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.282, H2=-8.098, H3=1.416
2025-07-22 09:36:00,722 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1247 sites
2025-07-22 09:36:02,899 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=17.744, H2=3.635, H3=-5.449
2025-07-22 09:36:02,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3868 sites
2025-07-22 09:36:09,678 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=19.937, H2=8.265, H3=4.463
2025-07-22 09:36:09,712 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4684 sites
2025-07-22 09:36:17,871 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=17.701, H2=10.502, H3=-2.228
2025-07-22 09:36:17,903 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3536 sites
2025-07-22 09:36:24,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=9.461, H2=0.377, H3=-2.089
2025-07-22 09:36:24,124 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 6100 sites
2025-07-22 09:36:34,916 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=15.048, H2=5.207, H3=5.394
2025-07-22 09:36:34,941 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-07-22 09:36:34,941 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-07-22 09:36:34,943 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-07-22 09:36:34,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:36:39,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:36:39,271 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:36:42,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:36:43,007 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1253 sites
2025-07-22 09:36:45,211 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.822, H2=-2.843, H3=-0.518
2025-07-22 09:36:45,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2432 sites
2025-07-22 09:36:49,548 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=44.504, H2=-0.751, H3=-0.878
2025-07-22 09:36:49,580 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3458 sites
2025-07-22 09:36:55,686 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.858, H2=-0.330, H3=-3.209
2025-07-22 09:36:55,716 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2405 sites
2025-07-22 09:36:59,981 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=18.028, H2=3.823, H3=3.070
2025-07-22 09:37:00,013 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3540 sites
2025-07-22 09:37:06,315 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.144, H2=-5.713, H3=-4.578
2025-07-22 09:37:06,348 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4117 sites
2025-07-22 09:37:13,611 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=63.680, H2=1.069, H3=2.246
2025-07-22 09:37:13,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 824 sites
2025-07-22 09:37:15,081 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-9.822, H2=-8.039, H3=-8.456
2025-07-22 09:37:15,109 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1841 sites
2025-07-22 09:37:18,364 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=15.326, H2=-0.394, H3=-4.073
2025-07-22 09:37:18,397 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4491 sites
2025-07-22 09:37:26,272 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=34.858, H2=18.774, H3=11.136
2025-07-22 09:37:26,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3409 sites
2025-07-22 09:37:32,304 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.777, H2=2.630, H3=3.436
2025-07-22 09:37:32,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 603 sites
2025-07-22 09:37:33,385 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.282, H2=-8.098, H3=1.416
2025-07-22 09:37:33,411 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 365 sites
2025-07-22 09:37:34,048 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.123, H2=-0.405, H3=-1.578
2025-07-22 09:37:34,075 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 882 sites
2025-07-22 09:37:35,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=36.749, H2=-0.910, H3=-6.858
2025-07-22 09:37:35,636 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3868 sites
2025-07-22 09:37:42,379 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=13.339, H2=8.387, H3=3.646
2025-07-22 09:37:42,414 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4684 sites
2025-07-22 09:37:50,556 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=19.020, H2=6.807, H3=-2.444
2025-07-22 09:37:50,588 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3536 sites
2025-07-22 09:37:56,716 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.673, H2=0.720, H3=-1.671
2025-07-22 09:37:56,752 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 6100 sites
2025-07-22 09:38:07,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=14.236, H2=4.376, H3=4.342
2025-07-22 09:38:07,381 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-07-22 09:38:07,381 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-07-22 09:38:07,383 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-07-22 09:38:07,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:38:11,651 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:38:11,680 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:38:15,363 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:38:15,391 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1253 sites
2025-07-22 09:38:17,571 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.822, H2=-2.843, H3=-0.518
2025-07-22 09:38:17,601 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2432 sites
2025-07-22 09:38:21,825 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=44.504, H2=-0.751, H3=-0.878
2025-07-22 09:38:21,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3458 sites
2025-07-22 09:38:27,904 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.858, H2=-0.330, H3=-3.209
2025-07-22 09:38:27,934 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2405 sites
2025-07-22 09:38:32,140 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=18.028, H2=3.823, H3=3.070
2025-07-22 09:38:32,172 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3540 sites
2025-07-22 09:38:38,338 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.144, H2=-5.713, H3=-4.578
2025-07-22 09:38:38,371 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4117 sites
2025-07-22 09:38:45,500 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=63.680, H2=1.069, H3=2.246
2025-07-22 09:38:45,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 824 sites
2025-07-22 09:38:46,961 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-9.822, H2=-8.039, H3=-8.456
2025-07-22 09:38:46,989 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1841 sites
2025-07-22 09:38:50,187 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=15.326, H2=-0.394, H3=-4.073
2025-07-22 09:38:50,221 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4491 sites
2025-07-22 09:38:58,038 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=34.858, H2=18.774, H3=11.136
2025-07-22 09:38:58,069 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3409 sites
2025-07-22 09:39:03,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.777, H2=2.630, H3=3.436
2025-07-22 09:39:03,984 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 603 sites
2025-07-22 09:39:05,037 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.282, H2=-8.098, H3=1.416
2025-07-22 09:39:05,063 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 365 sites
2025-07-22 09:39:05,698 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.123, H2=-0.405, H3=-1.578
2025-07-22 09:39:05,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 882 sites
2025-07-22 09:39:07,258 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=36.749, H2=-0.910, H3=-6.858
2025-07-22 09:39:07,291 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3868 sites
2025-07-22 09:39:14,026 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=13.339, H2=8.387, H3=3.646
2025-07-22 09:39:14,060 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4684 sites
2025-07-22 09:39:22,225 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=19.020, H2=6.807, H3=-2.444
2025-07-22 09:39:22,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3536 sites
2025-07-22 09:39:28,441 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.673, H2=0.720, H3=-1.671
2025-07-22 09:39:28,470 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2024 sites
2025-07-22 09:39:31,983 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-1.272, H2=0.340, H3=3.721
2025-07-22 09:39:32,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 4076 sites
2025-07-22 09:39:39,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=11.103, H2=5.960, H3=3.573
2025-07-22 09:39:39,119 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-07-22 09:39:39,119 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-07-22 09:39:39,121 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-07-22 09:39:39,151 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:39:43,398 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:39:43,427 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:39:47,123 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:39:47,151 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1253 sites
2025-07-22 09:39:49,340 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.822, H2=-2.843, H3=-0.518
2025-07-22 09:39:49,370 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2432 sites
2025-07-22 09:39:53,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=44.504, H2=-0.751, H3=-0.878
2025-07-22 09:39:53,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3458 sites
2025-07-22 09:39:59,654 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.858, H2=-0.330, H3=-3.209
2025-07-22 09:39:59,684 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2405 sites
2025-07-22 09:40:03,880 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=18.028, H2=3.823, H3=3.070
2025-07-22 09:40:03,912 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3540 sites
2025-07-22 09:40:10,067 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.144, H2=-5.713, H3=-4.578
2025-07-22 09:40:10,100 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4117 sites
2025-07-22 09:40:17,247 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=63.680, H2=1.069, H3=2.246
2025-07-22 09:40:17,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 824 sites
2025-07-22 09:40:18,702 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-9.822, H2=-8.039, H3=-8.456
2025-07-22 09:40:18,730 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1841 sites
2025-07-22 09:40:21,945 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=15.326, H2=-0.394, H3=-4.073
2025-07-22 09:40:21,978 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4491 sites
2025-07-22 09:40:29,825 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=34.858, H2=18.774, H3=11.136
2025-07-22 09:40:29,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3409 sites
2025-07-22 09:40:35,806 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.777, H2=2.630, H3=3.436
2025-07-22 09:40:35,833 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 603 sites
2025-07-22 09:40:36,880 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.282, H2=-8.098, H3=1.416
2025-07-22 09:40:36,906 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 365 sites
2025-07-22 09:40:37,540 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.123, H2=-0.405, H3=-1.578
2025-07-22 09:40:37,567 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 882 sites
2025-07-22 09:40:39,104 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=36.749, H2=-0.910, H3=-6.858
2025-07-22 09:40:39,137 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3868 sites
2025-07-22 09:40:45,879 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=13.339, H2=8.387, H3=3.646
2025-07-22 09:40:45,907 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1227 sites
2025-07-22 09:40:48,056 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.979, H2=-3.768, H3=-7.358
2025-07-22 09:40:48,088 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3457 sites
2025-07-22 09:40:54,139 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=24.438, H2=9.225, H3=1.847
2025-07-22 09:40:54,171 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3536 sites
2025-07-22 09:41:00,339 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.723, H2=0.321, H3=-2.424
2025-07-22 09:41:00,369 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2024 sites
2025-07-22 09:41:03,901 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-0.948, H2=0.433, H3=4.460
2025-07-22 09:41:03,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 4076 sites
2025-07-22 09:41:11,004 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=12.486, H2=6.888, H3=3.816
2025-07-22 09:41:11,029 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-07-22 09:41:11,030 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-07-22 09:41:11,031 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-07-22 09:41:11,061 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:41:15,330 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:41:15,360 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:41:19,112 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:41:19,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1253 sites
2025-07-22 09:41:21,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.822, H2=-2.843, H3=-0.518
2025-07-22 09:41:21,385 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2432 sites
2025-07-22 09:41:25,700 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=44.504, H2=-0.751, H3=-0.878
2025-07-22 09:41:25,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3458 sites
2025-07-22 09:41:31,868 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.858, H2=-0.330, H3=-3.209
2025-07-22 09:41:31,898 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2405 sites
2025-07-22 09:41:36,126 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=18.028, H2=3.823, H3=3.070
2025-07-22 09:41:36,158 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3540 sites
2025-07-22 09:41:42,379 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.144, H2=-5.713, H3=-4.578
2025-07-22 09:41:42,412 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4117 sites
2025-07-22 09:41:49,664 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=63.680, H2=1.069, H3=2.246
2025-07-22 09:41:49,691 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 824 sites
2025-07-22 09:41:51,136 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-9.822, H2=-8.039, H3=-8.456
2025-07-22 09:41:51,164 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1841 sites
2025-07-22 09:41:54,373 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=15.326, H2=-0.394, H3=-4.073
2025-07-22 09:41:54,406 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4491 sites
2025-07-22 09:42:02,293 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=34.858, H2=18.774, H3=11.136
2025-07-22 09:42:02,322 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2083 sites
2025-07-22 09:42:05,972 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=2.733, H2=3.725, H3=4.252
2025-07-22 09:42:06,000 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1326 sites
2025-07-22 09:42:08,280 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-8.106, H2=-5.212, H3=-2.802
2025-07-22 09:42:08,306 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 603 sites
2025-07-22 09:42:09,343 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.634, H2=-7.869, H3=1.438
2025-07-22 09:42:09,369 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 365 sites
2025-07-22 09:42:10,000 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-3.541, H2=0.300, H3=-0.842
2025-07-22 09:42:10,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 882 sites
2025-07-22 09:42:11,560 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=24.633, H2=-1.418, H3=-6.653
2025-07-22 09:42:11,593 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3868 sites
2025-07-22 09:42:18,319 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=16.180, H2=6.363, H3=4.746
2025-07-22 09:42:18,346 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1227 sites
2025-07-22 09:42:20,471 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-1.200, H2=-4.762, H3=-8.382
2025-07-22 09:42:20,501 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3457 sites
2025-07-22 09:42:26,499 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=20.165, H2=10.302, H3=1.174
2025-07-22 09:42:26,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3536 sites
2025-07-22 09:42:32,672 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=9.887, H2=0.167, H3=-2.487
2025-07-22 09:42:32,701 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2024 sites
2025-07-22 09:42:36,209 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-1.263, H2=0.338, H3=3.533
2025-07-22 09:42:36,241 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 4076 sites
2025-07-22 09:42:43,311 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=13.260, H2=6.863, H3=4.671
2025-07-22 09:42:43,336 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-07-22 09:42:43,336 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-07-22 09:42:43,337 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-07-22 09:42:43,366 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:42:47,615 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:42:47,644 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2116 sites
2025-07-22 09:42:51,301 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=6.478, H2=2.146, H3=-0.253
2025-07-22 09:42:51,329 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1253 sites
2025-07-22 09:42:53,501 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.822, H2=-2.843, H3=-0.518
2025-07-22 09:42:53,531 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2432 sites
2025-07-22 09:42:57,762 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=44.504, H2=-0.751, H3=-0.878
2025-07-22 09:42:57,794 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3458 sites
2025-07-22 09:43:03,799 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.858, H2=-0.330, H3=-3.209
2025-07-22 09:43:03,829 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2405 sites
2025-07-22 09:43:07,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=18.028, H2=3.823, H3=3.070
2025-07-22 09:43:08,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3540 sites
2025-07-22 09:43:14,191 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.144, H2=-5.713, H3=-4.578
2025-07-22 09:43:14,224 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4117 sites
2025-07-22 09:43:21,346 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=63.680, H2=1.069, H3=2.246
2025-07-22 09:43:21,373 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 824 sites
2025-07-22 09:43:22,795 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-9.822, H2=-8.039, H3=-8.456
2025-07-22 09:43:22,824 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1841 sites
2025-07-22 09:43:26,005 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=15.326, H2=-0.394, H3=-4.073
2025-07-22 09:43:26,035 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2482 sites
2025-07-22 09:43:30,348 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.061, H2=2.360, H3=10.697
2025-07-22 09:43:30,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2009 sites
2025-07-22 09:43:33,860 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=32.330, H2=10.488, H3=1.580
2025-07-22 09:43:33,889 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2083 sites
2025-07-22 09:43:37,499 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.741, H2=3.401, H3=2.808
2025-07-22 09:43:37,526 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1326 sites
2025-07-22 09:43:39,824 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-7.467, H2=-4.000, H3=-2.795
2025-07-22 09:43:39,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 603 sites
2025-07-22 09:43:40,899 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.944, H2=-6.667, H3=1.572
2025-07-22 09:43:40,925 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 365 sites
2025-07-22 09:43:41,561 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-4.023, H2=0.211, H3=-1.047
2025-07-22 09:43:41,588 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 882 sites
2025-07-22 09:43:43,115 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=26.707, H2=-1.005, H3=-7.031
2025-07-22 09:43:43,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3868 sites
2025-07-22 09:43:49,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=13.559, H2=7.805, H3=4.322
2025-07-22 09:43:49,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1227 sites
2025-07-22 09:43:52,041 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-1.520, H2=-4.582, H3=-8.788
2025-07-22 09:43:52,072 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3457 sites
2025-07-22 09:43:58,072 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=21.333, H2=9.231, H3=1.466
2025-07-22 09:43:58,104 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3536 sites
2025-07-22 09:44:04,259 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=12.151, H2=0.453, H3=-2.361
2025-07-22 09:44:04,288 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2024 sites
2025-07-22 09:44:07,835 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-1.260, H2=0.972, H3=4.761
2025-07-22 09:44:07,867 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 4076 sites
2025-07-22 09:44:14,992 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=11.823, H2=6.200, H3=3.028
2025-07-22 09:44:15,017 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-07-22 09:44:15,017 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-07-22 09:44:15,019 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-07-22 09:44:15,048 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:44:19,266 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:44:19,293 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 765 sites
2025-07-22 09:44:20,618 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-0.530, H2=1.290, H3=-1.538
2025-07-22 09:44:20,646 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1351 sites
2025-07-22 09:44:23,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.440, H2=0.246, H3=0.730
2025-07-22 09:44:23,041 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1253 sites
2025-07-22 09:44:25,222 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=13.491, H2=-2.471, H3=-0.819
2025-07-22 09:44:25,252 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2432 sites
2025-07-22 09:44:29,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=38.860, H2=-1.345, H3=-0.550
2025-07-22 09:44:29,520 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3458 sites
2025-07-22 09:44:35,580 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.914, H2=0.462, H3=-2.840
2025-07-22 09:44:35,610 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2405 sites
2025-07-22 09:44:39,797 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.254, H2=3.169, H3=3.421
2025-07-22 09:44:39,829 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3540 sites
2025-07-22 09:44:45,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=23.934, H2=-5.034, H3=-5.246
2025-07-22 09:44:46,008 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4117 sites
2025-07-22 09:44:53,126 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=122.554, H2=0.769, H3=2.257
2025-07-22 09:44:53,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 824 sites
2025-07-22 09:44:54,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-7.937, H2=-6.403, H3=-6.606
2025-07-22 09:44:54,612 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1841 sites
2025-07-22 09:44:57,811 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=11.802, H2=-0.006, H3=-2.773
2025-07-22 09:44:57,840 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2482 sites
2025-07-22 09:45:02,164 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.840, H2=1.977, H3=11.981
2025-07-22 09:45:02,193 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2009 sites
2025-07-22 09:45:05,695 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=27.207, H2=10.329, H3=0.803
2025-07-22 09:45:05,724 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2083 sites
2025-07-22 09:45:09,396 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.758, H2=3.410, H3=4.004
2025-07-22 09:45:09,424 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1326 sites
2025-07-22 09:45:11,776 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-12.014, H2=-5.739, H3=-2.692
2025-07-22 09:45:11,802 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 603 sites
2025-07-22 09:45:12,868 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.920, H2=-8.920, H3=1.573
2025-07-22 09:45:12,894 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 365 sites
2025-07-22 09:45:13,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-4.821, H2=0.102, H3=-0.979
2025-07-22 09:45:13,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 882 sites
2025-07-22 09:45:15,141 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=20.284, H2=-1.177, H3=-7.573
2025-07-22 09:45:15,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3868 sites
2025-07-22 09:45:21,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=10.835, H2=6.529, H3=4.299
2025-07-22 09:45:22,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1227 sites
2025-07-22 09:45:24,167 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-1.101, H2=-4.973, H3=-8.849
2025-07-22 09:45:24,198 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3457 sites
2025-07-22 09:45:30,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=22.599, H2=14.563, H3=1.885
2025-07-22 09:45:30,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3536 sites
2025-07-22 09:45:36,545 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=10.907, H2=0.699, H3=-2.342
2025-07-22 09:45:36,574 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2024 sites
2025-07-22 09:45:40,102 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-0.706, H2=0.843, H3=2.879
2025-07-22 09:45:40,134 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 4076 sites
2025-07-22 09:45:47,214 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=13.302, H2=6.392, H3=7.438
2025-07-22 09:45:47,239 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-07-22 09:45:47,239 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-07-22 09:45:47,241 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-07-22 09:45:47,271 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:45:51,558 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:45:51,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 765 sites
2025-07-22 09:45:52,905 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-0.530, H2=1.290, H3=-1.538
2025-07-22 09:45:52,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1351 sites
2025-07-22 09:45:55,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.440, H2=0.246, H3=0.730
2025-07-22 09:45:55,301 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1253 sites
2025-07-22 09:45:57,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=13.491, H2=-2.471, H3=-0.819
2025-07-22 09:45:57,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2432 sites
2025-07-22 09:46:01,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=38.860, H2=-1.345, H3=-0.550
2025-07-22 09:46:01,766 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3458 sites
2025-07-22 09:46:07,775 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.914, H2=0.462, H3=-2.840
2025-07-22 09:46:07,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2405 sites
2025-07-22 09:46:11,953 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.254, H2=3.169, H3=3.421
2025-07-22 09:46:11,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3540 sites
2025-07-22 09:46:18,114 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=23.934, H2=-5.034, H3=-5.246
2025-07-22 09:46:18,142 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1341 sites
2025-07-22 09:46:20,464 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=30.271, H2=-0.359, H3=0.196
2025-07-22 09:46:20,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2776 sites
2025-07-22 09:46:25,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=35.882, H2=0.933, H3=1.063
2025-07-22 09:46:25,345 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 824 sites
2025-07-22 09:46:26,771 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-5.523, H2=-5.401, H3=-7.036
2025-07-22 09:46:26,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1841 sites
2025-07-22 09:46:29,971 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=21.775, H2=-0.157, H3=-5.107
2025-07-22 09:46:30,001 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2482 sites
2025-07-22 09:46:34,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=3.754, H2=1.346, H3=8.553
2025-07-22 09:46:34,336 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2009 sites
2025-07-22 09:46:37,824 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=18.056, H2=10.117, H3=1.088
2025-07-22 09:46:37,854 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2083 sites
2025-07-22 09:46:41,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.865, H2=3.344, H3=2.916
2025-07-22 09:46:41,475 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1326 sites
2025-07-22 09:46:43,770 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-7.191, H2=-5.282, H3=-2.635
2025-07-22 09:46:43,796 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 603 sites
2025-07-22 09:46:44,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.945, H2=-6.542, H3=1.589
2025-07-22 09:46:44,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 365 sites
2025-07-22 09:46:45,495 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-3.947, H2=0.211, H3=-1.246
2025-07-22 09:46:45,522 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 882 sites
2025-07-22 09:46:47,054 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=25.262, H2=-0.913, H3=-10.664
2025-07-22 09:46:47,086 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3868 sites
2025-07-22 09:46:53,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=13.677, H2=7.270, H3=4.075
2025-07-22 09:46:53,809 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1227 sites
2025-07-22 09:46:55,941 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-1.011, H2=-4.874, H3=-7.910
2025-07-22 09:46:55,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3457 sites
2025-07-22 09:47:01,977 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=28.775, H2=10.265, H3=1.838
2025-07-22 09:47:02,009 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3536 sites
2025-07-22 09:47:08,133 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=9.045, H2=0.957, H3=-1.632
2025-07-22 09:47:08,162 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2024 sites
2025-07-22 09:47:11,661 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-0.807, H2=0.867, H3=5.091
2025-07-22 09:47:11,694 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 4076 sites
2025-07-22 09:47:18,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=13.880, H2=6.181, H3=3.425
2025-07-22 09:47:18,754 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-07-22 09:47:18,754 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-07-22 09:47:18,755 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-07-22 09:47:18,784 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2435 sites
2025-07-22 09:47:23,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-2.841, H2=-1.481, H3=-1.399
2025-07-22 09:47:23,078 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 765 sites
2025-07-22 09:47:24,411 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=-0.530, H2=1.290, H3=-1.538
2025-07-22 09:47:24,439 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1351 sites
2025-07-22 09:47:26,800 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=9.440, H2=0.246, H3=0.730
2025-07-22 09:47:26,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1253 sites
2025-07-22 09:47:29,002 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=13.491, H2=-2.471, H3=-0.819
2025-07-22 09:47:29,032 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2432 sites
2025-07-22 09:47:33,263 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=38.860, H2=-1.345, H3=-0.550
2025-07-22 09:47:33,294 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3458 sites
2025-07-22 09:47:39,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.914, H2=0.462, H3=-2.840
2025-07-22 09:47:39,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2405 sites
2025-07-22 09:47:43,456 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.254, H2=3.169, H3=3.421
2025-07-22 09:47:43,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3540 sites
2025-07-22 09:47:49,619 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=23.934, H2=-5.034, H3=-5.246
2025-07-22 09:47:49,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1341 sites
2025-07-22 09:47:51,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=30.271, H2=-0.359, H3=0.196
2025-07-22 09:47:51,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2776 sites
2025-07-22 09:47:56,794 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=35.882, H2=0.933, H3=1.063
2025-07-22 09:47:56,821 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 824 sites
2025-07-22 09:47:58,245 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-5.523, H2=-5.401, H3=-7.036
2025-07-22 09:47:58,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1841 sites
2025-07-22 09:48:01,454 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=21.775, H2=-0.157, H3=-5.107
2025-07-22 09:48:01,484 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2482 sites
2025-07-22 09:48:05,776 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=3.754, H2=1.346, H3=8.553
2025-07-22 09:48:05,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2009 sites
2025-07-22 09:48:09,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=18.056, H2=10.117, H3=1.088
2025-07-22 09:48:09,315 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2083 sites
2025-07-22 09:48:12,912 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.865, H2=3.344, H3=2.916
2025-07-22 09:48:12,940 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1326 sites
2025-07-22 09:48:15,233 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-7.191, H2=-5.282, H3=-2.635
2025-07-22 09:48:15,259 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 345 sites
2025-07-22 09:48:15,857 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-3.696, H2=-6.195, H3=1.564
2025-07-22 09:48:15,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 258 sites
2025-07-22 09:48:16,328 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.940, H2=-3.074, H3=0.342
2025-07-22 09:48:16,354 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 365 sites
2025-07-22 09:48:16,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-4.672, H2=0.544, H3=-1.068
2025-07-22 09:48:17,011 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 882 sites
2025-07-22 09:48:18,540 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=20.944, H2=-1.344, H3=-6.098
2025-07-22 09:48:18,572 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3868 sites
2025-07-22 09:48:25,235 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=16.272, H2=12.437, H3=4.768
2025-07-22 09:48:25,262 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1227 sites
2025-07-22 09:48:27,409 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-1.180, H2=-4.535, H3=-7.787
2025-07-22 09:48:27,440 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3457 sites
2025-07-22 09:48:33,476 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=24.416, H2=12.983, H3=2.052
2025-07-22 09:48:33,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 3536 sites
2025-07-22 09:48:39,688 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=15.881, H2=1.959, H3=-2.349
2025-07-22 09:48:39,717 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2024 sites
2025-07-22 09:48:43,269 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.537, H2=0.360, H3=3.672
2025-07-22 09:48:43,302 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 4076 sites
2025-07-22 09:48:50,469 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=14.350, H2=7.490, H3=4.814
2025-07-22 09:48:50,493 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-07-22 09:48:50,493 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-07-22 09:48:50,495 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-07-22 09:48:50,522 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 09:48:52,521 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 09:48:52,549 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 09:48:54,842 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 09:48:54,868 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 09:48:56,208 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 09:48:56,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 09:48:58,612 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 09:48:58,639 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 09:49:00,836 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 09:49:00,866 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2432 sites
2025-07-22 09:49:05,124 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=30.656, H2=-0.375, H3=-0.183
2025-07-22 09:49:05,156 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3458 sites
2025-07-22 09:49:11,185 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=18.175, H2=0.560, H3=-3.531
2025-07-22 09:49:11,215 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2405 sites
2025-07-22 09:49:15,419 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=18.522, H2=2.461, H3=2.228
2025-07-22 09:49:15,451 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3540 sites
2025-07-22 09:49:21,598 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=25.064, H2=-6.006, H3=-4.633
2025-07-22 09:49:21,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1341 sites
2025-07-22 09:49:23,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=25.969, H2=-0.405, H3=-0.126
2025-07-22 09:49:24,006 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2776 sites
2025-07-22 09:49:28,893 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=25.420, H2=0.659, H3=1.511
2025-07-22 09:49:28,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 824 sites
2025-07-22 09:49:30,363 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-11.090, H2=-6.693, H3=-5.886
2025-07-22 09:49:30,392 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1841 sites
2025-07-22 09:49:33,624 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=17.566, H2=-0.498, H3=-4.725
2025-07-22 09:49:33,654 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2482 sites
2025-07-22 09:49:38,014 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.447, H2=1.399, H3=8.464
2025-07-22 09:49:38,043 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2009 sites
2025-07-22 09:49:41,569 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=20.012, H2=10.473, H3=0.954
2025-07-22 09:49:41,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2083 sites
2025-07-22 09:49:45,236 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=1.117, H2=3.672, H3=4.407
2025-07-22 09:49:45,263 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1326 sites
2025-07-22 09:49:47,576 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-7.082, H2=-4.184, H3=-2.834
2025-07-22 09:49:47,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 345 sites
2025-07-22 09:49:48,200 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-3.706, H2=-7.007, H3=1.604
2025-07-22 09:49:48,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 258 sites
2025-07-22 09:49:48,677 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=4.555, H2=-3.225, H3=0.198
2025-07-22 09:49:48,703 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 365 sites
2025-07-22 09:49:49,339 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-3.439, H2=0.454, H3=-1.783
2025-07-22 09:49:49,366 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 882 sites
2025-07-22 09:49:50,911 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=28.479, H2=-1.292, H3=-6.834
2025-07-22 09:49:50,943 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3868 sites
2025-07-22 09:49:57,686 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=15.765, H2=8.656, H3=6.291
2025-07-22 09:49:57,714 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1227 sites
2025-07-22 09:49:59,859 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-0.984, H2=-4.850, H3=-5.724
2025-07-22 09:49:59,891 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 3457 sites
2025-07-22 09:50:05,925 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=29.528, H2=11.728, H3=2.700
2025-07-22 09:50:05,956 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 3536 sites
2025-07-22 09:50:12,115 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=10.744, H2=0.415, H3=-1.957
2025-07-22 09:50:12,143 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2024 sites
2025-07-22 09:50:15,678 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-1.899, H2=0.494, H3=3.902
2025-07-22 09:50:15,711 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 4076 sites
2025-07-22 09:50:22,846 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=20.032, H2=5.620, H3=3.061
2025-07-22 09:50:22,872 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-07-22 09:50:22,872 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-07-22 09:50:22,873 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-07-22 09:50:22,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 09:50:24,880 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 09:50:24,908 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 09:50:27,167 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 09:50:27,194 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 09:50:28,524 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 09:50:28,552 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 09:50:30,909 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 09:50:30,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 09:50:33,121 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 09:50:33,148 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 09:50:34,384 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 09:50:34,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 09:50:37,411 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 09:50:37,443 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 09:50:43,465 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 09:50:43,495 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2405 sites
2025-07-22 09:50:47,690 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.072, H2=2.918, H3=2.298
2025-07-22 09:50:47,722 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3540 sites
2025-07-22 09:50:53,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=28.311, H2=-6.298, H3=-4.399
2025-07-22 09:50:53,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1341 sites
2025-07-22 09:50:56,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=20.435, H2=-0.148, H3=0.670
2025-07-22 09:50:56,272 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2776 sites
2025-07-22 09:51:01,114 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=43.056, H2=0.971, H3=1.981
2025-07-22 09:51:01,141 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 824 sites
2025-07-22 09:51:02,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-5.087, H2=-4.999, H3=-5.821
2025-07-22 09:51:02,606 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1841 sites
2025-07-22 09:51:05,802 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=12.450, H2=-0.348, H3=-3.054
2025-07-22 09:51:05,831 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2482 sites
2025-07-22 09:51:10,144 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=6.575, H2=1.935, H3=8.449
2025-07-22 09:51:10,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2009 sites
2025-07-22 09:51:13,660 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=20.001, H2=10.852, H3=0.768
2025-07-22 09:51:13,689 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2083 sites
2025-07-22 09:51:17,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=1.387, H2=3.129, H3=3.807
2025-07-22 09:51:17,335 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1326 sites
2025-07-22 09:51:19,642 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-7.450, H2=-4.316, H3=-2.622
2025-07-22 09:51:19,668 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 345 sites
2025-07-22 09:51:20,266 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-3.333, H2=-4.921, H3=1.534
2025-07-22 09:51:20,292 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 258 sites
2025-07-22 09:51:20,739 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=6.583, H2=-2.808, H3=-0.097
2025-07-22 09:51:20,765 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 365 sites
2025-07-22 09:51:21,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-4.767, H2=0.325, H3=-1.031
2025-07-22 09:51:21,428 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 882 sites
2025-07-22 09:51:22,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=25.988, H2=-0.743, H3=-7.680
2025-07-22 09:51:22,990 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3868 sites
2025-07-22 09:51:29,720 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=13.464, H2=8.894, H3=4.964
2025-07-22 09:51:29,748 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1227 sites
2025-07-22 09:51:31,877 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-1.024, H2=-6.678, H3=-9.016
2025-07-22 09:51:31,908 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 3457 sites
2025-07-22 09:51:37,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=26.519, H2=8.917, H3=1.376
2025-07-22 09:51:37,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 3536 sites
2025-07-22 09:51:44,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=12.245, H2=0.591, H3=-2.362
2025-07-22 09:51:44,179 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2024 sites
2025-07-22 09:51:47,740 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.817, H2=0.523, H3=3.210
2025-07-22 09:51:47,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 4076 sites
2025-07-22 09:51:54,970 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=11.116, H2=6.192, H3=2.965
2025-07-22 09:51:54,995 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-07-22 09:51:54,996 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-07-22 09:51:54,999 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-07-22 09:51:55,026 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 09:51:57,031 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 09:51:57,059 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 09:51:59,364 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 09:51:59,391 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 09:52:00,746 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 09:52:00,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 09:52:03,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 09:52:03,159 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 09:52:05,351 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 09:52:05,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 09:52:06,628 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 09:52:06,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 09:52:09,672 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 09:52:09,703 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 09:52:15,757 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 09:52:15,787 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2405 sites
2025-07-22 09:52:20,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.072, H2=2.918, H3=2.298
2025-07-22 09:52:20,030 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 513 sites
2025-07-22 09:52:20,923 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.965, H2=-2.111, H3=-3.207
2025-07-22 09:52:20,953 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3027 sites
2025-07-22 09:52:26,206 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=18.682, H2=-4.999, H3=-3.619
2025-07-22 09:52:26,234 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1341 sites
2025-07-22 09:52:28,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=30.483, H2=-0.242, H3=0.024
2025-07-22 09:52:28,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2776 sites
2025-07-22 09:52:33,409 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=31.724, H2=0.525, H3=0.840
2025-07-22 09:52:33,436 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 824 sites
2025-07-22 09:52:34,864 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-6.657, H2=-5.485, H3=-8.546
2025-07-22 09:52:34,893 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1841 sites
2025-07-22 09:52:38,103 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=16.294, H2=-0.520, H3=-4.149
2025-07-22 09:52:38,133 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2482 sites
2025-07-22 09:52:42,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=3.518, H2=1.972, H3=8.592
2025-07-22 09:52:42,491 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2009 sites
2025-07-22 09:52:45,985 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=22.368, H2=9.207, H3=1.082
2025-07-22 09:52:46,014 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2083 sites
2025-07-22 09:52:49,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=1.338, H2=3.215, H3=3.821
2025-07-22 09:52:49,648 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1326 sites
2025-07-22 09:52:51,950 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-9.740, H2=-4.651, H3=-2.962
2025-07-22 09:52:51,976 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 345 sites
2025-07-22 09:52:52,573 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-3.171, H2=-8.744, H3=1.232
2025-07-22 09:52:52,599 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 258 sites
2025-07-22 09:52:53,045 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=5.851, H2=-5.324, H3=0.346
2025-07-22 09:52:53,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 365 sites
2025-07-22 09:52:53,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-3.147, H2=0.633, H3=-0.929
2025-07-22 09:52:53,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 882 sites
2025-07-22 09:52:55,263 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=21.003, H2=-1.114, H3=-4.559
2025-07-22 09:52:55,295 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 3868 sites
2025-07-22 09:53:02,019 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=18.276, H2=9.996, H3=10.622
2025-07-22 09:53:02,047 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1227 sites
2025-07-22 09:53:04,192 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.921, H2=-4.068, H3=-7.053
2025-07-22 09:53:04,223 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 3457 sites
2025-07-22 09:53:10,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=33.942, H2=10.981, H3=1.770
2025-07-22 09:53:10,269 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 3536 sites
2025-07-22 09:53:16,392 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=14.761, H2=0.915, H3=-1.929
2025-07-22 09:53:16,421 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2024 sites
2025-07-22 09:53:19,925 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-0.918, H2=0.846, H3=2.626
2025-07-22 09:53:19,958 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 4076 sites
2025-07-22 09:53:27,048 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=15.204, H2=6.771, H3=4.049
2025-07-22 09:53:27,074 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-07-22 09:53:27,074 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-07-22 09:53:27,076 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-07-22 09:53:27,103 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 09:53:29,083 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 09:53:29,111 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 09:53:31,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 09:53:31,400 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 09:53:32,721 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 09:53:32,749 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 09:53:35,091 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 09:53:35,118 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 09:53:37,290 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 09:53:37,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 09:53:38,550 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 09:53:38,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 09:53:41,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 09:53:41,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 09:53:47,618 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 09:53:47,648 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2405 sites
2025-07-22 09:53:51,839 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.072, H2=2.918, H3=2.298
2025-07-22 09:53:51,865 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 513 sites
2025-07-22 09:53:52,755 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.965, H2=-2.111, H3=-3.207
2025-07-22 09:53:52,786 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3027 sites
2025-07-22 09:53:58,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=18.682, H2=-4.999, H3=-3.619
2025-07-22 09:53:58,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1341 sites
2025-07-22 09:54:00,391 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=30.483, H2=-0.242, H3=0.024
2025-07-22 09:54:00,422 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2776 sites
2025-07-22 09:54:05,236 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=31.724, H2=0.525, H3=0.840
2025-07-22 09:54:05,263 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 824 sites
2025-07-22 09:54:06,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-6.657, H2=-5.485, H3=-8.546
2025-07-22 09:54:06,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1259 sites
2025-07-22 09:54:08,927 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.818, H2=-2.770, H3=-2.733
2025-07-22 09:54:08,954 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 582 sites
2025-07-22 09:54:09,974 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-10.178, H2=-7.527, H3=-7.680
2025-07-22 09:54:10,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2482 sites
2025-07-22 09:54:14,373 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.033, H2=1.946, H3=10.135
2025-07-22 09:54:14,402 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2009 sites
2025-07-22 09:54:17,899 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=18.606, H2=8.809, H3=1.660
2025-07-22 09:54:17,928 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2083 sites
2025-07-22 09:54:21,535 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=1.576, H2=3.401, H3=3.763
2025-07-22 09:54:21,563 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1326 sites
2025-07-22 09:54:23,871 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-7.693, H2=-4.876, H3=-2.843
2025-07-22 09:54:23,897 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 345 sites
2025-07-22 09:54:24,500 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-4.268, H2=-9.110, H3=1.809
2025-07-22 09:54:24,526 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 258 sites
2025-07-22 09:54:24,975 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.292, H2=-2.924, H3=0.257
2025-07-22 09:54:25,001 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 365 sites
2025-07-22 09:54:25,638 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.852, H2=0.408, H3=-1.104
2025-07-22 09:54:25,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 882 sites
2025-07-22 09:54:27,203 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=23.724, H2=-1.155, H3=-6.490
2025-07-22 09:54:27,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 3868 sites
2025-07-22 09:54:33,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=17.818, H2=7.551, H3=4.885
2025-07-22 09:54:33,982 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1227 sites
2025-07-22 09:54:36,120 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-1.463, H2=-4.681, H3=-7.582
2025-07-22 09:54:36,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 3457 sites
2025-07-22 09:54:42,191 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=28.535, H2=12.353, H3=1.433
2025-07-22 09:54:42,223 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 3536 sites
2025-07-22 09:54:48,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=10.948, H2=0.707, H3=-1.654
2025-07-22 09:54:48,436 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2024 sites
2025-07-22 09:54:51,969 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-1.266, H2=0.460, H3=4.579
2025-07-22 09:54:52,002 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 4076 sites
2025-07-22 09:54:59,183 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=12.585, H2=5.791, H3=5.533
2025-07-22 09:54:59,208 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=30 has insufficient data
2025-07-22 09:54:59,209 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=31
2025-07-22 09:54:59,210 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31 -9223372036854775808]
2025-07-22 09:54:59,238 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 09:55:01,239 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 09:55:01,266 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 09:55:03,569 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 09:55:03,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 09:55:04,939 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 09:55:04,967 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 09:55:07,337 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 09:55:07,364 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 09:55:09,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 09:55:09,590 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 09:55:10,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 09:55:10,867 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 09:55:13,869 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 09:55:13,901 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 09:55:19,944 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 09:55:19,973 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2405 sites
2025-07-22 09:55:24,158 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.072, H2=2.918, H3=2.298
2025-07-22 09:55:24,184 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 513 sites
2025-07-22 09:55:25,072 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.965, H2=-2.111, H3=-3.207
2025-07-22 09:55:25,103 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3027 sites
2025-07-22 09:55:30,403 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=18.682, H2=-4.999, H3=-3.619
2025-07-22 09:55:30,431 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1341 sites
2025-07-22 09:55:32,753 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=30.483, H2=-0.242, H3=0.024
2025-07-22 09:55:32,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2776 sites
2025-07-22 09:55:37,637 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=31.724, H2=0.525, H3=0.840
2025-07-22 09:55:37,665 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 824 sites
2025-07-22 09:55:39,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-6.657, H2=-5.485, H3=-8.546
2025-07-22 09:55:39,124 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1259 sites
2025-07-22 09:55:41,315 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.818, H2=-2.770, H3=-2.733
2025-07-22 09:55:41,341 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 582 sites
2025-07-22 09:55:42,352 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-10.178, H2=-7.527, H3=-7.680
2025-07-22 09:55:42,382 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2482 sites
2025-07-22 09:55:46,692 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.033, H2=1.946, H3=10.135
2025-07-22 09:55:46,721 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2009 sites
2025-07-22 09:55:50,217 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=18.606, H2=8.809, H3=1.660
2025-07-22 09:55:50,246 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2083 sites
2025-07-22 09:55:53,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=1.576, H2=3.401, H3=3.763
2025-07-22 09:55:53,883 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1326 sites
2025-07-22 09:55:56,187 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-7.693, H2=-4.876, H3=-2.843
2025-07-22 09:55:56,213 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 345 sites
2025-07-22 09:55:56,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-4.268, H2=-9.110, H3=1.809
2025-07-22 09:55:56,838 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 258 sites
2025-07-22 09:55:57,287 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.292, H2=-2.924, H3=0.257
2025-07-22 09:55:57,312 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 365 sites
2025-07-22 09:55:57,949 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.852, H2=0.408, H3=-1.104
2025-07-22 09:55:57,976 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 882 sites
2025-07-22 09:55:59,508 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=23.724, H2=-1.155, H3=-6.490
2025-07-22 09:55:59,540 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 3868 sites
2025-07-22 09:56:06,248 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=17.818, H2=7.551, H3=4.885
2025-07-22 09:56:06,275 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1227 sites
2025-07-22 09:56:08,397 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-1.463, H2=-4.681, H3=-7.582
2025-07-22 09:56:08,425 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1630 sites
2025-07-22 09:56:11,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.102, H2=0.695, H3=3.117
2025-07-22 09:56:11,273 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1827 sites
2025-07-22 09:56:14,426 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=34.935, H2=10.884, H3=-0.427
2025-07-22 09:56:14,458 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 3536 sites
2025-07-22 09:56:20,602 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=12.028, H2=0.684, H3=-2.109
2025-07-22 09:56:20,631 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2024 sites
2025-07-22 09:56:24,146 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-1.113, H2=0.684, H3=3.717
2025-07-22 09:56:24,179 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 4076 sites
2025-07-22 09:56:31,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=16.206, H2=6.553, H3=3.001
2025-07-22 09:56:31,263 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=31 has insufficient data
2025-07-22 09:56:31,263 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=32
2025-07-22 09:56:31,265 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32 -9223372036854775808]
2025-07-22 09:56:31,293 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 09:56:33,258 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 09:56:33,287 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 09:56:35,560 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 09:56:35,587 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 09:56:36,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 09:56:36,949 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 09:56:39,309 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 09:56:39,337 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 09:56:41,526 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 09:56:41,553 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 09:56:42,792 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 09:56:42,821 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 09:56:45,818 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 09:56:45,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 09:56:51,839 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 09:56:51,869 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2405 sites
2025-07-22 09:56:56,052 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.072, H2=2.918, H3=2.298
2025-07-22 09:56:56,078 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 513 sites
2025-07-22 09:56:56,968 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.965, H2=-2.111, H3=-3.207
2025-07-22 09:56:56,998 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3027 sites
2025-07-22 09:57:02,283 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=18.682, H2=-4.999, H3=-3.619
2025-07-22 09:57:02,311 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1341 sites
2025-07-22 09:57:04,651 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=30.483, H2=-0.242, H3=0.024
2025-07-22 09:57:04,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2776 sites
2025-07-22 09:57:09,520 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=31.724, H2=0.525, H3=0.840
2025-07-22 09:57:09,547 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 824 sites
2025-07-22 09:57:10,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-6.657, H2=-5.485, H3=-8.546
2025-07-22 09:57:11,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1259 sites
2025-07-22 09:57:13,200 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.818, H2=-2.770, H3=-2.733
2025-07-22 09:57:13,226 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 582 sites
2025-07-22 09:57:14,239 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-10.178, H2=-7.527, H3=-7.680
2025-07-22 09:57:14,269 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2482 sites
2025-07-22 09:57:18,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.033, H2=1.946, H3=10.135
2025-07-22 09:57:18,632 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2009 sites
2025-07-22 09:57:22,108 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=18.606, H2=8.809, H3=1.660
2025-07-22 09:57:22,137 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2083 sites
2025-07-22 09:57:25,770 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=1.576, H2=3.401, H3=3.763
2025-07-22 09:57:25,798 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1326 sites
2025-07-22 09:57:28,096 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-7.693, H2=-4.876, H3=-2.843
2025-07-22 09:57:28,122 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 345 sites
2025-07-22 09:57:28,723 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-4.268, H2=-9.110, H3=1.809
2025-07-22 09:57:28,749 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 258 sites
2025-07-22 09:57:29,201 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.292, H2=-2.924, H3=0.257
2025-07-22 09:57:29,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 365 sites
2025-07-22 09:57:29,862 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.852, H2=0.408, H3=-1.104
2025-07-22 09:57:29,890 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 882 sites
2025-07-22 09:57:31,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=23.724, H2=-1.155, H3=-6.490
2025-07-22 09:57:31,465 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1847 sites
2025-07-22 09:57:34,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=14.020, H2=11.357, H3=3.791
2025-07-22 09:57:34,734 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2021 sites
2025-07-22 09:57:38,278 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=11.906, H2=1.725, H3=2.365
2025-07-22 09:57:38,305 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1227 sites
2025-07-22 09:57:40,465 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.651, H2=-3.469, H3=-8.842
2025-07-22 09:57:40,493 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1630 sites
2025-07-22 09:57:43,377 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-0.303, H2=1.122, H3=2.674
2025-07-22 09:57:43,406 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1827 sites
2025-07-22 09:57:46,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=36.472, H2=13.612, H3=-0.933
2025-07-22 09:57:46,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 3536 sites
2025-07-22 09:57:52,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=14.629, H2=0.917, H3=-2.105
2025-07-22 09:57:52,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 2024 sites
2025-07-22 09:57:56,433 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-1.500, H2=0.568, H3=3.098
2025-07-22 09:57:56,466 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 4076 sites
2025-07-22 09:58:03,598 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=14.850, H2=5.802, H3=3.300
2025-07-22 09:58:03,624 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=32 has insufficient data
2025-07-22 09:58:03,624 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=33
2025-07-22 09:58:03,626 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
 -9223372036854775808]
2025-07-22 09:58:03,654 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 09:58:05,635 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 09:58:05,663 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 09:58:07,937 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 09:58:07,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 09:58:09,303 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 09:58:09,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 09:58:11,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 09:58:11,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 09:58:13,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 09:58:13,918 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 09:58:15,145 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 09:58:15,174 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 09:58:18,164 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 09:58:18,196 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 09:58:24,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 09:58:24,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2405 sites
2025-07-22 09:58:28,427 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.072, H2=2.918, H3=2.298
2025-07-22 09:58:28,454 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 513 sites
2025-07-22 09:58:29,344 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.965, H2=-2.111, H3=-3.207
2025-07-22 09:58:29,374 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3027 sites
2025-07-22 09:58:34,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=18.682, H2=-4.999, H3=-3.619
2025-07-22 09:58:34,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1341 sites
2025-07-22 09:58:36,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=30.483, H2=-0.242, H3=0.024
2025-07-22 09:58:37,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2776 sites
2025-07-22 09:58:41,893 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=31.724, H2=0.525, H3=0.840
2025-07-22 09:58:41,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 824 sites
2025-07-22 09:58:43,349 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-6.657, H2=-5.485, H3=-8.546
2025-07-22 09:58:43,377 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1259 sites
2025-07-22 09:58:45,557 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.818, H2=-2.770, H3=-2.733
2025-07-22 09:58:45,584 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 582 sites
2025-07-22 09:58:46,599 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-10.178, H2=-7.527, H3=-7.680
2025-07-22 09:58:46,629 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2482 sites
2025-07-22 09:58:50,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.033, H2=1.946, H3=10.135
2025-07-22 09:58:50,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2009 sites
2025-07-22 09:58:54,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=18.606, H2=8.809, H3=1.660
2025-07-22 09:58:54,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2083 sites
2025-07-22 09:58:58,142 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=1.576, H2=3.401, H3=3.763
2025-07-22 09:58:58,170 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1326 sites
2025-07-22 09:59:00,468 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-7.693, H2=-4.876, H3=-2.843
2025-07-22 09:59:00,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 345 sites
2025-07-22 09:59:01,096 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-4.268, H2=-9.110, H3=1.809
2025-07-22 09:59:01,122 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 258 sites
2025-07-22 09:59:01,572 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.292, H2=-2.924, H3=0.257
2025-07-22 09:59:01,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 365 sites
2025-07-22 09:59:02,234 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.852, H2=0.408, H3=-1.104
2025-07-22 09:59:02,261 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 882 sites
2025-07-22 09:59:03,797 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=23.724, H2=-1.155, H3=-6.490
2025-07-22 09:59:03,825 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1847 sites
2025-07-22 09:59:07,045 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=14.020, H2=11.357, H3=3.791
2025-07-22 09:59:07,074 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 2021 sites
2025-07-22 09:59:10,595 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=11.906, H2=1.725, H3=2.365
2025-07-22 09:59:10,622 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1227 sites
2025-07-22 09:59:12,749 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.651, H2=-3.469, H3=-8.842
2025-07-22 09:59:12,777 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1630 sites
2025-07-22 09:59:15,600 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-0.303, H2=1.122, H3=2.674
2025-07-22 09:59:15,629 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1827 sites
2025-07-22 09:59:18,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=36.472, H2=13.612, H3=-0.933
2025-07-22 09:59:18,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1057 sites
2025-07-22 09:59:20,640 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=3.322, H2=-0.426, H3=-3.666
2025-07-22 09:59:20,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 2479 sites
2025-07-22 09:59:24,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=3.540, H2=0.236, H3=0.271
2025-07-22 09:59:24,977 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 2024 sites
2025-07-22 09:59:28,497 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-1.655, H2=0.507, H3=3.413
2025-07-22 09:59:28,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 4076 sites
2025-07-22 09:59:35,581 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=12.214, H2=6.045, H3=2.810
2025-07-22 09:59:35,606 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=33 has insufficient data
2025-07-22 09:59:35,606 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=34
2025-07-22 09:59:35,607 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34 -9223372036854775808]
2025-07-22 09:59:35,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 09:59:37,613 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 09:59:37,641 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 09:59:39,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 09:59:39,912 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 09:59:41,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 09:59:41,270 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 09:59:43,612 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 09:59:43,640 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 09:59:45,826 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 09:59:45,853 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 09:59:47,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 09:59:47,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 09:59:50,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 09:59:50,128 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 09:59:56,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 09:59:56,162 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 437 sites
2025-07-22 09:59:56,915 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.239, H2=-0.963, H3=-1.059
2025-07-22 09:59:56,944 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1968 sites
2025-07-22 10:00:00,360 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.130, H2=3.556, H3=2.369
2025-07-22 10:00:00,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 513 sites
2025-07-22 10:00:01,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.951, H2=-1.465, H3=-1.721
2025-07-22 10:00:01,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3027 sites
2025-07-22 10:00:06,531 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=31.712, H2=-6.272, H3=-4.890
2025-07-22 10:00:06,559 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1341 sites
2025-07-22 10:00:08,877 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=20.821, H2=-0.598, H3=-0.091
2025-07-22 10:00:08,907 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2776 sites
2025-07-22 10:00:13,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=34.498, H2=0.563, H3=1.611
2025-07-22 10:00:13,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 824 sites
2025-07-22 10:00:15,154 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-6.614, H2=-5.194, H3=-8.824
2025-07-22 10:00:15,182 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1259 sites
2025-07-22 10:00:17,367 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.397, H2=-3.209, H3=-2.890
2025-07-22 10:00:17,395 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 582 sites
2025-07-22 10:00:18,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-7.355, H2=-3.598, H3=-5.796
2025-07-22 10:00:18,442 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2482 sites
2025-07-22 10:00:22,780 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.333, H2=1.935, H3=9.499
2025-07-22 10:00:22,809 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2009 sites
2025-07-22 10:00:26,297 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=18.795, H2=8.796, H3=0.955
2025-07-22 10:00:26,326 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2083 sites
2025-07-22 10:00:30,020 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.551, H2=3.525, H3=4.239
2025-07-22 10:00:30,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1326 sites
2025-07-22 10:00:32,364 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-11.905, H2=-4.916, H3=-2.714
2025-07-22 10:00:32,391 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 345 sites
2025-07-22 10:00:32,993 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-2.992, H2=-8.356, H3=1.723
2025-07-22 10:00:33,019 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 258 sites
2025-07-22 10:00:33,473 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=8.164, H2=-4.166, H3=-0.174
2025-07-22 10:00:33,499 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 365 sites
2025-07-22 10:00:34,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-5.124, H2=0.546, H3=-0.823
2025-07-22 10:00:34,166 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 882 sites
2025-07-22 10:00:35,698 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=24.962, H2=-1.295, H3=-6.865
2025-07-22 10:00:35,727 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1847 sites
2025-07-22 10:00:38,962 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=8.877, H2=11.459, H3=3.871
2025-07-22 10:00:38,991 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 2021 sites
2025-07-22 10:00:42,518 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=8.905, H2=1.434, H3=2.234
2025-07-22 10:00:42,545 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1227 sites
2025-07-22 10:00:44,682 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-1.121, H2=-5.480, H3=-5.350
2025-07-22 10:00:44,711 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1630 sites
2025-07-22 10:00:47,548 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-0.529, H2=1.003, H3=3.237
2025-07-22 10:00:47,577 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1827 sites
2025-07-22 10:00:50,766 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=34.165, H2=14.689, H3=-0.396
2025-07-22 10:00:50,793 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1057 sites
2025-07-22 10:00:52,632 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=5.332, H2=-0.720, H3=-2.943
2025-07-22 10:00:52,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 2479 sites
2025-07-22 10:00:57,007 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=3.911, H2=0.026, H3=-0.380
2025-07-22 10:00:57,036 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 2024 sites
2025-07-22 10:01:00,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=-1.317, H2=0.423, H3=3.064
2025-07-22 10:01:00,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 4076 sites
2025-07-22 10:01:07,701 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=14.362, H2=7.106, H3=3.703
2025-07-22 10:01:07,726 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=34 has insufficient data
2025-07-22 10:01:07,727 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=35
2025-07-22 10:01:07,728 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35 -9223372036854775808]
2025-07-22 10:01:07,756 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:01:09,743 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:01:09,771 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:01:12,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:01:12,039 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:01:13,372 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:01:13,400 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 10:01:15,755 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 10:01:15,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 10:01:17,952 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 10:01:17,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 10:01:19,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 10:01:19,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 10:01:22,229 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 10:01:22,260 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 10:01:28,281 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 10:01:28,307 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 437 sites
2025-07-22 10:01:29,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.239, H2=-0.963, H3=-1.059
2025-07-22 10:01:29,099 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1968 sites
2025-07-22 10:01:32,525 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.130, H2=3.556, H3=2.369
2025-07-22 10:01:32,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 513 sites
2025-07-22 10:01:33,451 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.951, H2=-1.465, H3=-1.721
2025-07-22 10:01:33,482 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3027 sites
2025-07-22 10:01:38,778 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=31.712, H2=-6.272, H3=-4.890
2025-07-22 10:01:38,806 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1341 sites
2025-07-22 10:01:41,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=20.821, H2=-0.598, H3=-0.091
2025-07-22 10:01:41,168 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2776 sites
2025-07-22 10:01:45,993 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=34.498, H2=0.563, H3=1.611
2025-07-22 10:01:46,020 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 824 sites
2025-07-22 10:01:47,460 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-6.614, H2=-5.194, H3=-8.824
2025-07-22 10:01:47,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1259 sites
2025-07-22 10:01:49,683 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.397, H2=-3.209, H3=-2.890
2025-07-22 10:01:49,710 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 582 sites
2025-07-22 10:01:50,724 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-7.355, H2=-3.598, H3=-5.796
2025-07-22 10:01:50,754 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2482 sites
2025-07-22 10:01:55,092 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.333, H2=1.935, H3=9.499
2025-07-22 10:01:55,120 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2009 sites
2025-07-22 10:01:58,633 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=18.795, H2=8.796, H3=0.955
2025-07-22 10:01:58,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2083 sites
2025-07-22 10:02:02,294 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.551, H2=3.525, H3=4.239
2025-07-22 10:02:02,322 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1326 sites
2025-07-22 10:02:04,633 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-11.905, H2=-4.916, H3=-2.714
2025-07-22 10:02:04,658 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 345 sites
2025-07-22 10:02:05,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-2.992, H2=-8.356, H3=1.723
2025-07-22 10:02:05,282 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 132 sites
2025-07-22 10:02:05,511 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-0.860, H2=-2.775, H3=-4.510
2025-07-22 10:02:05,536 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 126 sites
2025-07-22 10:02:05,755 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=0.589, H2=-4.864, H3=1.612
2025-07-22 10:02:05,780 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 365 sites
2025-07-22 10:02:06,415 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-4.000, H2=-0.032, H3=-0.683
2025-07-22 10:02:06,441 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 882 sites
2025-07-22 10:02:07,974 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=25.786, H2=-1.096, H3=-6.796
2025-07-22 10:02:08,003 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1847 sites
2025-07-22 10:02:11,193 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=10.398, H2=8.216, H3=3.768
2025-07-22 10:02:11,222 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2021 sites
2025-07-22 10:02:14,727 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=9.537, H2=1.989, H3=1.862
2025-07-22 10:02:14,755 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1227 sites
2025-07-22 10:02:16,872 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-1.324, H2=-5.069, H3=-8.444
2025-07-22 10:02:16,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1630 sites
2025-07-22 10:02:19,740 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-0.428, H2=1.382, H3=3.553
2025-07-22 10:02:19,769 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1827 sites
2025-07-22 10:02:22,955 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=36.001, H2=11.085, H3=-0.128
2025-07-22 10:02:22,982 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1057 sites
2025-07-22 10:02:24,819 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=4.715, H2=-1.129, H3=-4.328
2025-07-22 10:02:24,849 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 2479 sites
2025-07-22 10:02:29,162 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=3.638, H2=-0.043, H3=-0.521
2025-07-22 10:02:29,191 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 2024 sites
2025-07-22 10:02:32,715 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=-1.155, H2=0.534, H3=3.118
2025-07-22 10:02:32,747 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 4076 sites
2025-07-22 10:02:39,810 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=11.496, H2=7.115, H3=3.867
2025-07-22 10:02:39,834 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=35 has insufficient data
2025-07-22 10:02:39,835 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=36
2025-07-22 10:02:39,836 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
 -9223372036854775808]
2025-07-22 10:02:39,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:02:41,850 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:02:41,878 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:02:44,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:02:44,170 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:02:45,500 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:02:45,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 10:02:47,885 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 10:02:47,913 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 10:02:50,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 10:02:50,112 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 10:02:51,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 10:02:51,384 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 10:02:54,414 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 10:02:54,445 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 10:03:00,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 10:03:00,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 437 sites
2025-07-22 10:03:01,352 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.239, H2=-0.963, H3=-1.059
2025-07-22 10:03:01,381 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1968 sites
2025-07-22 10:03:04,836 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.130, H2=3.556, H3=2.369
2025-07-22 10:03:04,862 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 513 sites
2025-07-22 10:03:05,769 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.951, H2=-1.465, H3=-1.721
2025-07-22 10:03:05,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3027 sites
2025-07-22 10:03:11,148 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=31.712, H2=-6.272, H3=-4.890
2025-07-22 10:03:11,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1341 sites
2025-07-22 10:03:13,540 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=20.821, H2=-0.598, H3=-0.091
2025-07-22 10:03:13,570 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2776 sites
2025-07-22 10:03:18,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=34.498, H2=0.563, H3=1.611
2025-07-22 10:03:18,445 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 824 sites
2025-07-22 10:03:19,888 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-6.614, H2=-5.194, H3=-8.824
2025-07-22 10:03:19,916 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1259 sites
2025-07-22 10:03:22,114 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.397, H2=-3.209, H3=-2.890
2025-07-22 10:03:22,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 582 sites
2025-07-22 10:03:23,147 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-7.355, H2=-3.598, H3=-5.796
2025-07-22 10:03:23,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2482 sites
2025-07-22 10:03:27,491 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.333, H2=1.935, H3=9.499
2025-07-22 10:03:27,520 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2009 sites
2025-07-22 10:03:31,006 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=18.795, H2=8.796, H3=0.955
2025-07-22 10:03:31,035 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2083 sites
2025-07-22 10:03:34,653 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.551, H2=3.525, H3=4.239
2025-07-22 10:03:34,681 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1326 sites
2025-07-22 10:03:36,996 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-11.905, H2=-4.916, H3=-2.714
2025-07-22 10:03:37,022 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 345 sites
2025-07-22 10:03:37,624 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-2.992, H2=-8.356, H3=1.723
2025-07-22 10:03:37,649 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 132 sites
2025-07-22 10:03:37,880 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-0.860, H2=-2.775, H3=-4.510
2025-07-22 10:03:37,906 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 126 sites
2025-07-22 10:03:38,126 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=0.589, H2=-4.864, H3=1.612
2025-07-22 10:03:38,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 365 sites
2025-07-22 10:03:38,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-4.000, H2=-0.032, H3=-0.683
2025-07-22 10:03:38,818 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 882 sites
2025-07-22 10:03:40,350 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=25.786, H2=-1.096, H3=-6.796
2025-07-22 10:03:40,378 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1847 sites
2025-07-22 10:03:43,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=10.398, H2=8.216, H3=3.768
2025-07-22 10:03:43,632 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 2021 sites
2025-07-22 10:03:47,148 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=9.537, H2=1.989, H3=1.862
2025-07-22 10:03:47,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1227 sites
2025-07-22 10:03:49,317 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-1.324, H2=-5.069, H3=-8.444
2025-07-22 10:03:49,345 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1630 sites
2025-07-22 10:03:52,188 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-0.428, H2=1.382, H3=3.553
2025-07-22 10:03:52,216 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1827 sites
2025-07-22 10:03:55,392 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=36.001, H2=11.085, H3=-0.128
2025-07-22 10:03:55,419 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1057 sites
2025-07-22 10:03:57,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=4.715, H2=-1.129, H3=-4.328
2025-07-22 10:03:57,287 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 2479 sites
2025-07-22 10:04:01,610 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=3.638, H2=-0.043, H3=-0.521
2025-07-22 10:04:01,639 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 2024 sites
2025-07-22 10:04:05,193 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=-1.155, H2=0.534, H3=3.118
2025-07-22 10:04:05,220 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 484 sites
2025-07-22 10:04:06,060 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=2.634, H2=7.062, H3=0.534
2025-07-22 10:04:06,092 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 3592 sites
2025-07-22 10:04:12,368 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=10.401, H2=3.510, H3=3.432
2025-07-22 10:04:12,393 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=36 has insufficient data
2025-07-22 10:04:12,393 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=37
2025-07-22 10:04:12,394 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37 -9223372036854775808]
2025-07-22 10:04:12,422 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:04:14,393 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:04:14,421 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:04:16,670 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:04:16,697 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:04:18,027 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:04:18,055 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 10:04:20,403 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 10:04:20,431 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 10:04:22,611 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 10:04:22,637 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 10:04:23,870 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 10:04:23,899 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 10:04:26,889 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 10:04:26,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 10:04:32,919 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 10:04:32,945 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 437 sites
2025-07-22 10:04:33,706 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.239, H2=-0.963, H3=-1.059
2025-07-22 10:04:33,735 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1968 sites
2025-07-22 10:04:37,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.130, H2=3.556, H3=2.369
2025-07-22 10:04:37,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 513 sites
2025-07-22 10:04:38,066 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.951, H2=-1.465, H3=-1.721
2025-07-22 10:04:38,098 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3027 sites
2025-07-22 10:04:43,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=31.712, H2=-6.272, H3=-4.890
2025-07-22 10:04:43,415 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1341 sites
2025-07-22 10:04:45,746 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=20.821, H2=-0.598, H3=-0.091
2025-07-22 10:04:45,776 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2776 sites
2025-07-22 10:04:50,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=34.498, H2=0.563, H3=1.611
2025-07-22 10:04:50,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 824 sites
2025-07-22 10:04:52,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-6.614, H2=-5.194, H3=-8.824
2025-07-22 10:04:52,121 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1259 sites
2025-07-22 10:04:54,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.397, H2=-3.209, H3=-2.890
2025-07-22 10:04:54,336 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 582 sites
2025-07-22 10:04:55,347 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-7.355, H2=-3.598, H3=-5.796
2025-07-22 10:04:55,376 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2482 sites
2025-07-22 10:04:59,717 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.333, H2=1.935, H3=9.499
2025-07-22 10:04:59,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 719 sites
2025-07-22 10:05:01,000 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-9.997, H2=-11.055, H3=-12.883
2025-07-22 10:05:01,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1290 sites
2025-07-22 10:05:03,272 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-4.238, H2=1.584, H3=1.521
2025-07-22 10:05:03,302 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2083 sites
2025-07-22 10:05:06,935 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=2.163, H2=4.350, H3=4.350
2025-07-22 10:05:06,963 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1326 sites
2025-07-22 10:05:09,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-10.130, H2=-4.263, H3=-2.936
2025-07-22 10:05:09,302 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 345 sites
2025-07-22 10:05:09,902 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.599, H2=-5.960, H3=1.229
2025-07-22 10:05:09,928 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 132 sites
2025-07-22 10:05:10,160 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-2.450, H2=-3.246, H3=-3.894
2025-07-22 10:05:10,186 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 126 sites
2025-07-22 10:05:10,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=0.592, H2=-2.665, H3=1.610
2025-07-22 10:05:10,433 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 365 sites
2025-07-22 10:05:11,075 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-4.196, H2=0.235, H3=-1.613
2025-07-22 10:05:11,102 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 882 sites
2025-07-22 10:05:12,662 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=24.045, H2=-1.273, H3=-7.784
2025-07-22 10:05:12,691 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1847 sites
2025-07-22 10:05:15,970 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=12.222, H2=8.561, H3=2.891
2025-07-22 10:05:15,999 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2021 sites
2025-07-22 10:05:19,566 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=9.171, H2=1.707, H3=2.991
2025-07-22 10:05:19,594 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1227 sites
2025-07-22 10:05:21,777 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-1.214, H2=-6.878, H3=-7.393
2025-07-22 10:05:21,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1630 sites
2025-07-22 10:05:24,692 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-0.721, H2=1.097, H3=2.489
2025-07-22 10:05:24,721 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1827 sites
2025-07-22 10:05:27,944 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=36.994, H2=11.838, H3=-0.949
2025-07-22 10:05:27,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1057 sites
2025-07-22 10:05:29,839 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=2.767, H2=-1.522, H3=-5.061
2025-07-22 10:05:29,869 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 2479 sites
2025-07-22 10:05:34,246 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=3.585, H2=0.162, H3=-0.217
2025-07-22 10:05:34,275 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 2024 sites
2025-07-22 10:05:37,826 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=-1.145, H2=0.374, H3=4.789
2025-07-22 10:05:37,852 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 484 sites
2025-07-22 10:05:38,701 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=2.139, H2=6.651, H3=1.074
2025-07-22 10:05:38,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 3592 sites
2025-07-22 10:05:44,999 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=12.850, H2=3.972, H3=3.827
2025-07-22 10:05:45,024 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=37 has insufficient data
2025-07-22 10:05:45,024 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=38
2025-07-22 10:05:45,026 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38 -9223372036854775808]
2025-07-22 10:05:45,053 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:05:47,030 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:05:47,058 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:05:49,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:05:49,334 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:05:50,664 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:05:50,692 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1351 sites
2025-07-22 10:05:53,045 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=6.176, H2=0.349, H3=0.413
2025-07-22 10:05:53,072 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1253 sites
2025-07-22 10:05:55,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=12.594, H2=-2.345, H3=-0.670
2025-07-22 10:05:55,269 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 711 sites
2025-07-22 10:05:56,503 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=20.964, H2=1.321, H3=0.707
2025-07-22 10:05:56,531 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1721 sites
2025-07-22 10:05:59,539 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=31.482, H2=-2.637, H3=-1.109
2025-07-22 10:05:59,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3458 sites
2025-07-22 10:06:05,616 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=16.156, H2=0.333, H3=-3.335
2025-07-22 10:06:05,642 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 437 sites
2025-07-22 10:06:06,403 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.239, H2=-0.963, H3=-1.059
2025-07-22 10:06:06,433 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1968 sites
2025-07-22 10:06:09,867 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.130, H2=3.556, H3=2.369
2025-07-22 10:06:09,894 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 513 sites
2025-07-22 10:06:10,787 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.951, H2=-1.465, H3=-1.721
2025-07-22 10:06:10,819 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3027 sites
2025-07-22 10:06:16,081 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=31.712, H2=-6.272, H3=-4.890
2025-07-22 10:06:16,109 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1341 sites
2025-07-22 10:06:18,431 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=20.821, H2=-0.598, H3=-0.091
2025-07-22 10:06:18,461 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2776 sites
2025-07-22 10:06:23,263 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=34.498, H2=0.563, H3=1.611
2025-07-22 10:06:23,290 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 824 sites
2025-07-22 10:06:24,720 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-6.614, H2=-5.194, H3=-8.824
2025-07-22 10:06:24,748 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1259 sites
2025-07-22 10:06:26,945 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.397, H2=-3.209, H3=-2.890
2025-07-22 10:06:26,971 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 582 sites
2025-07-22 10:06:27,987 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-7.355, H2=-3.598, H3=-5.796
2025-07-22 10:06:28,017 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2482 sites
2025-07-22 10:06:32,380 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.333, H2=1.935, H3=9.499
2025-07-22 10:06:32,408 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 719 sites
2025-07-22 10:06:33,658 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-9.997, H2=-11.055, H3=-12.883
2025-07-22 10:06:33,685 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1290 sites
2025-07-22 10:06:35,930 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-4.238, H2=1.584, H3=1.521
2025-07-22 10:06:35,960 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2083 sites
2025-07-22 10:06:39,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=2.163, H2=4.350, H3=4.350
2025-07-22 10:06:39,595 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1326 sites
2025-07-22 10:06:41,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-10.130, H2=-4.263, H3=-2.936
2025-07-22 10:06:41,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 345 sites
2025-07-22 10:06:42,537 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.599, H2=-5.960, H3=1.229
2025-07-22 10:06:42,563 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 132 sites
2025-07-22 10:06:42,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-2.450, H2=-3.246, H3=-3.894
2025-07-22 10:06:42,820 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 126 sites
2025-07-22 10:06:43,038 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=0.592, H2=-2.665, H3=1.610
2025-07-22 10:06:43,064 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 365 sites
2025-07-22 10:06:43,697 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-4.196, H2=0.235, H3=-1.613
2025-07-22 10:06:43,724 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 882 sites
2025-07-22 10:06:45,253 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=24.045, H2=-1.273, H3=-7.784
2025-07-22 10:06:45,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1847 sites
2025-07-22 10:06:48,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=12.222, H2=8.561, H3=2.891
2025-07-22 10:06:48,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 2021 sites
2025-07-22 10:06:52,054 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=9.171, H2=1.707, H3=2.991
2025-07-22 10:06:52,082 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1227 sites
2025-07-22 10:06:54,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-1.214, H2=-6.878, H3=-7.393
2025-07-22 10:06:54,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1630 sites
2025-07-22 10:06:57,075 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-0.721, H2=1.097, H3=2.489
2025-07-22 10:06:57,103 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1827 sites
2025-07-22 10:07:00,282 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=36.994, H2=11.838, H3=-0.949
2025-07-22 10:07:00,309 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1057 sites
2025-07-22 10:07:02,144 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=2.767, H2=-1.522, H3=-5.061
2025-07-22 10:07:02,174 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 2479 sites
2025-07-22 10:07:06,492 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=3.585, H2=0.162, H3=-0.217
2025-07-22 10:07:06,521 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 2024 sites
2025-07-22 10:07:10,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=-1.145, H2=0.374, H3=4.789
2025-07-22 10:07:10,083 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 484 sites
2025-07-22 10:07:10,926 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=2.139, H2=6.651, H3=1.074
2025-07-22 10:07:10,953 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 1150 sites
2025-07-22 10:07:12,953 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=1.402, H2=2.202, H3=3.689
2025-07-22 10:07:12,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 2442 sites
2025-07-22 10:07:17,222 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=12.923, H2=3.017, H3=2.349
2025-07-22 10:07:17,247 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=38 has insufficient data
2025-07-22 10:07:17,247 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=39
2025-07-22 10:07:17,249 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
 -9223372036854775808]
2025-07-22 10:07:17,276 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:07:19,252 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:07:19,279 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:07:21,534 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:07:21,561 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:07:22,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:07:22,921 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 438 sites
2025-07-22 10:07:23,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-3.477, H2=-1.866, H3=-0.049
2025-07-22 10:07:23,714 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 913 sites
2025-07-22 10:07:25,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.165, H2=1.232, H3=0.902
2025-07-22 10:07:25,335 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1253 sites
2025-07-22 10:07:27,510 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.924, H2=-2.073, H3=-0.189
2025-07-22 10:07:27,537 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 711 sites
2025-07-22 10:07:28,766 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=25.527, H2=1.368, H3=1.071
2025-07-22 10:07:28,795 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1721 sites
2025-07-22 10:07:31,787 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.680, H2=-2.577, H3=-1.542
2025-07-22 10:07:31,818 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3458 sites
2025-07-22 10:07:37,816 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.476, H2=0.190, H3=-3.637
2025-07-22 10:07:37,842 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 437 sites
2025-07-22 10:07:38,607 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.321, H2=-0.931, H3=-1.478
2025-07-22 10:07:38,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1968 sites
2025-07-22 10:07:42,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.659, H2=3.247, H3=4.188
2025-07-22 10:07:42,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 513 sites
2025-07-22 10:07:43,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.742, H2=-1.642, H3=-2.150
2025-07-22 10:07:43,047 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3027 sites
2025-07-22 10:07:48,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=26.685, H2=-5.398, H3=-4.312
2025-07-22 10:07:48,430 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1341 sites
2025-07-22 10:07:50,784 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=19.514, H2=-0.407, H3=0.327
2025-07-22 10:07:50,815 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2776 sites
2025-07-22 10:07:55,703 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=38.623, H2=0.473, H3=1.227
2025-07-22 10:07:55,730 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 824 sites
2025-07-22 10:07:57,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-6.649, H2=-6.932, H3=-6.229
2025-07-22 10:07:57,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1259 sites
2025-07-22 10:07:59,409 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.793, H2=-2.658, H3=-3.251
2025-07-22 10:07:59,435 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 582 sites
2025-07-22 10:08:00,451 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-8.909, H2=-6.267, H3=-7.076
2025-07-22 10:08:00,481 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2482 sites
2025-07-22 10:08:04,833 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=3.163, H2=1.840, H3=10.214
2025-07-22 10:08:04,859 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 719 sites
2025-07-22 10:08:06,105 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-8.311, H2=-12.088, H3=-14.222
2025-07-22 10:08:06,133 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1290 sites
2025-07-22 10:08:08,375 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-6.845, H2=1.321, H3=1.630
2025-07-22 10:08:08,404 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2083 sites
2025-07-22 10:08:12,040 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.804, H2=4.594, H3=4.096
2025-07-22 10:08:12,068 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1326 sites
2025-07-22 10:08:14,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-7.959, H2=-5.699, H3=-2.328
2025-07-22 10:08:14,414 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 345 sites
2025-07-22 10:08:15,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-3.721, H2=-8.688, H3=2.211
2025-07-22 10:08:15,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 132 sites
2025-07-22 10:08:15,268 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.311, H2=-2.358, H3=-4.194
2025-07-22 10:08:15,294 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 126 sites
2025-07-22 10:08:15,512 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=0.265, H2=-3.587, H3=0.977
2025-07-22 10:08:15,538 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 365 sites
2025-07-22 10:08:16,174 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-3.693, H2=0.624, H3=-1.216
2025-07-22 10:08:16,201 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 882 sites
2025-07-22 10:08:17,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=23.828, H2=-1.394, H3=-6.520
2025-07-22 10:08:17,763 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1847 sites
2025-07-22 10:08:20,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=12.865, H2=9.237, H3=3.957
2025-07-22 10:08:21,015 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2021 sites
2025-07-22 10:08:24,521 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=9.474, H2=1.876, H3=2.647
2025-07-22 10:08:24,548 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1227 sites
2025-07-22 10:08:26,673 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-1.222, H2=-4.663, H3=-5.478
2025-07-22 10:08:26,701 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1630 sites
2025-07-22 10:08:29,539 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-0.638, H2=0.679, H3=2.274
2025-07-22 10:08:29,568 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1827 sites
2025-07-22 10:08:32,746 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=29.993, H2=15.951, H3=-1.081
2025-07-22 10:08:32,774 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1057 sites
2025-07-22 10:08:34,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=3.490, H2=-0.733, H3=-5.393
2025-07-22 10:08:34,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 2479 sites
2025-07-22 10:08:38,966 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=3.251, H2=-0.081, H3=-0.698
2025-07-22 10:08:38,995 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 2024 sites
2025-07-22 10:08:42,538 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=-1.462, H2=0.688, H3=2.905
2025-07-22 10:08:42,564 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 484 sites
2025-07-22 10:08:43,411 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=2.350, H2=6.952, H3=1.076
2025-07-22 10:08:43,439 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 1150 sites
2025-07-22 10:08:45,447 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=1.941, H2=3.150, H3=4.018
2025-07-22 10:08:45,477 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 2442 sites
2025-07-22 10:08:49,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=9.739, H2=2.169, H3=1.324
2025-07-22 10:08:49,769 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=39 has insufficient data
2025-07-22 10:08:49,769 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=40
2025-07-22 10:08:49,771 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40 -9223372036854775808]
2025-07-22 10:08:49,799 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:08:51,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:08:51,809 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:08:54,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:08:54,107 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:08:55,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:08:55,466 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 438 sites
2025-07-22 10:08:56,230 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-3.477, H2=-1.866, H3=-0.049
2025-07-22 10:08:56,257 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 913 sites
2025-07-22 10:08:57,836 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.165, H2=1.232, H3=0.902
2025-07-22 10:08:57,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1253 sites
2025-07-22 10:09:00,039 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.924, H2=-2.073, H3=-0.189
2025-07-22 10:09:00,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 711 sites
2025-07-22 10:09:01,293 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=25.527, H2=1.368, H3=1.071
2025-07-22 10:09:01,321 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1721 sites
2025-07-22 10:09:04,327 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.680, H2=-2.577, H3=-1.542
2025-07-22 10:09:04,359 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3458 sites
2025-07-22 10:09:10,400 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.476, H2=0.190, H3=-3.637
2025-07-22 10:09:10,427 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 437 sites
2025-07-22 10:09:11,181 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.321, H2=-0.931, H3=-1.478
2025-07-22 10:09:11,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1968 sites
2025-07-22 10:09:14,627 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.659, H2=3.247, H3=4.188
2025-07-22 10:09:14,653 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 513 sites
2025-07-22 10:09:15,542 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.742, H2=-1.642, H3=-2.150
2025-07-22 10:09:15,573 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3027 sites
2025-07-22 10:09:20,858 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=26.685, H2=-5.398, H3=-4.312
2025-07-22 10:09:20,886 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1341 sites
2025-07-22 10:09:23,212 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=19.514, H2=-0.407, H3=0.327
2025-07-22 10:09:23,242 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2776 sites
2025-07-22 10:09:28,068 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=38.623, H2=0.473, H3=1.227
2025-07-22 10:09:28,095 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 824 sites
2025-07-22 10:09:29,527 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-6.649, H2=-6.932, H3=-6.229
2025-07-22 10:09:29,555 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1259 sites
2025-07-22 10:09:31,731 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.793, H2=-2.658, H3=-3.251
2025-07-22 10:09:31,757 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 582 sites
2025-07-22 10:09:32,775 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-8.909, H2=-6.267, H3=-7.076
2025-07-22 10:09:32,805 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2482 sites
2025-07-22 10:09:37,139 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=3.163, H2=1.840, H3=10.214
2025-07-22 10:09:37,166 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 719 sites
2025-07-22 10:09:38,414 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-8.311, H2=-12.088, H3=-14.222
2025-07-22 10:09:38,442 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1290 sites
2025-07-22 10:09:40,689 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-6.845, H2=1.321, H3=1.630
2025-07-22 10:09:40,718 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2083 sites
2025-07-22 10:09:44,330 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.804, H2=4.594, H3=4.096
2025-07-22 10:09:44,358 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1326 sites
2025-07-22 10:09:46,655 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-7.959, H2=-5.699, H3=-2.328
2025-07-22 10:09:46,681 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 345 sites
2025-07-22 10:09:47,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-3.721, H2=-8.688, H3=2.211
2025-07-22 10:09:47,305 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 132 sites
2025-07-22 10:09:47,536 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.311, H2=-2.358, H3=-4.194
2025-07-22 10:09:47,561 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 126 sites
2025-07-22 10:09:47,781 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=0.265, H2=-3.587, H3=0.977
2025-07-22 10:09:47,807 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 365 sites
2025-07-22 10:09:48,444 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-3.693, H2=0.624, H3=-1.216
2025-07-22 10:09:48,471 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 882 sites
2025-07-22 10:09:50,012 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=23.828, H2=-1.394, H3=-6.520
2025-07-22 10:09:50,041 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1847 sites
2025-07-22 10:09:53,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=12.865, H2=9.237, H3=3.957
2025-07-22 10:09:53,306 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 2021 sites
2025-07-22 10:09:56,852 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=9.474, H2=1.876, H3=2.647
2025-07-22 10:09:56,880 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1227 sites
2025-07-22 10:09:59,038 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=-1.222, H2=-4.663, H3=-5.478
2025-07-22 10:09:59,066 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1630 sites
2025-07-22 10:10:01,941 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-0.638, H2=0.679, H3=2.274
2025-07-22 10:10:01,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1827 sites
2025-07-22 10:10:05,151 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=29.993, H2=15.951, H3=-1.081
2025-07-22 10:10:05,178 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1057 sites
2025-07-22 10:10:07,023 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=3.490, H2=-0.733, H3=-5.393
2025-07-22 10:10:07,053 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 2479 sites
2025-07-22 10:10:11,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=3.251, H2=-0.081, H3=-0.698
2025-07-22 10:10:11,505 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1247 sites
2025-07-22 10:10:13,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=-3.540, H2=-0.715, H3=-0.125
2025-07-22 10:10:13,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 777 sites
2025-07-22 10:10:15,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=2.894, H2=0.720, H3=6.238
2025-07-22 10:10:15,095 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 484 sites
2025-07-22 10:10:15,945 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=3.448, H2=6.836, H3=1.026
2025-07-22 10:10:15,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 1150 sites
2025-07-22 10:10:17,983 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=1.664, H2=2.697, H3=2.436
2025-07-22 10:10:18,013 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 40 with 2442 sites
2025-07-22 10:10:22,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 40: H1=11.663, H2=3.263, H3=1.775
2025-07-22 10:10:22,263 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=40 has insufficient data
2025-07-22 10:10:22,263 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=41
2025-07-22 10:10:22,264 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40                   41 -9223372036854775808]
2025-07-22 10:10:22,291 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:10:24,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:10:24,300 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:10:26,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:10:26,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:10:27,906 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:10:27,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 438 sites
2025-07-22 10:10:28,693 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-3.477, H2=-1.866, H3=-0.049
2025-07-22 10:10:28,719 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 913 sites
2025-07-22 10:10:30,306 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.165, H2=1.232, H3=0.902
2025-07-22 10:10:30,334 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1253 sites
2025-07-22 10:10:32,509 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.924, H2=-2.073, H3=-0.189
2025-07-22 10:10:32,535 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 711 sites
2025-07-22 10:10:33,769 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=25.527, H2=1.368, H3=1.071
2025-07-22 10:10:33,797 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1721 sites
2025-07-22 10:10:36,782 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.680, H2=-2.577, H3=-1.542
2025-07-22 10:10:36,814 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3458 sites
2025-07-22 10:10:42,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.476, H2=0.190, H3=-3.637
2025-07-22 10:10:42,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 437 sites
2025-07-22 10:10:43,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.321, H2=-0.931, H3=-1.478
2025-07-22 10:10:43,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1968 sites
2025-07-22 10:10:47,080 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.659, H2=3.247, H3=4.188
2025-07-22 10:10:47,106 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 513 sites
2025-07-22 10:10:47,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.742, H2=-1.642, H3=-2.150
2025-07-22 10:10:48,021 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3027 sites
2025-07-22 10:10:53,292 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=26.685, H2=-5.398, H3=-4.312
2025-07-22 10:10:53,320 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1341 sites
2025-07-22 10:10:55,639 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=19.514, H2=-0.407, H3=0.327
2025-07-22 10:10:55,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2776 sites
2025-07-22 10:11:00,496 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=38.623, H2=0.473, H3=1.227
2025-07-22 10:11:00,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 824 sites
2025-07-22 10:11:01,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-6.649, H2=-6.932, H3=-6.229
2025-07-22 10:11:01,981 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1259 sites
2025-07-22 10:11:04,171 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.793, H2=-2.658, H3=-3.251
2025-07-22 10:11:04,198 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 582 sites
2025-07-22 10:11:05,208 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-8.909, H2=-6.267, H3=-7.076
2025-07-22 10:11:05,238 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2482 sites
2025-07-22 10:11:09,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=3.163, H2=1.840, H3=10.214
2025-07-22 10:11:09,595 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 719 sites
2025-07-22 10:11:10,844 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-8.311, H2=-12.088, H3=-14.222
2025-07-22 10:11:10,872 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1290 sites
2025-07-22 10:11:13,104 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-6.845, H2=1.321, H3=1.630
2025-07-22 10:11:13,134 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2083 sites
2025-07-22 10:11:16,754 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.804, H2=4.594, H3=4.096
2025-07-22 10:11:16,782 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1326 sites
2025-07-22 10:11:19,076 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-7.959, H2=-5.699, H3=-2.328
2025-07-22 10:11:19,102 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 345 sites
2025-07-22 10:11:19,701 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-3.721, H2=-8.688, H3=2.211
2025-07-22 10:11:19,727 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 132 sites
2025-07-22 10:11:19,957 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.311, H2=-2.358, H3=-4.194
2025-07-22 10:11:19,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 126 sites
2025-07-22 10:11:20,202 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=0.265, H2=-3.587, H3=0.977
2025-07-22 10:11:20,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 365 sites
2025-07-22 10:11:20,859 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-3.693, H2=0.624, H3=-1.216
2025-07-22 10:11:20,885 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 432 sites
2025-07-22 10:11:21,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=16.843, H2=-5.726, H3=-4.563
2025-07-22 10:11:21,655 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 450 sites
2025-07-22 10:11:22,429 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=6.191, H2=3.345, H3=-5.107
2025-07-22 10:11:22,458 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1847 sites
2025-07-22 10:11:25,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=12.765, H2=10.763, H3=4.363
2025-07-22 10:11:25,694 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 2021 sites
2025-07-22 10:11:29,189 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=9.228, H2=1.756, H3=2.214
2025-07-22 10:11:29,216 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1227 sites
2025-07-22 10:11:31,344 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-1.521, H2=-5.224, H3=-6.378
2025-07-22 10:11:31,372 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1630 sites
2025-07-22 10:11:34,187 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=-0.638, H2=0.807, H3=2.198
2025-07-22 10:11:34,216 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1827 sites
2025-07-22 10:11:37,378 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=30.990, H2=15.774, H3=-1.011
2025-07-22 10:11:37,406 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1057 sites
2025-07-22 10:11:39,233 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=3.250, H2=-1.141, H3=-5.497
2025-07-22 10:11:39,263 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 2479 sites
2025-07-22 10:11:43,546 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=2.972, H2=0.383, H3=0.000
2025-07-22 10:11:43,573 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 1247 sites
2025-07-22 10:11:45,725 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=-3.534, H2=-0.892, H3=-0.615
2025-07-22 10:11:45,753 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 777 sites
2025-07-22 10:11:47,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=3.267, H2=1.168, H3=5.698
2025-07-22 10:11:47,120 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 484 sites
2025-07-22 10:11:47,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=2.112, H2=7.932, H3=1.675
2025-07-22 10:11:47,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 40 with 1150 sites
2025-07-22 10:11:49,973 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 40: H1=1.494, H2=3.219, H3=3.308
2025-07-22 10:11:50,004 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 41 with 2442 sites
2025-07-22 10:11:54,249 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 41: H1=10.845, H2=2.759, H3=1.690
2025-07-22 10:11:54,275 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=41 has insufficient data
2025-07-22 10:11:54,275 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=42
2025-07-22 10:11:54,277 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40                   41                   42
 -9223372036854775808]
2025-07-22 10:11:54,305 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:11:56,275 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:11:56,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:11:58,550 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:11:58,577 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:11:59,898 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:11:59,924 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 438 sites
2025-07-22 10:12:00,685 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-3.477, H2=-1.866, H3=-0.049
2025-07-22 10:12:00,713 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 913 sites
2025-07-22 10:12:02,306 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.165, H2=1.232, H3=0.902
2025-07-22 10:12:02,334 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1253 sites
2025-07-22 10:12:04,524 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.924, H2=-2.073, H3=-0.189
2025-07-22 10:12:04,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 711 sites
2025-07-22 10:12:05,798 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=25.527, H2=1.368, H3=1.071
2025-07-22 10:12:05,826 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1721 sites
2025-07-22 10:12:08,841 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.680, H2=-2.577, H3=-1.542
2025-07-22 10:12:08,873 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3458 sites
2025-07-22 10:12:14,948 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=23.476, H2=0.190, H3=-3.637
2025-07-22 10:12:14,975 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 437 sites
2025-07-22 10:12:15,741 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.321, H2=-0.931, H3=-1.478
2025-07-22 10:12:15,769 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1968 sites
2025-07-22 10:12:19,211 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.659, H2=3.247, H3=4.188
2025-07-22 10:12:19,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 513 sites
2025-07-22 10:12:20,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.742, H2=-1.642, H3=-2.150
2025-07-22 10:12:20,166 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3027 sites
2025-07-22 10:12:25,505 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=26.685, H2=-5.398, H3=-4.312
2025-07-22 10:12:25,533 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1341 sites
2025-07-22 10:12:27,881 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=19.514, H2=-0.407, H3=0.327
2025-07-22 10:12:27,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2776 sites
2025-07-22 10:12:32,762 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=38.623, H2=0.473, H3=1.227
2025-07-22 10:12:32,789 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 824 sites
2025-07-22 10:12:34,232 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-6.649, H2=-6.932, H3=-6.229
2025-07-22 10:12:34,260 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1259 sites
2025-07-22 10:12:36,449 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.793, H2=-2.658, H3=-3.251
2025-07-22 10:12:36,476 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 582 sites
2025-07-22 10:12:37,481 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-8.909, H2=-6.267, H3=-7.076
2025-07-22 10:12:37,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2482 sites
2025-07-22 10:12:41,830 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=3.163, H2=1.840, H3=10.214
2025-07-22 10:12:41,857 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 719 sites
2025-07-22 10:12:43,108 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-8.311, H2=-12.088, H3=-14.222
2025-07-22 10:12:43,136 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1290 sites
2025-07-22 10:12:45,372 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-6.845, H2=1.321, H3=1.630
2025-07-22 10:12:45,401 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2083 sites
2025-07-22 10:12:49,023 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.804, H2=4.594, H3=4.096
2025-07-22 10:12:49,052 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1326 sites
2025-07-22 10:12:51,360 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-7.959, H2=-5.699, H3=-2.328
2025-07-22 10:12:51,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 345 sites
2025-07-22 10:12:51,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-3.721, H2=-8.688, H3=2.211
2025-07-22 10:12:52,011 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 132 sites
2025-07-22 10:12:52,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.311, H2=-2.358, H3=-4.194
2025-07-22 10:12:52,267 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 126 sites
2025-07-22 10:12:52,486 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=0.265, H2=-3.587, H3=0.977
2025-07-22 10:12:52,512 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 365 sites
2025-07-22 10:12:53,143 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-3.693, H2=0.624, H3=-1.216
2025-07-22 10:12:53,169 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 432 sites
2025-07-22 10:12:53,918 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=16.843, H2=-5.726, H3=-4.563
2025-07-22 10:12:53,944 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 450 sites
2025-07-22 10:12:54,724 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=6.191, H2=3.345, H3=-5.107
2025-07-22 10:12:54,753 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1847 sites
2025-07-22 10:12:57,963 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=12.765, H2=10.763, H3=4.363
2025-07-22 10:12:57,992 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 2021 sites
2025-07-22 10:13:01,500 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=9.228, H2=1.756, H3=2.214
2025-07-22 10:13:01,528 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 1227 sites
2025-07-22 10:13:03,671 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=-1.521, H2=-5.224, H3=-6.378
2025-07-22 10:13:03,700 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1630 sites
2025-07-22 10:13:06,557 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=-0.638, H2=0.807, H3=2.198
2025-07-22 10:13:06,584 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 786 sites
2025-07-22 10:13:07,961 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=8.235, H2=2.192, H3=-4.784
2025-07-22 10:13:07,989 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 1041 sites
2025-07-22 10:13:09,805 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=5.685, H2=2.979, H3=4.185
2025-07-22 10:13:09,833 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1057 sites
2025-07-22 10:13:11,671 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=2.975, H2=-0.715, H3=-3.838
2025-07-22 10:13:11,700 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 2479 sites
2025-07-22 10:13:16,031 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=4.435, H2=-0.193, H3=-0.755
2025-07-22 10:13:16,059 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 1247 sites
2025-07-22 10:13:18,229 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=-4.253, H2=-0.771, H3=0.023
2025-07-22 10:13:18,255 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 777 sites
2025-07-22 10:13:19,608 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=3.852, H2=1.270, H3=5.235
2025-07-22 10:13:19,634 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 40 with 484 sites
2025-07-22 10:13:20,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 40: H1=1.642, H2=7.133, H3=1.083
2025-07-22 10:13:20,504 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 41 with 1150 sites
2025-07-22 10:13:22,512 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 41: H1=1.300, H2=2.549, H3=3.449
2025-07-22 10:13:22,541 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 42 with 2442 sites
2025-07-22 10:13:26,794 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 42: H1=11.659, H2=2.654, H3=2.049
2025-07-22 10:13:26,820 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=42 has insufficient data
2025-07-22 10:13:26,820 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=43
2025-07-22 10:13:26,821 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40                   41                   42
                   43 -9223372036854775808]
2025-07-22 10:13:26,849 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
2025-07-22 10:13:28,833 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=-6.603, H2=-6.717, H3=-6.363
2025-07-22 10:13:28,861 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1298 sites
2025-07-22 10:13:31,127 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=1.373, H2=3.223, H3=3.776
2025-07-22 10:13:31,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 765 sites
2025-07-22 10:13:32,486 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=-0.957, H2=1.897, H3=-1.613
2025-07-22 10:13:32,513 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 438 sites
2025-07-22 10:13:33,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=-3.477, H2=-1.866, H3=-0.049
2025-07-22 10:13:33,303 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 913 sites
2025-07-22 10:13:34,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.165, H2=1.232, H3=0.902
2025-07-22 10:13:34,912 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1253 sites
2025-07-22 10:13:37,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.924, H2=-2.073, H3=-0.189
2025-07-22 10:13:37,125 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 711 sites
2025-07-22 10:13:38,358 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=25.527, H2=1.368, H3=1.071
2025-07-22 10:13:38,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1721 sites
2025-07-22 10:13:41,376 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=22.680, H2=-2.577, H3=-1.542
2025-07-22 10:13:41,404 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1379 sites
2025-07-22 10:13:43,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=15.475, H2=3.859, H3=2.296
2025-07-22 10:13:43,820 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2079 sites
2025-07-22 10:13:47,431 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.533, H2=-2.361, H3=-5.823
2025-07-22 10:13:47,458 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 437 sites
2025-07-22 10:13:48,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.581, H2=-0.478, H3=-0.618
2025-07-22 10:13:48,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1968 sites
2025-07-22 10:13:51,679 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=5.606, H2=3.487, H3=3.534
2025-07-22 10:13:51,705 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 513 sites
2025-07-22 10:13:52,595 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.386, H2=-1.838, H3=-2.062
2025-07-22 10:13:52,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3027 sites
2025-07-22 10:13:57,915 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=24.886, H2=-5.846, H3=-3.865
2025-07-22 10:13:57,943 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1341 sites
2025-07-22 10:14:00,284 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=32.988, H2=-0.639, H3=-0.092
2025-07-22 10:14:00,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2776 sites
2025-07-22 10:14:05,146 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=33.477, H2=0.999, H3=1.025
2025-07-22 10:14:05,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 824 sites
2025-07-22 10:14:06,601 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-6.532, H2=-5.060, H3=-6.903
2025-07-22 10:14:06,629 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1259 sites
2025-07-22 10:14:08,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.474, H2=-3.035, H3=-2.778
2025-07-22 10:14:08,839 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 582 sites
2025-07-22 10:14:09,843 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-9.048, H2=-4.597, H3=-8.178
2025-07-22 10:14:09,872 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2482 sites
2025-07-22 10:14:14,209 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=3.330, H2=1.660, H3=9.823
2025-07-22 10:14:14,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 719 sites
2025-07-22 10:14:15,497 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-11.137, H2=-14.858, H3=-12.647
2025-07-22 10:14:15,525 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1290 sites
2025-07-22 10:14:17,798 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-5.179, H2=1.815, H3=1.475
2025-07-22 10:14:17,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2083 sites
2025-07-22 10:14:21,490 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=1.412, H2=4.021, H3=3.804
2025-07-22 10:14:21,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1326 sites
2025-07-22 10:14:23,871 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-7.178, H2=-4.799, H3=-3.451
2025-07-22 10:14:23,897 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 345 sites
2025-07-22 10:14:24,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-3.515, H2=-8.150, H3=2.250
2025-07-22 10:14:24,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 132 sites
2025-07-22 10:14:24,763 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-1.089, H2=-2.364, H3=-4.217
2025-07-22 10:14:24,789 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 126 sites
2025-07-22 10:14:25,011 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=0.811, H2=-2.992, H3=1.734
2025-07-22 10:14:25,037 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 365 sites
2025-07-22 10:14:25,682 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-4.640, H2=0.211, H3=-1.040
2025-07-22 10:14:25,708 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 432 sites
2025-07-22 10:14:26,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=13.490, H2=-5.684, H3=-6.603
2025-07-22 10:14:26,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 450 sites
2025-07-22 10:14:27,284 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=6.508, H2=2.876, H3=-3.731
2025-07-22 10:14:27,312 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 31 with 1847 sites
2025-07-22 10:14:30,558 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 31: H1=10.160, H2=9.781, H3=3.048
2025-07-22 10:14:30,586 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 32 with 2021 sites
2025-07-22 10:14:34,112 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 32: H1=11.122, H2=1.435, H3=2.192
2025-07-22 10:14:34,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 33 with 1227 sites
2025-07-22 10:14:36,289 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 33: H1=-1.633, H2=-5.938, H3=-7.473
2025-07-22 10:14:36,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 34 with 1630 sites
2025-07-22 10:14:39,158 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 34: H1=-0.416, H2=1.255, H3=2.557
2025-07-22 10:14:39,186 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 35 with 786 sites
2025-07-22 10:14:40,556 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 35: H1=10.318, H2=2.437, H3=-7.522
2025-07-22 10:14:40,584 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 36 with 1041 sites
2025-07-22 10:14:42,389 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 36: H1=4.649, H2=2.692, H3=4.135
2025-07-22 10:14:42,416 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 37 with 1057 sites
2025-07-22 10:14:44,255 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 37: H1=3.243, H2=-1.136, H3=-4.470
2025-07-22 10:14:44,285 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 38 with 2479 sites
2025-07-22 10:14:48,606 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 38: H1=4.409, H2=0.561, H3=0.114
2025-07-22 10:14:48,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 39 with 1247 sites
2025-07-22 10:14:50,807 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 39: H1=-3.684, H2=-0.473, H3=-0.265
2025-07-22 10:14:50,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 40 with 777 sites
2025-07-22 10:14:52,177 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 40: H1=3.390, H2=1.467, H3=6.078
2025-07-22 10:14:52,203 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 41 with 484 sites
2025-07-22 10:14:53,043 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 41: H1=2.417, H2=6.763, H3=1.374
2025-07-22 10:14:53,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 42 with 1150 sites
2025-07-22 10:14:55,060 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 42: H1=1.602, H2=2.977, H3=3.564
2025-07-22 10:14:55,090 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 43 with 2442 sites
2025-07-22 10:14:59,328 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 43: H1=11.780, H2=2.931, H3=1.431
2025-07-22 10:14:59,352 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=43 has insufficient data
2025-07-22 10:14:59,353 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=44
2025-07-22 10:14:59,354 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31                   32                   33
                   34                   35                   36
                   37                   38                   39
                   40                   41                   42
                   43                   44 -9223372036854775808]
2025-07-22 10:14:59,381 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1137 sites
