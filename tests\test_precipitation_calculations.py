#!/usr/bin/env python3
"""
Comprehensive test suite for precipitation calculations with synthetic data.
"""

import os
import sys
import numpy as np
import xarray as xr
import tempfile
import shutil
import unittest
from unittest.mock import patch

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.precipitation import PrecipitationAnalyzer

class TestPrecipitationCalculations(unittest.TestCase):
    """Test precipitation calculations with synthetic data."""
    
    def setUp(self):
        """Set up test data."""
        self.temp_dir = tempfile.mkdtemp()
        self.precip_file = os.path.join(self.temp_dir, "test_precip.nc")
        self.cluster_file = os.path.join(self.temp_dir, "test_clusters.nc")
        self.output_file = os.path.join(self.temp_dir, "test_output.nc")
        
        # Create synthetic data
        self.create_synthetic_data()
        
    def tearDown(self):
        """Clean up test data."""
        shutil.rmtree(self.temp_dir)
    
    def create_synthetic_data(self):
        """Create synthetic precipitation and cluster data."""
        
        # Parameters
        n_years = 5
        n_days_per_year = 365
        n_time = n_years * n_days_per_year
        ny, nx = 10, 15
        
        # Create time coordinate
        time_coord = np.arange(n_time)
        
        # Create spatial coordinates
        y_coord = np.arange(ny)
        x_coord = np.arange(nx)
        
        # Create synthetic precipitation data with known patterns
        np.random.seed(42)
        
        # Base precipitation pattern (varies by location)
        base_precip = np.random.gamma(2, 3, (ny, nx))  # Shape parameter for realistic precip
        
        # Create precipitation with seasonal and weather type patterns
        precip_data = np.zeros((n_time, ny, nx))
        
        # Define weather type patterns (3 weather types for testing)
        wt_patterns = {
            0: 1.5,  # High precipitation WT
            1: 0.8,  # Medium precipitation WT  
            2: 0.3   # Low precipitation WT
        }
        
        # Create cluster assignments (3 weather types)
        k_values = [2, 3, 4]  # Test with different k values
        cluster_data = {}
        
        for k in k_values:
            # Create realistic cluster sequence with some persistence
            clusters = np.zeros(n_time, dtype=int)
            current_wt = 0
            persistence = 0
            
            for t in range(n_time):
                # Add some persistence (weather types tend to persist for a few days)
                if persistence > 0:
                    persistence -= 1
                else:
                    # Change weather type
                    current_wt = np.random.randint(0, k)
                    persistence = np.random.randint(1, 5)  # 1-4 day persistence
                
                clusters[t] = current_wt
                
                # Create precipitation based on weather type and season
                day_of_year = t % 365
                season_factor = 1.0 + 0.5 * np.sin(2 * np.pi * day_of_year / 365)  # Seasonal cycle
                
                # Weather type effect
                wt_factor = wt_patterns.get(current_wt, 1.0)
                
                # Add some randomness
                random_factor = np.random.gamma(1, 1, (ny, nx))
                
                # Combine effects
                precip_data[t] = base_precip * season_factor * wt_factor * random_factor
                
                # Add some extreme events (make sure some days have very high precipitation)
                if np.random.random() < 0.02:  # 2% chance of extreme event
                    extreme_factor = np.random.uniform(3, 8)
                    precip_data[t] *= extreme_factor
            
            cluster_data[f"k={k}"] = clusters[:k*len(clusters)//k]  # Adjust length for different k
        
        # Create precipitation dataset
        precip_ds = xr.Dataset(
            {
                "precip": (["time", "y", "x"], precip_data)
            },
            coords={
                "time": time_coord,
                "y": y_coord,
                "x": x_coord
            }
        )
        precip_ds.to_netcdf(self.precip_file)
        
        # Create cluster dataset
        cluster_ds = xr.Dataset(
            cluster_data,
            coords={"time": time_coord}
        )
        cluster_ds.to_netcdf(self.cluster_file)
        
        # Store expected values for testing
        self.n_years = n_years
        self.n_time = n_time
        self.ny, self.nx = ny, nx
        self.wt_patterns = wt_patterns
        self.k_values = k_values
        
    def test_initialization(self):
        """Test analyzer initialization."""
        analyzer = PrecipitationAnalyzer(
            self.precip_file, 
            self.cluster_file, 
            self.output_file
        )
        
        self.assertEqual(analyzer.precip_path, self.precip_file)
        self.assertEqual(analyzer.cluster_path, self.cluster_file)
        self.assertEqual(analyzer.output_path, self.output_file)
        self.assertEqual(analyzer.max_k, 35)
        
        # Check that data was loaded
        self.assertIsNotNone(analyzer.precip)
        self.assertIsNotNone(analyzer.cluster_ds)
        
    def test_time_preprocessing(self):
        """Test time index preprocessing."""
        analyzer = PrecipitationAnalyzer(
            self.precip_file, 
            self.cluster_file, 
            self.output_file
        )
        
        result = analyzer.preprocess_time_indices()
        precip_np, year_ids, season_ids, doy, early_mask, late_mask, unique_years = result
        
        # Check shapes
        self.assertEqual(precip_np.shape[0], len(year_ids))
        self.assertEqual(len(season_ids), len(year_ids))
        self.assertEqual(len(doy), len(year_ids))
        self.assertEqual(len(early_mask), len(year_ids))
        self.assertEqual(len(late_mask), len(year_ids))
        
        # Check year range
        self.assertEqual(len(unique_years), self.n_years)
        self.assertEqual(unique_years[0], 1981)
        self.assertEqual(unique_years[-1], 1981 + self.n_years - 1)
        
        # Check season assignments
        self.assertTrue(np.all(season_ids >= 0))
        self.assertTrue(np.all(season_ids <= 3))
        
        # Check day of year
        self.assertTrue(np.all(doy >= 1))
        self.assertTrue(np.all(doy <= 365))
        
    def test_annual_maxima_computation(self):
        """Test annual maxima computation."""
        analyzer = PrecipitationAnalyzer(
            self.precip_file, 
            self.cluster_file, 
            self.output_file
        )
        
        precip_np, year_ids, _, _, _, _, unique_years = analyzer.preprocess_time_indices()
        annual_max = analyzer.compute_annual_maxima(precip_np, year_ids, unique_years)
        
        # Check shape
        self.assertEqual(annual_max.shape, (self.n_years, self.ny, self.nx))
        
        # Check that annual maxima are reasonable
        self.assertTrue(np.all(annual_max >= 0))
        
        # Check that annual max is indeed the maximum for each year
        for i, year in enumerate(unique_years):
            year_mask = year_ids == year
            year_data = precip_np[year_mask]
            expected_max = np.nanmax(year_data, axis=0)
            np.testing.assert_array_almost_equal(annual_max[i], expected_max)
    
    def test_full_processing(self):
        """Test full processing pipeline."""
        analyzer = PrecipitationAnalyzer(
            self.precip_file, 
            self.cluster_file, 
            self.output_file
        )
        
        # Run processing
        analyzer.process_clusters()
        
        # Check that arrays were populated
        for k in self.k_values:
            for c in range(k):
                # Check mean_annual_max
                values = analyzer.mean_annual_max[k, c]
                self.assertFalse(np.all(np.isnan(values)), 
                               f"mean_annual_max all NaN for k={k}, c={c}")
                
                # Check mean_annual_total
                values = analyzer.mean_annual_total[k, c]
                self.assertFalse(np.all(np.isnan(values)), 
                               f"mean_annual_total all NaN for k={k}, c={c}")
                
                # Check frequencies
                freq_values = analyzer.wt_freq_gridpoint[k, c]
                self.assertFalse(np.all(np.isnan(freq_values)), 
                               f"wt_freq_gridpoint all NaN for k={k}, c={c}")
                
                ams_freq_values = analyzer.wt_freq_ams_gridpoint[k, c]
                self.assertFalse(np.all(np.isnan(ams_freq_values)), 
                               f"wt_freq_ams_gridpoint all NaN for k={k}, c={c}")
    
    def test_frequency_consistency(self):
        """Test that frequencies sum to 1."""
        analyzer = PrecipitationAnalyzer(
            self.precip_file, 
            self.cluster_file, 
            self.output_file
        )
        
        analyzer.process_clusters()
        
        for k in self.k_values:
            # Test wt_freq_gridpoint sums to 1
            freq_sum = np.zeros((self.ny, self.nx))
            for c in range(k):
                freq_sum += analyzer.wt_freq_gridpoint[k, c]
            
            # Should sum to 1 (within tolerance)
            np.testing.assert_array_almost_equal(freq_sum, 1.0, decimal=6,
                                               err_msg=f"wt_freq_gridpoint doesn't sum to 1 for k={k}")
            
            # Test wt_freq_ams_gridpoint sums to 1
            ams_freq_sum = np.zeros((self.ny, self.nx))
            for c in range(k):
                ams_freq_sum += np.nan_to_num(analyzer.wt_freq_ams_gridpoint[k, c], nan=0)
            
            # Should sum to approximately 1 (some gridpoints might have NaN)
            valid_mask = ~np.isnan(analyzer.wt_freq_ams_gridpoint[k, 0])
            if np.any(valid_mask):
                valid_sums = ams_freq_sum[valid_mask]
                self.assertTrue(np.all(np.abs(valid_sums - 1.0) < 0.1),
                              f"wt_freq_ams_gridpoint doesn't sum to ~1 for k={k}")
    
    def test_weather_type_differences(self):
        """Test that different weather types produce different precipitation patterns."""
        analyzer = PrecipitationAnalyzer(
            self.precip_file, 
            self.cluster_file, 
            self.output_file
        )
        
        analyzer.process_clusters()
        
        k = 3  # Test with 3 weather types
        
        # Get mean precipitation for each weather type
        wt_means = []
        for c in range(k):
            mean_precip = np.nanmean(analyzer.mean_all_days[k, c])
            wt_means.append(mean_precip)
        
        # Weather types should have different mean precipitation
        wt_means = np.array(wt_means)
        self.assertTrue(np.std(wt_means) > 0.1, 
                       "Weather types should have different mean precipitation")
        
        # Check that the ordering roughly matches our synthetic pattern
        # (WT 0 should have highest, WT 2 should have lowest)
        if not np.isnan(wt_means).any():
            self.assertGreater(wt_means[0], wt_means[2], 
                             "WT 0 should have higher precipitation than WT 2")
    
    def test_seasonal_calculations(self):
        """Test seasonal precipitation calculations."""
        analyzer = PrecipitationAnalyzer(
            self.precip_file, 
            self.cluster_file, 
            self.output_file
        )
        
        analyzer.process_clusters()
        
        k = 3
        c = 0  # Test first cluster
        
        # Check that seasonal data exists
        for season in range(4):
            seasonal_data = analyzer.mean_seasonal[k, c, season]
            # At least some seasons should have data
            if not np.all(np.isnan(seasonal_data)):
                self.assertTrue(np.any(seasonal_data >= 0), 
                              f"Seasonal data should be non-negative for k={k}, c={c}, season={season}")
    
    def test_save_and_load_results(self):
        """Test saving and loading results."""
        analyzer = PrecipitationAnalyzer(
            self.precip_file, 
            self.cluster_file, 
            self.output_file
        )
        
        analyzer.process_clusters()
        analyzer.save_results()
        
        # Check that file was created
        self.assertTrue(os.path.exists(self.output_file))
        
        # Load and check contents
        ds = xr.open_dataset(self.output_file)
        
        # Check that all expected variables are present
        expected_vars = [
            'mean_annual_max', 'mean_annual_total', 'mean_all_days',
            'mean_seasonal', 'mean_seasonal_total', 'mean_seasonal_max',
            'wt_frac', 'wt_AMS_frac', 'wt_freq_gridpoint', 'wt_freq_ams_gridpoint'
        ]
        
        for var in expected_vars:
            self.assertIn(var, ds.data_vars, f"Variable {var} missing from output")
        
        # Check dimensions
        self.assertEqual(ds.dims['k'], 36)  # max_k + 1
        self.assertEqual(ds.dims['cluster'], 35)  # max_k
        self.assertEqual(ds.dims['y'], self.ny)
        self.assertEqual(ds.dims['x'], self.nx)
        self.assertEqual(ds.dims['season'], 4)
        
        # Check that removed variables are not present
        removed_vars = ['wt_entropy', 'wt_dominance', 'wt_freq_early', 'wt_freq_late', 'wt_drift_index']
        for var in removed_vars:
            self.assertNotIn(var, ds.data_vars, f"Removed variable {var} still present")
        
        ds.close()

def run_comprehensive_test():
    """Run comprehensive test with detailed output."""
    
    print("="*60)
    print("COMPREHENSIVE PRECIPITATION CALCULATION TESTS")
    print("="*60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPrecipitationCalculations)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    if result.wasSuccessful():
        print("🎉 ALL TESTS PASSED!")
        print(f"✅ Ran {result.testsRun} tests successfully")
        print("✅ All precipitation calculations are working correctly")
        print("✅ Removed variables are properly eliminated")
        print("✅ Frequency calculations sum to 1.0")
        print("✅ Weather types show distinct precipitation patterns")
    else:
        print("❌ SOME TESTS FAILED!")
        print(f"Failed: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        
        if result.failures:
            print("\nFailures:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\nErrors:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
