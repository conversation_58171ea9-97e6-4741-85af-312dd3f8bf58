# -*- coding: utf-8 -*-
import numpy as np
import xarray as xr
from sklearn.utils.extmath import randomized_svd
import matplotlib.pyplot as plt
import logging

logger = logging.getLogger(__name__)

class AtmosClusterer:
    """
    Class for preprocessing atmospheric data for clustering.
    """
    def __init__(self, latlon_path, variable_paths, variable_names, levels=None, retain_variance=0.95, lat_var='latitude', lon_var='longitude'):
        """
        Initialize the atmospheric data clusterer.
        
        Parameters:
        -----------
        latlon_path : str
            Path to the latitude/longitude NetCDF file
        variable_paths : dict
            Dictionary mapping variable keys to file paths
        variable_names : dict
            Dictionary mapping variable keys to variable names in the files
        levels : dict, optional
            Dictionary mapping variable keys to pressure levels
        retain_variance : float, optional
            Fraction of variance to retain in dimensionality reduction
        lat_var : str, optional
            Name of the latitude variable in the latlon file
        lon_var : str, optional
            Name of the longitude variable in the latlon file
        """
        self.variables = {}
        self.retain_variance = retain_variance
        self.LAT = None
        self.LON = None
        self.lat_weights = None
        self.nan_mean = None
        self.nan_std = None
        self.reduced_data = None
        self.explained_var_ratio = None
        self.cumulative_variance = None
        self.n_components = None

        self.load_lat_lon(latlon_path, lat_var, lon_var)
        self.load_variables(variable_paths, variable_names, levels)

    def load_lat_lon(self, latlon_nc_path, lat_var='latitude', lon_var='longitude'):
        """
        Load latitude and longitude data.
        
        Parameters:
        -----------
        latlon_nc_path : str
            Path to the latitude/longitude NetCDF file
        lat_var : str, optional
            Name of the latitude variable
        lon_var : str, optional
            Name of the longitude variable
        """
        # Open in chunks to avoid memory overflow
        ds = xr.open_dataset(latlon_nc_path, chunks={lat_var: 1000, lon_var: 1000})
        lats = ds[lat_var].values
        lons = ds[lon_var].values

        # Handle 1D or 2D lats/lons
        if lats.ndim == 1 and lons.ndim == 1:
            lon_grid, lat_grid = np.meshgrid(lons, lats)
        elif lats.ndim == 2 and lons.ndim == 2:
            lat_grid, lon_grid = lats, lons
        else:
            raise ValueError("Unsupported lat/lon dimension configuration.")

        self.LAT = lat_grid
        self.LON = lon_grid
        self.lat_weights = np.sqrt(np.cos(np.pi * self.LAT / 180))
        logger.info(f"Loaded lat/lon grid with shape {self.LAT.shape}")

    def load_variables(self, paths_dict, varnames_dict, levels_dict=None):
        """
        Load variables from NetCDF files.
        
        Parameters:
        -----------
        paths_dict : dict
            Dictionary mapping variable keys to file paths
        varnames_dict : dict
            Dictionary mapping variable keys to variable names in the files
        levels_dict : dict, optional
            Dictionary mapping variable keys to pressure levels
        """
        for key in paths_dict:
            ds = xr.open_dataset(paths_dict[key])
            varname = varnames_dict[key]
            level = levels_dict.get(key, None) if levels_dict else None
            self.add_variable(name=key, ds=ds, varname=varname, level=level)
        logger.info(f"Loaded {len(self.variables)} variables")

    def add_variable(self, name, ds: xr.Dataset, varname: str, level: int = None):
        """
        Add a variable to the clusterer.

        Parameters:
        -----------
        name : str
            Name to use for the variable
        ds : xarray.Dataset
            Dataset containing the variable
        varname : str
            Name of the variable in the dataset
        level : int, optional
            Pressure level to select
        """
        if level is not None:
            data_var = ds.sel(pressure_level=level)[varname]
            data = data_var.values
        else:
            data_var = ds[varname]
            data = data_var.values

        # Convert 2D data to 3D with time=1
        if data.ndim == 2:
            data = data[np.newaxis, :, :]
            # For 2D data, use overall mean (no time dimension)
            mean = np.around(np.mean(data, axis=0), 4)
            anomaly = np.around(data, 4) - mean
        else:
            # For 3D data with time dimension, compute day-specific means
            if 'time' in data_var.dims:
                # Extract day of year from time coordinate
                time_coord = data_var.time
                try:
                    # Convert to pandas datetime and extract day of year
                    import pandas as pd
                    dates = pd.to_datetime(time_coord.values)
                    doy = dates.dayofyear.values

                    # Handle leap years by mapping Feb 29 to Feb 28
                    doy[doy > 59] = doy[doy > 59] - (dates.is_leap_year & (doy > 59)).astype(int)

                    # Compute day-specific means (1-365)
                    daily_means = np.full((365, *data.shape[1:]), np.nan)
                    for day in range(1, 366):
                        day_mask = doy == day
                        if np.any(day_mask):
                            daily_means[day-1] = np.nanmean(data[day_mask], axis=0)

                    # Compute anomalies using day-specific means
                    anomaly = np.full_like(data, np.nan)
                    for i, day in enumerate(doy):
                        day_idx = min(day - 1, 364)  # Ensure we don't exceed array bounds
                        anomaly[i] = data[i] - daily_means[day_idx]

                    logger.info(f"Using day-specific means for {name} (time dimension detected)")

                except Exception as e:
                    logger.warning(f"Could not extract day-of-year for {name}, using overall mean: {e}")
                    # Fallback to overall mean
                    mean = np.around(np.mean(data, axis=0), 4)
                    anomaly = np.around(data, 4) - mean
            else:
                # No time dimension, use overall mean
                mean = np.around(np.mean(data, axis=0), 4)
                anomaly = np.around(data, 4) - mean

        # Apply latitude-based weights
        if self.lat_weights.shape != anomaly.shape[1:]:
            raise ValueError("Latitude weight shape does not match variable spatial dimensions.")

        weighted = anomaly * self.lat_weights[np.newaxis, :, :]
        self.variables[name] = weighted.squeeze()
        logger.info(f"Added variable {name} with shape {self.variables[name].shape}")

    def _map2mat(self, data):
        """
        Map 2D or 3D data to a matrix.
        
        Parameters:
        -----------
        data : numpy.ndarray
            Input data array
            
        Returns:
        --------
        numpy.ndarray
            Reshaped data matrix
        """
        if data.ndim == 2:
            # Single 2D frame
            return data.reshape(1, -1)
        elif data.ndim == 3:
            tps, nlat, nlon = data.shape
            return data.reshape(tps, nlat * nlon)
        else:
            raise ValueError(f"Unexpected data shape: {data.shape}")

    def preprocess(self):
        """
        Preprocess the data by combining variables and standardizing.

        Note: Variables are already anomaly-processed (day-specific or overall mean removed)
        in add_variable(). This step only standardizes the variance.

        Returns:
        --------
        numpy.ndarray
            Preprocessed data matrix
        """
        matrices = [self._map2mat(data) for data in self.variables.values()]
        combined = np.concatenate(matrices, axis=1)

        # Only standardize variance, don't remove additional mean
        # (anomalies already computed in add_variable with day-specific or overall means)
        self.nan_std = np.nanstd(combined, axis=0, ddof=1)

        # Standardize only by dividing by standard deviation
        # Don't subtract mean again as anomalies are already computed
        standardized = combined / self.nan_std
        logger.info(f"Preprocessed data shape: {standardized.shape}")
        logger.info("Preserved day-specific anomaly structure during standardization")
        return np.nan_to_num(standardized)

    def reduce_dimensionality(self, data):
        """
        Reduce dimensionality using randomized SVD.
        
        Parameters:
        -----------
        data : numpy.ndarray
            Input data matrix
            
        Returns:
        --------
        numpy.ndarray
            Reduced data matrix
        """
        max_components = min(data.shape)
        U, S, Vt = randomized_svd(data, n_components=max_components)
        s_squared = S ** 2
        self.explained_var_ratio = s_squared / np.sum(s_squared)
        self.cumulative_variance = np.cumsum(self.explained_var_ratio)
        self.n_components = np.searchsorted(self.cumulative_variance, self.retain_variance) + 1

        U_reduced = np.around(U[:, :self.n_components], 4)
        S_reduced = np.diag(np.around(S[:self.n_components], 4))
        self.reduced_data = U_reduced @ S_reduced
        logger.info(f"Reduced dimensionality to {self.n_components} components")
        return self.reduced_data

    def plot_variance_explained(self, filename='variance_explained.png'):
        """
        Plot the variance explained by each component.
        
        Parameters:
        -----------
        filename : str, optional
            Output filename for the plot
        """
        if self.explained_var_ratio is None or self.cumulative_variance is None:
            raise RuntimeError("You must run `reduce_dimensionality` before plotting variance.")

        plt.figure(figsize=(10, 5))
        plt.plot(np.arange(1, len(self.explained_var_ratio) + 1), self.explained_var_ratio * 100, marker='o', label='Individual variance')
        plt.plot(np.arange(1, len(self.cumulative_variance) + 1), self.cumulative_variance * 100, marker='s', label='Cumulative variance')
        plt.axhline(y=self.retain_variance * 100, color='r', linestyle='--', label=f'{int(self.retain_variance*100)}% retention threshold')
        plt.axvline(x=self.n_components, color='g', linestyle='--', label=f'{self.n_components} modes')
        plt.xlabel('EOF Mode')
        plt.ylabel('Variance Explained (%)')
        plt.title('Scree Plot: Variance Explained by EOF Modes')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(filename)
        plt.close()
        logger.info(f"Saved variance explained plot to {filename}")

    def run(self, plot_filename='variance_explained.png'):
        """
        Run the full preprocessing pipeline.
        
        Parameters:
        -----------
        plot_filename : str, optional
            Output filename for the variance explained plot
            
        Returns:
        --------
        numpy.ndarray
            Reduced data matrix
        """
        data = self.preprocess()
        reduced = self.reduce_dimensionality(data)
        self.plot_variance_explained(plot_filename)
        return reduced
