 #!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
<PERSON>ript to run precipitation regionalization with weather typing.
"""
import os
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"

import logging
import argparse
import numpy as np
import xarray as xr
from src.features.standardize import VariableStandardizer
from src.models.clustering import EOFClusteringAnalyzer
from src.visualization.visualize import RegionalizationVisualizer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run precipitation regionalization with weather typing')
    parser.add_argument('--wt-range', type=str, default='2-35',
                        help='Range of weather type counts to analyze (e.g., 2-35)')
    parser.add_argument('--maxclust', type=int, default=40,
                        help='Maximum number of clusters to consider')
    parser.add_argument('--nsim', type=int, default=100,
                        help='Number of simulations')
    parser.add_argument('--cluster-stats', type=str, default='cluster_precip_stats.nc',
                        help='Path to cluster precipitation statistics file')
    parser.add_argument('--method', type=str, default='kmeans', choices=['kmeans', 'hierarchical'],
                        help='Clustering method to use (kmeans or hierarchical)')
    return parser.parse_args()

def main():
    """Main function to run precipitation regionalization with weather typing."""
    args = parse_args()
    
    # Parse weather type range
    wt_start, wt_end = map(int, args.wt_range.split('-'))
    
    # Load cluster precipitation statistics
    logger.info(f"Loading cluster precipitation statistics from {args.cluster_stats}")
    ds = xr.open_dataset(args.cluster_stats)
    
    # Extract variables
    # mam = ds["mean_annual_max"]
    mapr = ds["mean_annual_precip"]
    # mad = ds["mean_all_days"]
    # msm = ds["mean_seasonal_max"]
    # mst = ds["mean_seasonal_total"]
    # msd = ds["mean_seasonal"]
    # maxam = ds['max_annual_max']
    mh500 = ds['mean_h500'] 
    ah500 = ds['anomaly_h500']
    mmslp = ds['mean_mslp']
    amslp = ds['anomaly_mslp']
    mu850 = ds['mean_u850']
    au850 = ds['anomaly_u850']
    mv850 = ds['mean_v850']
    av850 = ds['anomaly_v850']
    
    
    
    
    # Process each weather type count
    for i in range(wt_start, wt_end + 1):
        logger.info(f"Processing weather type count k = {i} using {args.method} clustering")

        outdir = f"./regionalization_w_WT_{i}_{args.method}_onevariable"
        os.makedirs(outdir, exist_ok=True)
        
        # mam2 = mam[i][0:i],
        mapr2 = mapr[i][0:i],
        # maxam2 = maxam[i][0:i],
        mh5002 = mh500[i][0:i],
        amslp2 = amslp[i][0:i],
        mu8502 = mu850[i][0:i],
        mv8502 = mv850[i][0:i],
        
        # Define paths and variables
        paths = {
            "elevation": "sample_data/elevation_CONUS_CCSM_grid.nc",
            "lat": "sample_data/WRF_CCSM_lat_lon.nc",
            "lon": "sample_data/WRF_CCSM_lat_lon.nc",
        }
            
        for j in range(i):
            # paths[f"mam{j}"] = mam2[0].sel(cluster=j)
            paths[f"mapr{j}"] = mapr2[0].sel(cluster=j)
            # paths[f"maxam{j}"] = maxam2[0].sel(cluster=j)
            paths[f"mh500{j}"] = mh5002[0].sel(cluster=j)
            paths[f"amslp{j}"] = amslp2[0].sel(cluster=j)
            paths[f"mu850{j}"] = mu8502[0].sel(cluster=j)
            paths[f"mv850{j}"] = mv8502[0].sel(cluster=j)
            
            # "mad": mad[i][0:i],
            
            # Mean Seasonal Max
            # "msm_djf": msm.sel(season="DJF")[i][0:i],
            # "msm_mam": msm.sel(season="MAM")[i][0:i],
            # "msm_jja": msm.sel(season="JJA")[i][0:i],
            # "msm_son": msm.sel(season="SON")[i][0:i],
            
            # Mean Seasonal Total
            # "mst_djf": mst.sel(season="DJF")[i][0:i],
            # "mst_mam": mst.sel(season="MAM")[i][0:i],
            # "mst_jja": mst.sel(season="JJA")[i][0:i],
            # "mst_son": mst.sel(season="SON")[i][0:i],
            
            # Mean Seasonal (unspecified)
            # "msd_djf": msd.sel(season="DJF")[i][0:i],
            # "msd_mam": msd.sel(season="MAM")[i][0:i],
            # "msd_jja": msd.sel(season="JJA")[i][0:i],
            # "msd_son": msd.sel(season="SON")[i][0:i]
        
        
        names = {
            "elevation": "interpolated",
            "lat": "latitude",
            "lon": "longitude",
        }
        
        try:
            # Initialize standardizer
            logger.info("Initializing variable standardizer")
            
            stdzr = VariableStandardizer(paths, names)
            
            # Standardize data
            logger.info("Standardizing data")
            X = stdzr.fit_transform()
            
            logger.info(f"Standardized data shape: {X.shape}")
            
            # Initialize and run clustering analyzer
            logger.info(f"Initializing clustering analyzer with {args.method} method")
            analyzer = EOFClusteringAnalyzer(
                X,
                outdir=outdir,
                maxclust=args.maxclust,
                nsim=args.nsim,
                method=args.method
            )
            
            # Compute or load classifiability index
            logger.info("Computing classifiability index")
            analyzer.load_or_compute_ci()
            analyzer.plot_ci()
            
            # Visualize results
            #logger.info("Visualizing results")
            #ds_results = xr.open_dataset(f"{outdir}/CI_results_{args.method}.nc")
            
            #k_values = [f'k={j}' for j in range(1, args.maxclust + 1)]
            
            # Initialize visualizer
            #visualizer = RegionalizationVisualizer(outdir, "sample_data/WRF_CCSM_lat_lon.nc")
            
            # Plot individual region maps
            for k_name in k_values:
                logger.info(f"Plotting {k_name}")
                class_map = stdzr.inverse_transform_to_grid(ds_results[k_name].values)
                visualizer.plot_region_map(class_map, k_name)
            
            # Create comparison grid
            logger.info("Creating comparison grid")
            visualizer.create_comparison_grid(k_values)
            
        except Exception as e:
            logger.error(f"Error processing weather type count k = {i}: {str(e)}")
            continue
    
    logger.info("Precipitation regionalization with weather typing complete")

if __name__ == '__main__':
    main()
