2025-07-13 22:36:01,180 - __main__ - INFO - run_heterogeneity_analysis.py:525 - Starting heterogeneity analysis
2025-07-13 22:36:01,180 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-13 22:36:01,180 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Cluster data: regionalization_w_WT_9_hierarchical/CI_results_hierarchical.nc
2025-07-13 22:36:01,180 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Output directory: heterogeneity_results/regionalization_w_WT_9_hierarchical
2025-07-13 22:36:01,180 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Maximum clusters: 125
2025-07-13 22:36:01,180 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Number of simulations: 50
2025-07-13 22:36:01,180 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Remap clusters: True
2025-07-13 22:36:01,181 - __main__ - INFO - run_heterogeneity_analysis.py:540 - Remapping clusters from 1D to 2D grid format
2025-07-13 22:36:01,181 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_9_hierarchical/CI_results_hierarchical.nc
2025-07-13 22:36:01,181 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-13 22:36:04,673 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-13 22:36:05,406 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-13 22:36:05,439 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-13 22:36:05,463 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-13 22:36:05,466 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-13 22:36:05,486 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-13 22:36:05,525 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-13 22:36:05,556 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
