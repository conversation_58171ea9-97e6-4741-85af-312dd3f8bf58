2025-07-24 13:42:02,375 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-07-24 13:42:02,375 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-24 13:42:02,376 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_31_hierarchical/CI_results_hierarchical.nc
2025-07-24 13:42:02,376 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_31_hierarchical
2025-07-24 13:42:02,376 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-07-24 13:42:02,376 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-07-24 13:42:02,376 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-07-24 13:42:02,377 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-07-24 13:42:02,379 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_31_hierarchical/CI_results_hierarchical.nc
2025-07-24 13:42:02,379 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-24 13:42:03,415 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-24 13:42:03,934 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-24 13:42:03,972 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-24 13:42:03,996 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-24 13:42:03,998 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-24 13:42:04,015 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-24 13:42:04,047 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-24 13:42:04,103 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-24 13:42:04,275 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-24 13:42:04,481 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-24 13:42:04,483 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-24 13:42:04,485 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-24 13:42:04,516 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-24 13:42:04,518 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-24 13:42:04,520 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-24 13:42:04,522 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-24 13:42:04,573 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-24 13:42:04,574 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-24 13:42:04,576 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-24 13:42:04,580 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-24 13:42:04,580 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-24 13:42:04,582 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-24 13:42:04,583 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-24 13:42:04,583 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-24 13:42:04,585 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-24 13:42:04,587 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-24 13:42:04,588 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-24 13:42:04,590 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-24 13:42:04,592 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-24 13:42:04,594 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-24 13:42:04,597 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-24 13:42:04,599 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-24 13:42:04,601 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-24 13:42:04,603 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-24 13:42:04,605 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-24 13:42:04,607 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-24 13:42:04,609 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-24 13:42:04,611 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-24 13:42:04,614 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-24 13:42:04,624 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-07-24 13:42:04,624 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-24 13:42:04,624 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-24 13:42:04,625 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 100 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100']
2025-07-24 13:42:04,626 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-24 13:42:04,641 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-24 13:42:04,641 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-24 13:42:04,685 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-24 13:42:04,685 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-24 13:42:04,687 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-24 13:42:04,687 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-24 13:42:04,689 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-24 13:42:04,689 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-24 13:42:04,691 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-24 13:42:04,691 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-24 13:42:04,693 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-24 13:42:04,693 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-24 13:42:04,696 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-24 13:42:04,696 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-24 13:42:04,699 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-24 13:42:04,699 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-24 13:42:04,702 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-24 13:42:04,702 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-24 13:42:04,705 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-24 13:42:04,705 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-24 13:42:04,707 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-24 13:42:04,708 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-24 13:42:04,710 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-24 13:42:04,711 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-24 13:42:04,713 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-24 13:42:04,714 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-24 13:42:04,717 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-24 13:42:04,717 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-24 13:42:04,720 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-24 13:42:04,720 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-24 13:42:04,722 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-24 13:42:04,722 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-24 13:42:04,725 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-24 13:42:04,725 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-24 13:42:04,728 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-24 13:42:04,728 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-24 13:42:04,730 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-24 13:42:04,730 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-24 13:42:04,733 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-24 13:42:04,733 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-24 13:42:04,736 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-24 13:42:04,736 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-24 13:42:04,739 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-24 13:42:04,739 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-24 13:42:04,742 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-24 13:42:04,742 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-24 13:42:04,744 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-24 13:42:04,744 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-24 13:42:04,747 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-24 13:42:04,747 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-24 13:42:04,750 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-24 13:42:04,750 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-24 13:42:04,752 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-24 13:42:04,753 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-24 13:42:04,755 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-24 13:42:04,755 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-24 13:42:04,758 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-24 13:42:04,758 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-24 13:42:04,761 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-24 13:42:04,761 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-24 13:42:04,764 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-24 13:42:04,764 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-24 13:42:04,766 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-24 13:42:04,767 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-24 13:42:04,769 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-24 13:42:04,769 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-24 13:42:04,772 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-24 13:42:04,772 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-24 13:42:04,775 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-24 13:42:04,775 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-24 13:42:04,777 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-24 13:42:04,778 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-24 13:42:04,781 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-24 13:42:04,781 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-24 13:42:04,784 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-24 13:42:04,784 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-24 13:42:04,786 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-24 13:42:04,787 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-24 13:42:04,824 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-24 13:42:04,824 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-24 13:42:04,826 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-24 13:42:04,827 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-24 13:42:04,829 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-24 13:42:04,829 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-24 13:42:04,832 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-24 13:42:04,832 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-24 13:42:04,834 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-24 13:42:04,835 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-24 13:42:04,837 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-24 13:42:04,838 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-24 13:42:04,840 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-24 13:42:04,840 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-24 13:42:04,843 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-24 13:42:04,843 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-24 13:42:04,845 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-24 13:42:04,846 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-24 13:42:04,848 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-24 13:42:04,848 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-24 13:42:04,851 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-24 13:42:04,851 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-24 13:42:04,853 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-24 13:42:04,854 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-24 13:42:04,856 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-24 13:42:04,856 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-24 13:42:04,859 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-24 13:42:04,859 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-24 13:42:04,862 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-24 13:42:04,862 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-24 13:42:04,865 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-24 13:42:04,865 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-24 13:42:04,867 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-24 13:42:04,867 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-24 13:42:04,870 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-24 13:42:04,870 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-24 13:42:04,873 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-24 13:42:04,873 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-24 13:42:04,875 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-24 13:42:04,875 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-24 13:42:04,878 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-24 13:42:04,878 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-24 13:42:04,881 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-24 13:42:04,881 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-24 13:42:04,884 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-24 13:42:04,884 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-24 13:42:04,887 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-24 13:42:04,887 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-07-24 13:42:04,889 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-07-24 13:42:04,889 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-07-24 13:42:04,892 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-07-24 13:42:04,892 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-07-24 13:42:04,895 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-07-24 13:42:04,895 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-07-24 13:42:04,897 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-07-24 13:42:04,897 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-07-24 13:42:04,900 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-07-24 13:42:04,900 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-07-24 13:42:04,902 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-07-24 13:42:04,903 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-07-24 13:42:04,905 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-07-24 13:42:04,905 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-07-24 13:42:04,908 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-07-24 13:42:04,908 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-07-24 13:42:04,910 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-07-24 13:42:04,910 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-07-24 13:42:04,913 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-07-24 13:42:04,913 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-07-24 13:42:04,916 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-07-24 13:42:04,916 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-07-24 13:42:04,918 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-07-24 13:42:04,918 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-07-24 13:42:04,921 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-07-24 13:42:04,921 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-07-24 13:42:04,924 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-07-24 13:42:04,924 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-07-24 13:42:04,926 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-07-24 13:42:04,926 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-07-24 13:42:04,929 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-07-24 13:42:04,929 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-07-24 13:42:04,931 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-07-24 13:42:04,932 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-07-24 13:42:04,934 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-07-24 13:42:04,934 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-07-24 13:42:04,937 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-07-24 13:42:04,937 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-07-24 13:42:04,940 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-07-24 13:42:04,940 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-07-24 13:42:04,942 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-07-24 13:42:04,942 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-07-24 13:42:04,945 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-07-24 13:42:04,945 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-07-24 13:42:04,948 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-07-24 13:42:04,948 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-07-24 13:42:04,951 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-07-24 13:42:04,951 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-07-24 13:42:04,953 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-07-24 13:42:04,953 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-07-24 13:42:04,956 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-07-24 13:42:04,956 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-07-24 13:42:04,958 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-07-24 13:42:04,958 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-07-24 13:42:04,961 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-07-24 13:42:04,961 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-07-24 13:42:04,964 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-07-24 13:42:04,964 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-07-24 13:42:04,967 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-07-24 13:42:04,967 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-07-24 13:42:04,969 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-07-24 13:42:04,969 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-07-24 13:42:04,972 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-07-24 13:42:04,972 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-07-24 13:42:04,974 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-07-24 13:42:04,974 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-07-24 13:42:04,977 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-07-24 13:42:04,977 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-07-24 13:42:04,980 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-07-24 13:42:04,980 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-07-24 13:42:04,982 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-07-24 13:42:04,983 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-07-24 13:42:04,985 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-07-24 13:42:04,985 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_31_hierarchical/CI_results_hierarchical_remap.nc
2025-07-24 13:42:06,139 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-07-24 13:42:06,139 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-07-24 13:42:06,139 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-07-24 13:42:06,140 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_31_hierarchical/CI_results_hierarchical.nc
2025-07-24 13:42:06,140 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_31_hierarchical/CI_results_hierarchical_remap.nc
2025-07-24 13:42:06,140 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 100 variables
2025-07-24 13:42:06,140 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100
2025-07-24 13:42:06,140 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-07-24 13:42:06,143 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_31_hierarchical/CI_results_hierarchical_remap.nc
2025-07-24 13:42:06,143 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-07-24 13:42:06,143 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-24 13:42:06,181 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-07-24 13:42:13,725 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_31_hierarchical/CI_results_hierarchical_remap.nc
2025-07-24 13:42:14,034 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-07-24 13:42:14,034 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-07-24 13:42:14,034 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-07-24 13:42:14,036 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-07-24 13:42:14,094 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 18216 sites
2025-07-24 13:42:46,728 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=90.077, H2=32.014, H3=7.911
2025-07-24 13:42:46,817 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 34143 sites
2025-07-24 13:43:48,388 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=100.315, H2=12.309, H3=1.958
2025-07-24 13:43:48,416 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-07-24 13:43:48,416 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-07-24 13:43:48,417 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-07-24 13:43:48,475 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 18216 sites
2025-07-24 13:44:21,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=90.077, H2=32.014, H3=7.911
2025-07-24 13:44:21,061 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 17313 sites
2025-07-24 13:44:51,826 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=90.979, H2=0.359, H3=-1.606
2025-07-24 13:44:51,882 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 16830 sites
2025-07-24 13:45:21,713 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=40.494, H2=10.804, H3=2.545
2025-07-24 13:45:21,739 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-07-24 13:45:21,739 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-07-24 13:45:21,740 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-07-24 13:45:21,799 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 18216 sites
2025-07-24 13:45:54,028 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=90.077, H2=32.014, H3=7.911
2025-07-24 13:45:54,085 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 17313 sites
2025-07-24 13:46:24,774 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=90.979, H2=0.359, H3=-1.606
2025-07-24 13:46:24,819 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 10578 sites
2025-07-24 13:46:43,653 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=18.763, H2=6.275, H3=2.999
2025-07-24 13:46:43,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6252 sites
2025-07-24 13:46:54,699 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=28.108, H2=8.363, H3=0.236
2025-07-24 13:46:54,724 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-07-24 13:46:54,725 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-07-24 13:46:54,727 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-07-24 13:46:54,770 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 9471 sites
2025-07-24 13:47:11,623 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=19.151, H2=11.222, H3=4.343
2025-07-24 13:47:11,665 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8745 sites
2025-07-24 13:47:27,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=65.006, H2=27.352, H3=7.084
2025-07-24 13:47:27,154 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 17313 sites
2025-07-24 13:47:57,853 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=116.669, H2=0.512, H3=-1.609
2025-07-24 13:47:57,897 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 10578 sites
2025-07-24 13:48:16,535 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=18.525, H2=6.002, H3=2.793
2025-07-24 13:48:16,572 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6252 sites
2025-07-24 13:48:27,661 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=28.760, H2=6.946, H3=-0.038
2025-07-24 13:48:27,687 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-07-24 13:48:27,687 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-07-24 13:48:27,689 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-07-24 13:48:27,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 9471 sites
2025-07-24 13:48:44,604 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=19.151, H2=11.222, H3=4.343
2025-07-24 13:48:44,632 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1351 sites
2025-07-24 13:48:47,013 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=23.516, H2=-1.365, H3=-1.716
2025-07-24 13:48:47,051 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7394 sites
2025-07-24 13:49:00,113 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=33.943, H2=17.917, H3=10.722
2025-07-24 13:49:00,169 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 17313 sites
2025-07-24 13:49:30,832 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=91.016, H2=0.290, H3=-1.623
2025-07-24 13:49:30,877 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 10578 sites
2025-07-24 13:49:49,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.117, H2=4.179, H3=2.282
2025-07-24 13:49:49,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6252 sites
2025-07-24 13:50:00,600 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=23.659, H2=6.008, H3=0.424
2025-07-24 13:50:00,625 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-07-24 13:50:00,625 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-07-24 13:50:00,627 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-07-24 13:50:00,669 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 9471 sites
2025-07-24 13:50:17,390 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=19.151, H2=11.222, H3=4.343
2025-07-24 13:50:17,419 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1351 sites
2025-07-24 13:50:19,795 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=23.516, H2=-1.365, H3=-1.716
2025-07-24 13:50:19,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7394 sites
2025-07-24 13:50:32,842 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=33.943, H2=17.917, H3=10.722
2025-07-24 13:50:32,872 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2360 sites
2025-07-24 13:50:37,019 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=58.793, H2=-1.615, H3=-2.833
2025-07-24 13:50:37,071 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 14953 sites
2025-07-24 13:51:03,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=102.114, H2=-0.152, H3=-0.661
2025-07-24 13:51:03,379 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 10578 sites
2025-07-24 13:51:22,039 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.918, H2=4.741, H3=2.419
2025-07-24 13:51:22,076 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6252 sites
2025-07-24 13:51:33,102 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=19.316, H2=5.793, H3=0.240
2025-07-24 13:51:33,127 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-07-24 13:51:33,128 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-07-24 13:51:33,129 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-07-24 13:51:33,171 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 9471 sites
2025-07-24 13:51:50,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=19.151, H2=11.222, H3=4.343
2025-07-24 13:51:50,031 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1351 sites
2025-07-24 13:51:52,421 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=23.516, H2=-1.365, H3=-1.716
2025-07-24 13:51:52,459 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7394 sites
2025-07-24 13:52:05,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=33.943, H2=17.917, H3=10.722
2025-07-24 13:52:05,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2360 sites
2025-07-24 13:52:09,686 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=58.793, H2=-1.615, H3=-2.833
2025-07-24 13:52:09,738 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 14953 sites
2025-07-24 13:52:36,211 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=102.114, H2=-0.152, H3=-0.661
2025-07-24 13:52:36,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 10578 sites
2025-07-24 13:52:54,962 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.918, H2=4.741, H3=2.419
2025-07-24 13:52:54,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2515 sites
2025-07-24 13:52:59,428 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=19.261, H2=1.322, H3=-4.894
2025-07-24 13:52:59,460 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3737 sites
2025-07-24 13:53:06,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=17.047, H2=5.154, H3=2.267
2025-07-24 13:53:06,083 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-07-24 13:53:06,083 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-07-24 13:53:06,084 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-07-24 13:53:06,127 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 9471 sites
2025-07-24 13:53:22,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=19.151, H2=11.222, H3=4.343
2025-07-24 13:53:22,938 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 1351 sites
2025-07-24 13:53:25,326 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=23.516, H2=-1.365, H3=-1.716
2025-07-24 13:53:25,364 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 7394 sites
2025-07-24 13:53:38,423 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=33.943, H2=17.917, H3=10.722
2025-07-24 13:53:38,453 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2360 sites
2025-07-24 13:53:42,622 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=58.793, H2=-1.615, H3=-2.833
2025-07-24 13:53:42,674 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 14953 sites
2025-07-24 13:54:09,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=102.114, H2=-0.152, H3=-0.661
2025-07-24 13:54:09,121 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4513 sites
2025-07-24 13:54:17,046 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.025, H2=1.535, H3=-0.003
2025-07-24 13:54:17,082 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6065 sites
2025-07-24 13:54:27,820 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=11.554, H2=3.962, H3=2.898
2025-07-24 13:54:27,850 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2515 sites
2025-07-24 13:54:32,284 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=15.143, H2=0.765, H3=-5.207
2025-07-24 13:54:32,316 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3737 sites
2025-07-24 13:54:38,908 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=19.327, H2=4.673, H3=2.235
2025-07-24 13:54:38,933 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-07-24 13:54:38,933 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-07-24 13:54:38,935 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-07-24 13:54:38,968 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 13:54:45,508 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 13:54:45,545 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 13:54:55,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 13:54:55,819 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1351 sites
2025-07-24 13:54:58,202 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.020, H2=-2.337, H3=-1.731
2025-07-24 13:54:58,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 7394 sites
2025-07-24 13:55:11,312 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=41.468, H2=16.883, H3=8.505
2025-07-24 13:55:11,342 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2360 sites
2025-07-24 13:55:15,500 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=53.713, H2=-1.978, H3=-2.409
2025-07-24 13:55:15,553 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 14953 sites
2025-07-24 13:55:42,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=88.750, H2=0.209, H3=-0.603
2025-07-24 13:55:42,143 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4513 sites
2025-07-24 13:55:50,113 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.318, H2=1.877, H3=0.053
2025-07-24 13:55:50,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6065 sites
2025-07-24 13:56:00,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=14.116, H2=4.314, H3=3.592
2025-07-24 13:56:00,913 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2515 sites
2025-07-24 13:56:05,355 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.938, H2=0.890, H3=-3.732
2025-07-24 13:56:05,387 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3737 sites
2025-07-24 13:56:11,988 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=19.798, H2=4.875, H3=1.939
2025-07-24 13:56:12,013 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-07-24 13:56:12,013 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-07-24 13:56:12,015 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-07-24 13:56:12,048 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 13:56:18,606 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 13:56:18,641 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 13:56:28,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 13:56:28,923 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1351 sites
2025-07-24 13:56:31,331 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.020, H2=-2.337, H3=-1.731
2025-07-24 13:56:31,365 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 4309 sites
2025-07-24 13:56:39,057 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=16.671, H2=6.678, H3=10.125
2025-07-24 13:56:39,089 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3085 sites
2025-07-24 13:56:44,622 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=8.241, H2=1.615, H3=-4.114
2025-07-24 13:56:44,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2360 sites
2025-07-24 13:56:48,862 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=53.442, H2=-1.132, H3=-2.554
2025-07-24 13:56:48,914 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 14953 sites
2025-07-24 13:57:15,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=67.023, H2=0.627, H3=-0.782
2025-07-24 13:57:15,310 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4513 sites
2025-07-24 13:57:23,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.737, H2=1.555, H3=-0.130
2025-07-24 13:57:23,347 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6065 sites
2025-07-24 13:57:34,061 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=17.065, H2=5.550, H3=3.502
2025-07-24 13:57:34,091 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2515 sites
2025-07-24 13:57:38,567 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=20.275, H2=0.697, H3=-3.754
2025-07-24 13:57:38,599 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3737 sites
2025-07-24 13:57:45,223 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=17.390, H2=6.193, H3=3.226
2025-07-24 13:57:45,261 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-07-24 13:57:45,261 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-07-24 13:57:45,263 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-07-24 13:57:45,295 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 13:57:51,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 13:57:51,857 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 13:58:02,034 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 13:58:02,061 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 13:58:03,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 13:58:03,539 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 13:58:04,458 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 13:58:04,491 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4309 sites
2025-07-24 13:58:12,096 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.788, H2=5.870, H3=8.400
2025-07-24 13:58:12,127 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3085 sites
2025-07-24 13:58:17,579 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.957, H2=2.772, H3=-4.815
2025-07-24 13:58:17,608 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2360 sites
2025-07-24 13:58:21,759 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=54.908, H2=-1.355, H3=-3.237
2025-07-24 13:58:21,811 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 14953 sites
2025-07-24 13:58:48,293 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=98.574, H2=0.122, H3=-0.709
2025-07-24 13:58:48,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4513 sites
2025-07-24 13:58:56,309 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.430, H2=1.351, H3=-0.254
2025-07-24 13:58:56,345 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6065 sites
2025-07-24 13:59:07,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=18.553, H2=4.937, H3=2.723
2025-07-24 13:59:07,072 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2515 sites
2025-07-24 13:59:11,486 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=13.118, H2=0.734, H3=-3.354
2025-07-24 13:59:11,517 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3737 sites
2025-07-24 13:59:18,121 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=24.395, H2=5.918, H3=3.747
2025-07-24 13:59:18,147 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-07-24 13:59:18,147 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-07-24 13:59:18,148 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-07-24 13:59:18,193 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 13:59:24,703 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 13:59:24,740 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 13:59:35,047 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 13:59:35,074 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 13:59:36,532 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 13:59:36,559 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 13:59:37,475 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 13:59:37,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4309 sites
2025-07-24 13:59:45,097 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.788, H2=5.870, H3=8.400
2025-07-24 13:59:45,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3085 sites
2025-07-24 13:59:50,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.957, H2=2.772, H3=-4.815
2025-07-24 13:59:50,614 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2360 sites
2025-07-24 13:59:54,789 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=54.908, H2=-1.355, H3=-3.237
2025-07-24 13:59:54,825 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6164 sites
2025-07-24 14:00:05,771 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=72.443, H2=-0.824, H3=-0.875
2025-07-24 14:00:05,813 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8789 sites
2025-07-24 14:00:21,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=58.229, H2=-0.146, H3=-0.705
2025-07-24 14:00:21,344 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4513 sites
2025-07-24 14:00:29,284 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.999, H2=1.514, H3=-0.412
2025-07-24 14:00:29,320 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6065 sites
2025-07-24 14:00:40,033 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=12.258, H2=9.143, H3=3.694
2025-07-24 14:00:40,064 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2515 sites
2025-07-24 14:00:44,540 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=19.302, H2=1.193, H3=-4.738
2025-07-24 14:00:44,572 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3737 sites
2025-07-24 14:00:51,183 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=18.028, H2=4.797, H3=1.875
2025-07-24 14:00:51,208 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-07-24 14:00:51,209 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-07-24 14:00:51,210 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-07-24 14:00:51,242 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:00:57,767 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:00:57,803 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:01:08,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:01:08,069 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:01:09,537 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:01:09,563 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:01:10,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:01:10,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4309 sites
2025-07-24 14:01:18,108 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.788, H2=5.870, H3=8.400
2025-07-24 14:01:18,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3085 sites
2025-07-24 14:01:23,570 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.957, H2=2.772, H3=-4.815
2025-07-24 14:01:23,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1168 sites
2025-07-24 14:01:25,656 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=10.850, H2=-4.040, H3=-3.454
2025-07-24 14:01:25,683 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1192 sites
2025-07-24 14:01:27,778 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=19.460, H2=1.497, H3=-0.170
2025-07-24 14:01:27,814 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6164 sites
2025-07-24 14:01:38,681 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=83.837, H2=-1.044, H3=-0.965
2025-07-24 14:01:38,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 8789 sites
2025-07-24 14:01:54,297 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=50.388, H2=0.124, H3=-0.562
2025-07-24 14:01:54,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4513 sites
2025-07-24 14:02:02,300 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.010, H2=2.124, H3=0.128
2025-07-24 14:02:02,336 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6065 sites
2025-07-24 14:02:13,096 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=15.440, H2=6.711, H3=2.951
2025-07-24 14:02:13,127 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2515 sites
2025-07-24 14:02:17,596 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=15.672, H2=0.407, H3=-4.067
2025-07-24 14:02:17,629 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3737 sites
2025-07-24 14:02:24,235 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=20.166, H2=4.329, H3=2.488
2025-07-24 14:02:24,260 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-07-24 14:02:24,260 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-07-24 14:02:24,262 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-07-24 14:02:24,294 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:02:30,809 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:02:30,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:02:41,169 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:02:41,216 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:02:42,677 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:02:42,703 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:02:43,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:02:43,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4309 sites
2025-07-24 14:02:51,294 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.788, H2=5.870, H3=8.400
2025-07-24 14:02:51,326 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3085 sites
2025-07-24 14:02:56,850 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.957, H2=2.772, H3=-4.815
2025-07-24 14:02:56,877 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1168 sites
2025-07-24 14:02:58,955 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=10.850, H2=-4.040, H3=-3.454
2025-07-24 14:02:58,982 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1192 sites
2025-07-24 14:03:01,103 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=19.460, H2=1.497, H3=-0.170
2025-07-24 14:03:01,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6164 sites
2025-07-24 14:03:12,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=83.837, H2=-1.044, H3=-0.965
2025-07-24 14:03:12,099 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 8789 sites
2025-07-24 14:03:27,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=50.388, H2=0.124, H3=-0.562
2025-07-24 14:03:27,720 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4513 sites
2025-07-24 14:03:35,774 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.010, H2=2.124, H3=0.128
2025-07-24 14:03:35,810 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6065 sites
2025-07-24 14:03:46,644 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=15.440, H2=6.711, H3=2.951
2025-07-24 14:03:46,675 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2515 sites
2025-07-24 14:03:51,160 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=15.672, H2=0.407, H3=-4.067
2025-07-24 14:03:51,188 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1624 sites
2025-07-24 14:03:54,120 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=0.557, H2=2.015, H3=2.291
2025-07-24 14:03:54,149 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2113 sites
2025-07-24 14:03:57,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.195, H2=3.312, H3=0.467
2025-07-24 14:03:58,009 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-07-24 14:03:58,009 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-07-24 14:03:58,011 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-07-24 14:03:58,043 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:04:04,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:04:04,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:04:14,993 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:04:15,021 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:04:16,497 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:04:16,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:04:17,462 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:04:17,495 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4309 sites
2025-07-24 14:04:25,184 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.788, H2=5.870, H3=8.400
2025-07-24 14:04:25,216 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3085 sites
2025-07-24 14:04:30,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.957, H2=2.772, H3=-4.815
2025-07-24 14:04:30,733 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1168 sites
2025-07-24 14:04:32,807 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=10.850, H2=-4.040, H3=-3.454
2025-07-24 14:04:32,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1192 sites
2025-07-24 14:04:34,944 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=19.460, H2=1.497, H3=-0.170
2025-07-24 14:04:34,981 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6164 sites
2025-07-24 14:04:45,939 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=83.837, H2=-1.044, H3=-0.965
2025-07-24 14:04:45,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2389 sites
2025-07-24 14:04:50,184 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=18.617, H2=-2.932, H3=-1.291
2025-07-24 14:04:50,222 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6400 sites
2025-07-24 14:05:01,630 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=45.993, H2=1.538, H3=0.126
2025-07-24 14:05:01,664 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4513 sites
2025-07-24 14:05:09,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.979, H2=2.730, H3=0.224
2025-07-24 14:05:09,673 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 6065 sites
2025-07-24 14:05:20,409 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=12.150, H2=5.013, H3=3.367
2025-07-24 14:05:20,439 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2515 sites
2025-07-24 14:05:24,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=13.321, H2=0.564, H3=-3.130
2025-07-24 14:05:24,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1624 sites
2025-07-24 14:05:27,805 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=0.631, H2=1.610, H3=2.339
2025-07-24 14:05:27,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2113 sites
2025-07-24 14:05:31,568 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=7.364, H2=3.371, H3=0.362
2025-07-24 14:05:31,593 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-07-24 14:05:31,593 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-07-24 14:05:31,594 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-07-24 14:05:31,639 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:05:38,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:05:38,187 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:05:48,422 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:05:48,449 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:05:49,921 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:05:49,947 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:05:50,876 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:05:50,909 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4309 sites
2025-07-24 14:05:58,516 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.788, H2=5.870, H3=8.400
2025-07-24 14:05:58,543 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 790 sites
2025-07-24 14:05:59,938 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-5.760, H2=-5.232, H3=-6.767
2025-07-24 14:05:59,968 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2295 sites
2025-07-24 14:06:04,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=15.696, H2=6.456, H3=-2.645
2025-07-24 14:06:04,077 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1168 sites
2025-07-24 14:06:06,145 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=10.298, H2=-4.243, H3=-5.152
2025-07-24 14:06:06,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1192 sites
2025-07-24 14:06:08,289 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=25.239, H2=1.573, H3=0.005
2025-07-24 14:06:08,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6164 sites
2025-07-24 14:06:19,195 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=88.496, H2=-1.172, H3=-0.884
2025-07-24 14:06:19,225 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2389 sites
2025-07-24 14:06:23,414 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=12.810, H2=-2.965, H3=-0.714
2025-07-24 14:06:23,451 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6400 sites
2025-07-24 14:06:34,767 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=62.582, H2=1.861, H3=0.345
2025-07-24 14:06:34,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4513 sites
2025-07-24 14:06:42,733 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.971, H2=1.603, H3=-0.514
2025-07-24 14:06:42,769 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 6065 sites
2025-07-24 14:06:53,497 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=15.409, H2=4.250, H3=3.864
2025-07-24 14:06:53,528 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2515 sites
2025-07-24 14:06:57,966 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=18.683, H2=0.707, H3=-3.253
2025-07-24 14:06:57,994 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1624 sites
2025-07-24 14:07:00,860 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=0.175, H2=2.949, H3=2.324
2025-07-24 14:07:00,889 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2113 sites
2025-07-24 14:07:04,630 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=7.354, H2=2.776, H3=0.569
2025-07-24 14:07:04,667 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-07-24 14:07:04,667 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-07-24 14:07:04,668 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-07-24 14:07:04,700 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:07:11,193 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:07:11,229 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:07:21,437 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:07:21,464 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:07:22,931 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:07:22,957 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:07:23,873 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:07:23,906 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4309 sites
2025-07-24 14:07:31,527 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.788, H2=5.870, H3=8.400
2025-07-24 14:07:31,555 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 790 sites
2025-07-24 14:07:32,940 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-5.760, H2=-5.232, H3=-6.767
2025-07-24 14:07:32,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2295 sites
2025-07-24 14:07:37,028 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=15.696, H2=6.456, H3=-2.645
2025-07-24 14:07:37,056 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1168 sites
2025-07-24 14:07:39,121 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=10.298, H2=-4.243, H3=-5.152
2025-07-24 14:07:39,149 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1192 sites
2025-07-24 14:07:41,268 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=25.239, H2=1.573, H3=0.005
2025-07-24 14:07:41,304 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6164 sites
2025-07-24 14:07:52,180 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=88.496, H2=-1.172, H3=-0.884
2025-07-24 14:07:52,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2389 sites
2025-07-24 14:07:56,437 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=12.810, H2=-2.965, H3=-0.714
2025-07-24 14:07:56,467 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2160 sites
2025-07-24 14:08:00,267 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=54.015, H2=-0.778, H3=1.774
2025-07-24 14:08:00,300 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4240 sites
2025-07-24 14:08:07,737 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=21.702, H2=1.376, H3=-1.666
2025-07-24 14:08:07,772 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4513 sites
2025-07-24 14:08:15,724 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=11.400, H2=1.177, H3=0.041
2025-07-24 14:08:15,770 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 6065 sites
2025-07-24 14:08:26,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=19.242, H2=5.469, H3=3.222
2025-07-24 14:08:26,466 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2515 sites
2025-07-24 14:08:30,883 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=15.931, H2=0.912, H3=-3.965
2025-07-24 14:08:30,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1624 sites
2025-07-24 14:08:33,765 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=0.867, H2=2.035, H3=2.516
2025-07-24 14:08:33,795 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2113 sites
2025-07-24 14:08:37,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=7.583, H2=2.917, H3=1.192
2025-07-24 14:08:37,569 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-07-24 14:08:37,569 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-07-24 14:08:37,571 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-07-24 14:08:37,603 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:08:44,083 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:08:44,119 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:08:54,335 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:08:54,362 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:08:55,835 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:08:55,861 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:08:56,784 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:08:56,811 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1037 sites
2025-07-24 14:08:58,659 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.451, H2=2.760, H3=1.647
2025-07-24 14:08:58,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3272 sites
2025-07-24 14:09:04,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.687, H2=4.441, H3=8.911
2025-07-24 14:09:04,516 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 790 sites
2025-07-24 14:09:05,928 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-7.901, H2=-4.736, H3=-9.132
2025-07-24 14:09:05,958 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2295 sites
2025-07-24 14:09:10,030 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=12.668, H2=4.697, H3=-3.010
2025-07-24 14:09:10,058 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1168 sites
2025-07-24 14:09:12,140 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.633, H2=-5.164, H3=-3.972
2025-07-24 14:09:12,168 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1192 sites
2025-07-24 14:09:14,287 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=21.651, H2=1.782, H3=-0.103
2025-07-24 14:09:14,324 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6164 sites
2025-07-24 14:09:25,245 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=57.092, H2=-1.082, H3=-0.533
2025-07-24 14:09:25,275 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2389 sites
2025-07-24 14:09:29,483 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=15.323, H2=-2.826, H3=-1.282
2025-07-24 14:09:29,512 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2160 sites
2025-07-24 14:09:33,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=42.470, H2=-1.291, H3=0.640
2025-07-24 14:09:33,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4240 sites
2025-07-24 14:09:40,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=25.633, H2=1.092, H3=-1.372
2025-07-24 14:09:40,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4513 sites
2025-07-24 14:09:48,762 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.489, H2=1.887, H3=0.065
2025-07-24 14:09:48,798 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 6065 sites
2025-07-24 14:09:59,442 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=12.352, H2=6.047, H3=3.269
2025-07-24 14:09:59,472 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2515 sites
2025-07-24 14:10:03,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=12.138, H2=1.036, H3=-3.686
2025-07-24 14:10:03,912 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1624 sites
2025-07-24 14:10:06,761 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=0.287, H2=2.043, H3=3.156
2025-07-24 14:10:06,790 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2113 sites
2025-07-24 14:10:10,524 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=6.932, H2=2.584, H3=0.312
2025-07-24 14:10:10,549 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-07-24 14:10:10,549 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-07-24 14:10:10,550 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-07-24 14:10:10,582 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:10:17,061 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:10:17,097 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:10:27,326 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:10:27,353 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:10:28,814 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:10:28,840 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:10:29,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:10:29,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1037 sites
2025-07-24 14:10:31,604 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.451, H2=2.760, H3=1.647
2025-07-24 14:10:31,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3272 sites
2025-07-24 14:10:37,391 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.687, H2=4.441, H3=8.911
2025-07-24 14:10:37,418 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 790 sites
2025-07-24 14:10:38,818 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-7.901, H2=-4.736, H3=-9.132
2025-07-24 14:10:38,847 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2295 sites
2025-07-24 14:10:42,921 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=12.668, H2=4.697, H3=-3.010
2025-07-24 14:10:42,948 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1168 sites
2025-07-24 14:10:45,012 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.633, H2=-5.164, H3=-3.972
2025-07-24 14:10:45,039 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1192 sites
2025-07-24 14:10:47,127 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=21.651, H2=1.782, H3=-0.103
2025-07-24 14:10:47,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6164 sites
2025-07-24 14:10:57,987 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=57.092, H2=-1.082, H3=-0.533
2025-07-24 14:10:58,017 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2389 sites
2025-07-24 14:11:02,209 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=15.323, H2=-2.826, H3=-1.282
2025-07-24 14:11:02,238 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2160 sites
2025-07-24 14:11:06,037 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=42.470, H2=-1.291, H3=0.640
2025-07-24 14:11:06,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4240 sites
2025-07-24 14:11:13,520 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=25.633, H2=1.092, H3=-1.372
2025-07-24 14:11:13,553 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4513 sites
2025-07-24 14:11:21,457 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.489, H2=1.887, H3=0.065
2025-07-24 14:11:21,493 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 6065 sites
2025-07-24 14:11:32,182 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=12.352, H2=6.047, H3=3.269
2025-07-24 14:11:32,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1502 sites
2025-07-24 14:11:34,859 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-4.324, H2=-3.331, H3=-2.568
2025-07-24 14:11:34,886 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1013 sites
2025-07-24 14:11:36,666 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.212, H2=-0.436, H3=-6.133
2025-07-24 14:11:36,694 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1624 sites
2025-07-24 14:11:39,558 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=0.604, H2=2.860, H3=2.392
2025-07-24 14:11:39,587 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2113 sites
2025-07-24 14:11:43,299 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=8.051, H2=3.012, H3=0.738
2025-07-24 14:11:43,324 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-07-24 14:11:43,324 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-07-24 14:11:43,326 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-07-24 14:11:43,375 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:11:49,835 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:11:49,870 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:12:00,030 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:12:00,057 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:12:01,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:12:01,540 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:12:02,458 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:12:02,486 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1037 sites
2025-07-24 14:12:04,311 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.451, H2=2.760, H3=1.647
2025-07-24 14:12:04,342 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3272 sites
2025-07-24 14:12:10,099 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.687, H2=4.441, H3=8.911
2025-07-24 14:12:10,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 790 sites
2025-07-24 14:12:11,524 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-7.901, H2=-4.736, H3=-9.132
2025-07-24 14:12:11,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2295 sites
2025-07-24 14:12:15,606 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=12.668, H2=4.697, H3=-3.010
2025-07-24 14:12:15,633 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1168 sites
2025-07-24 14:12:17,685 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.633, H2=-5.164, H3=-3.972
2025-07-24 14:12:17,713 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1192 sites
2025-07-24 14:12:19,801 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=21.651, H2=1.782, H3=-0.103
2025-07-24 14:12:19,838 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6164 sites
2025-07-24 14:12:30,753 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=57.092, H2=-1.082, H3=-0.533
2025-07-24 14:12:30,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2389 sites
2025-07-24 14:12:34,990 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=15.323, H2=-2.826, H3=-1.282
2025-07-24 14:12:35,019 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2160 sites
2025-07-24 14:12:38,826 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=42.470, H2=-1.291, H3=0.640
2025-07-24 14:12:38,859 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4240 sites
2025-07-24 14:12:46,297 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=25.633, H2=1.092, H3=-1.372
2025-07-24 14:12:46,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4513 sites
2025-07-24 14:12:54,229 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.489, H2=1.887, H3=0.065
2025-07-24 14:12:54,260 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3405 sites
2025-07-24 14:13:00,237 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=10.581, H2=4.337, H3=1.432
2025-07-24 14:13:00,268 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2660 sites
2025-07-24 14:13:04,935 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=1.265, H2=1.879, H3=2.616
2025-07-24 14:13:04,963 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1502 sites
2025-07-24 14:13:07,599 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-3.774, H2=-3.460, H3=-1.707
2025-07-24 14:13:07,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1013 sites
2025-07-24 14:13:09,408 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=4.031, H2=-0.136, H3=-7.458
2025-07-24 14:13:09,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1624 sites
2025-07-24 14:13:12,269 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=0.550, H2=2.374, H3=3.204
2025-07-24 14:13:12,298 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2113 sites
2025-07-24 14:13:16,007 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=12.767, H2=2.888, H3=0.607
2025-07-24 14:13:16,032 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-07-24 14:13:16,032 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-07-24 14:13:16,033 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-07-24 14:13:16,066 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:13:22,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:13:22,653 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 5795 sites
2025-07-24 14:13:32,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.056, H2=7.249, H3=0.236
2025-07-24 14:13:33,006 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 829 sites
2025-07-24 14:13:34,476 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.928, H2=0.002, H3=-4.314
2025-07-24 14:13:34,502 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 522 sites
2025-07-24 14:13:35,427 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=3.098, H2=-8.332, H3=1.971
2025-07-24 14:13:35,454 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1037 sites
2025-07-24 14:13:37,302 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=6.451, H2=2.760, H3=1.647
2025-07-24 14:13:37,334 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3272 sites
2025-07-24 14:13:43,111 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.687, H2=4.441, H3=8.911
2025-07-24 14:13:43,138 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 790 sites
2025-07-24 14:13:44,546 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=-7.901, H2=-4.736, H3=-9.132
2025-07-24 14:13:44,576 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2295 sites
2025-07-24 14:13:48,683 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=12.668, H2=4.697, H3=-3.010
2025-07-24 14:13:48,710 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1168 sites
2025-07-24 14:13:50,773 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=10.633, H2=-5.164, H3=-3.972
2025-07-24 14:13:50,813 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1192 sites
2025-07-24 14:13:52,908 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=21.651, H2=1.782, H3=-0.103
2025-07-24 14:13:52,945 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6164 sites
2025-07-24 14:14:03,854 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=57.092, H2=-1.082, H3=-0.533
2025-07-24 14:14:03,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2389 sites
2025-07-24 14:14:08,135 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=15.323, H2=-2.826, H3=-1.282
2025-07-24 14:14:08,164 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2160 sites
2025-07-24 14:14:11,977 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=42.470, H2=-1.291, H3=0.640
2025-07-24 14:14:12,010 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4240 sites
2025-07-24 14:14:19,453 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=25.633, H2=1.092, H3=-1.372
2025-07-24 14:14:19,482 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2124 sites
2025-07-24 14:14:23,220 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=9.256, H2=2.161, H3=0.940
2025-07-24 14:14:23,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2389 sites
2025-07-24 14:14:27,469 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=6.566, H2=-1.308, H3=-1.870
2025-07-24 14:14:27,501 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3405 sites
2025-07-24 14:14:33,508 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=10.831, H2=3.341, H3=1.914
2025-07-24 14:14:33,539 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2660 sites
2025-07-24 14:14:38,232 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=0.818, H2=1.956, H3=2.807
2025-07-24 14:14:38,260 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1502 sites
2025-07-24 14:14:40,907 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-3.456, H2=-3.123, H3=-2.306
2025-07-24 14:14:40,935 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1013 sites
2025-07-24 14:14:42,724 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.889, H2=-0.331, H3=-6.924
2025-07-24 14:14:42,752 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1624 sites
2025-07-24 14:14:45,621 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=1.121, H2=2.000, H3=2.844
2025-07-24 14:14:45,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2113 sites
2025-07-24 14:14:49,377 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=8.957, H2=4.529, H3=0.824
2025-07-24 14:14:49,402 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-07-24 14:14:49,402 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-07-24 14:14:49,403 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-07-24 14:14:49,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:14:55,959 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:14:56,003 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3670 sites
2025-07-24 14:15:02,514 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.215, H2=7.458, H3=5.845
2025-07-24 14:15:02,544 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2125 sites
2025-07-24 14:15:06,306 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.342, H2=-2.650, H3=-9.986
2025-07-24 14:15:06,333 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 829 sites
2025-07-24 14:15:07,798 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.470, H2=0.247, H3=-4.813
2025-07-24 14:15:07,825 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 522 sites
2025-07-24 14:15:08,749 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=2.555, H2=-7.581, H3=2.917
2025-07-24 14:15:08,777 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1037 sites
2025-07-24 14:15:10,610 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=7.339, H2=2.887, H3=1.491
2025-07-24 14:15:10,641 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3272 sites
2025-07-24 14:15:16,451 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=4.763, H2=3.550, H3=11.047
2025-07-24 14:15:16,478 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 790 sites
2025-07-24 14:15:17,889 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-8.204, H2=-6.777, H3=-7.825
2025-07-24 14:15:17,919 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2295 sites
2025-07-24 14:15:21,987 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.220, H2=5.487, H3=-1.832
2025-07-24 14:15:22,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1168 sites
2025-07-24 14:15:24,079 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.214, H2=-5.308, H3=-3.379
2025-07-24 14:15:24,107 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1192 sites
2025-07-24 14:15:26,219 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=20.750, H2=1.736, H3=0.336
2025-07-24 14:15:26,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6164 sites
2025-07-24 14:15:37,156 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=72.644, H2=-0.773, H3=-0.284
2025-07-24 14:15:37,186 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2389 sites
2025-07-24 14:15:41,415 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=15.922, H2=-3.432, H3=-1.363
2025-07-24 14:15:41,444 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2160 sites
2025-07-24 14:15:45,235 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=33.452, H2=-1.254, H3=1.119
2025-07-24 14:15:45,268 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4240 sites
2025-07-24 14:15:52,718 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=27.245, H2=1.791, H3=-1.175
2025-07-24 14:15:52,748 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2124 sites
2025-07-24 14:15:56,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=5.676, H2=2.485, H3=0.776
2025-07-24 14:15:56,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2389 sites
2025-07-24 14:16:00,739 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=6.050, H2=-0.947, H3=-2.049
2025-07-24 14:16:00,771 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3405 sites
2025-07-24 14:16:06,764 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=13.027, H2=4.474, H3=2.079
2025-07-24 14:16:06,810 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2660 sites
2025-07-24 14:16:11,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=0.900, H2=1.680, H3=2.154
2025-07-24 14:16:11,521 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1502 sites
2025-07-24 14:16:14,169 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-2.826, H2=-3.117, H3=-2.385
2025-07-24 14:16:14,197 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1013 sites
2025-07-24 14:16:15,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=3.821, H2=-0.293, H3=-6.422
2025-07-24 14:16:16,014 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1624 sites
2025-07-24 14:16:18,879 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=0.596, H2=3.144, H3=2.734
2025-07-24 14:16:18,909 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2113 sites
2025-07-24 14:16:22,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=6.995, H2=3.539, H3=0.749
2025-07-24 14:16:22,671 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-07-24 14:16:22,671 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-07-24 14:16:22,673 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-07-24 14:16:22,705 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:16:29,198 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:16:29,230 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3670 sites
2025-07-24 14:16:35,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.215, H2=7.458, H3=5.845
2025-07-24 14:16:35,764 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2125 sites
2025-07-24 14:16:39,516 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.342, H2=-2.650, H3=-9.986
2025-07-24 14:16:39,543 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 829 sites
2025-07-24 14:16:41,010 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.470, H2=0.247, H3=-4.813
2025-07-24 14:16:41,036 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 522 sites
2025-07-24 14:16:41,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=2.555, H2=-7.581, H3=2.917
2025-07-24 14:16:41,986 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1037 sites
2025-07-24 14:16:43,831 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=7.339, H2=2.887, H3=1.491
2025-07-24 14:16:43,863 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3272 sites
2025-07-24 14:16:49,671 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=4.763, H2=3.550, H3=11.047
2025-07-24 14:16:49,699 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 790 sites
2025-07-24 14:16:51,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-8.204, H2=-6.777, H3=-7.825
2025-07-24 14:16:51,123 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2295 sites
2025-07-24 14:16:55,178 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.220, H2=5.487, H3=-1.832
2025-07-24 14:16:55,225 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1168 sites
2025-07-24 14:16:57,286 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.214, H2=-5.308, H3=-3.379
2025-07-24 14:16:57,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1192 sites
2025-07-24 14:16:59,407 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=20.750, H2=1.736, H3=0.336
2025-07-24 14:16:59,434 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1412 sites
2025-07-24 14:17:01,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=39.526, H2=-0.723, H3=-0.254
2025-07-24 14:17:01,955 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4752 sites
2025-07-24 14:17:10,317 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=33.413, H2=-2.322, H3=-2.317
2025-07-24 14:17:10,347 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2389 sites
2025-07-24 14:17:14,591 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=14.201, H2=-3.611, H3=-0.830
2025-07-24 14:17:14,620 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2160 sites
2025-07-24 14:17:18,464 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=40.227, H2=-1.083, H3=1.097
2025-07-24 14:17:18,497 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4240 sites
2025-07-24 14:17:26,007 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=21.477, H2=1.846, H3=-1.540
2025-07-24 14:17:26,036 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2124 sites
2025-07-24 14:17:29,813 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=5.876, H2=2.138, H3=0.902
2025-07-24 14:17:29,843 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2389 sites
2025-07-24 14:17:34,134 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.028, H2=-0.851, H3=-1.627
2025-07-24 14:17:34,165 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3405 sites
2025-07-24 14:17:40,197 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=11.559, H2=4.795, H3=1.308
2025-07-24 14:17:40,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2660 sites
2025-07-24 14:17:44,944 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.178, H2=1.976, H3=2.639
2025-07-24 14:17:44,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1502 sites
2025-07-24 14:17:47,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-5.241, H2=-3.272, H3=-2.282
2025-07-24 14:17:47,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1013 sites
2025-07-24 14:17:49,453 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=3.169, H2=0.146, H3=-6.887
2025-07-24 14:17:49,481 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1624 sites
2025-07-24 14:17:52,340 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=0.650, H2=2.866, H3=2.340
2025-07-24 14:17:52,369 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2113 sites
2025-07-24 14:17:56,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=8.845, H2=3.528, H3=1.387
2025-07-24 14:17:56,113 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-07-24 14:17:56,113 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-07-24 14:17:56,115 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-07-24 14:17:56,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:18:02,649 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:18:02,681 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3670 sites
2025-07-24 14:18:09,118 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.215, H2=7.458, H3=5.845
2025-07-24 14:18:09,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2125 sites
2025-07-24 14:18:12,873 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.342, H2=-2.650, H3=-9.986
2025-07-24 14:18:12,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 829 sites
2025-07-24 14:18:14,354 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.470, H2=0.247, H3=-4.813
2025-07-24 14:18:14,381 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 344 sites
2025-07-24 14:18:14,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-2.638, H2=-6.295, H3=3.594
2025-07-24 14:18:15,013 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 178 sites
2025-07-24 14:18:15,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.985, H2=-4.972, H3=-0.665
2025-07-24 14:18:15,352 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1037 sites
2025-07-24 14:18:17,173 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.981, H2=2.543, H3=1.573
2025-07-24 14:18:17,203 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3272 sites
2025-07-24 14:18:22,961 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.437, H2=3.534, H3=7.195
2025-07-24 14:18:22,988 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 790 sites
2025-07-24 14:18:24,380 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-7.419, H2=-6.441, H3=-7.879
2025-07-24 14:18:24,410 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2295 sites
2025-07-24 14:18:28,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=14.981, H2=5.042, H3=-2.136
2025-07-24 14:18:28,489 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1168 sites
2025-07-24 14:18:30,546 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.709, H2=-4.177, H3=-4.886
2025-07-24 14:18:30,574 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1192 sites
2025-07-24 14:18:32,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=38.721, H2=1.838, H3=-0.177
2025-07-24 14:18:32,694 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1412 sites
2025-07-24 14:18:35,174 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=24.440, H2=-0.808, H3=-0.431
2025-07-24 14:18:35,208 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4752 sites
2025-07-24 14:18:43,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=47.577, H2=-2.495, H3=-1.705
2025-07-24 14:18:43,574 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2389 sites
2025-07-24 14:18:47,786 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=15.489, H2=-3.186, H3=-1.104
2025-07-24 14:18:47,816 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2160 sites
2025-07-24 14:18:51,668 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=38.811, H2=-0.740, H3=0.925
2025-07-24 14:18:51,701 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4240 sites
2025-07-24 14:18:59,192 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=19.396, H2=1.121, H3=-1.895
2025-07-24 14:18:59,222 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2124 sites
2025-07-24 14:19:02,968 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.067, H2=2.295, H3=1.674
2025-07-24 14:19:02,998 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2389 sites
2025-07-24 14:19:07,200 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=5.358, H2=-1.246, H3=-2.341
2025-07-24 14:19:07,231 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3405 sites
2025-07-24 14:19:13,193 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=10.964, H2=3.578, H3=2.147
2025-07-24 14:19:13,223 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2660 sites
2025-07-24 14:19:17,902 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=1.910, H2=2.141, H3=2.673
2025-07-24 14:19:17,930 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1502 sites
2025-07-24 14:19:20,564 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-3.987, H2=-3.485, H3=-2.467
2025-07-24 14:19:20,591 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1013 sites
2025-07-24 14:19:22,373 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=2.291, H2=-0.126, H3=-4.574
2025-07-24 14:19:22,402 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1624 sites
2025-07-24 14:19:25,251 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=0.631, H2=2.585, H3=3.473
2025-07-24 14:19:25,281 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 2113 sites
2025-07-24 14:19:28,998 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=9.293, H2=2.689, H3=0.726
2025-07-24 14:19:29,024 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-07-24 14:19:29,024 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-07-24 14:19:29,026 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-07-24 14:19:29,058 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:19:35,486 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:19:35,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3670 sites
2025-07-24 14:19:41,955 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.215, H2=7.458, H3=5.845
2025-07-24 14:19:41,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2125 sites
2025-07-24 14:19:45,719 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.342, H2=-2.650, H3=-9.986
2025-07-24 14:19:45,746 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 829 sites
2025-07-24 14:19:47,200 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.470, H2=0.247, H3=-4.813
2025-07-24 14:19:47,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 344 sites
2025-07-24 14:19:47,846 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-2.638, H2=-6.295, H3=3.594
2025-07-24 14:19:47,872 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 178 sites
2025-07-24 14:19:48,185 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.985, H2=-4.972, H3=-0.665
2025-07-24 14:19:48,212 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1037 sites
2025-07-24 14:19:50,037 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.981, H2=2.543, H3=1.573
2025-07-24 14:19:50,069 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3272 sites
2025-07-24 14:19:55,819 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.437, H2=3.534, H3=7.195
2025-07-24 14:19:55,846 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 790 sites
2025-07-24 14:19:57,234 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-7.419, H2=-6.441, H3=-7.879
2025-07-24 14:19:57,265 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2295 sites
2025-07-24 14:20:01,279 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=14.981, H2=5.042, H3=-2.136
2025-07-24 14:20:01,306 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1168 sites
2025-07-24 14:20:03,359 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.709, H2=-4.177, H3=-4.886
2025-07-24 14:20:03,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1192 sites
2025-07-24 14:20:05,465 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=38.721, H2=1.838, H3=-0.177
2025-07-24 14:20:05,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1412 sites
2025-07-24 14:20:07,992 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=24.440, H2=-0.808, H3=-0.431
2025-07-24 14:20:08,026 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4752 sites
2025-07-24 14:20:16,383 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=47.577, H2=-2.495, H3=-1.705
2025-07-24 14:20:16,413 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2389 sites
2025-07-24 14:20:20,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=15.489, H2=-3.186, H3=-1.104
2025-07-24 14:20:20,659 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2160 sites
2025-07-24 14:20:24,468 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=38.811, H2=-0.740, H3=0.925
2025-07-24 14:20:24,501 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4240 sites
2025-07-24 14:20:31,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=19.396, H2=1.121, H3=-1.895
2025-07-24 14:20:32,009 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2124 sites
2025-07-24 14:20:35,754 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=6.067, H2=2.295, H3=1.674
2025-07-24 14:20:35,784 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2389 sites
2025-07-24 14:20:39,995 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=5.358, H2=-1.246, H3=-2.341
2025-07-24 14:20:40,026 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3405 sites
2025-07-24 14:20:45,997 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=10.964, H2=3.578, H3=2.147
2025-07-24 14:20:46,027 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2660 sites
2025-07-24 14:20:50,708 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=1.910, H2=2.141, H3=2.673
2025-07-24 14:20:50,743 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1502 sites
2025-07-24 14:20:53,368 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-3.987, H2=-3.485, H3=-2.467
2025-07-24 14:20:53,395 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1013 sites
2025-07-24 14:20:55,190 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=2.291, H2=-0.126, H3=-4.574
2025-07-24 14:20:55,219 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1624 sites
2025-07-24 14:20:58,095 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=0.631, H2=2.585, H3=3.473
2025-07-24 14:20:58,122 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 651 sites
2025-07-24 14:20:59,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-0.979, H2=3.186, H3=-0.681
2025-07-24 14:20:59,301 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1462 sites
2025-07-24 14:21:01,998 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=6.960, H2=0.580, H3=1.094
2025-07-24 14:21:02,024 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-07-24 14:21:02,024 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-07-24 14:21:02,026 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-07-24 14:21:02,057 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:21:08,611 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:21:08,643 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3670 sites
2025-07-24 14:21:15,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.215, H2=7.458, H3=5.845
2025-07-24 14:21:15,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2125 sites
2025-07-24 14:21:18,977 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.342, H2=-2.650, H3=-9.986
2025-07-24 14:21:19,003 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 829 sites
2025-07-24 14:21:20,484 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.470, H2=0.247, H3=-4.813
2025-07-24 14:21:20,509 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 344 sites
2025-07-24 14:21:21,126 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-2.638, H2=-6.295, H3=3.594
2025-07-24 14:21:21,151 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 178 sites
2025-07-24 14:21:21,470 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.985, H2=-4.972, H3=-0.665
2025-07-24 14:21:21,497 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1037 sites
2025-07-24 14:21:23,340 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.981, H2=2.543, H3=1.573
2025-07-24 14:21:23,371 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3272 sites
2025-07-24 14:21:29,197 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.437, H2=3.534, H3=7.195
2025-07-24 14:21:29,224 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 790 sites
2025-07-24 14:21:30,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-7.419, H2=-6.441, H3=-7.879
2025-07-24 14:21:30,659 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2295 sites
2025-07-24 14:21:34,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=14.981, H2=5.042, H3=-2.136
2025-07-24 14:21:34,714 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1168 sites
2025-07-24 14:21:36,778 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.709, H2=-4.177, H3=-4.886
2025-07-24 14:21:36,804 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 373 sites
2025-07-24 14:21:37,464 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-5.510, H2=-1.620, H3=-2.591
2025-07-24 14:21:37,491 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 819 sites
2025-07-24 14:21:38,942 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=16.718, H2=0.454, H3=1.137
2025-07-24 14:21:38,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1412 sites
2025-07-24 14:21:41,474 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=25.903, H2=-0.790, H3=0.044
2025-07-24 14:21:41,507 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4752 sites
2025-07-24 14:21:49,887 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=41.119, H2=-1.887, H3=-1.600
2025-07-24 14:21:49,917 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2389 sites
2025-07-24 14:21:54,123 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=13.952, H2=-3.631, H3=-1.174
2025-07-24 14:21:54,152 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2160 sites
2025-07-24 14:21:57,979 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=34.122, H2=-0.535, H3=1.280
2025-07-24 14:21:58,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 4240 sites
2025-07-24 14:22:05,494 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=25.224, H2=1.329, H3=-1.094
2025-07-24 14:22:05,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2124 sites
2025-07-24 14:22:09,298 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=5.045, H2=1.689, H3=0.651
2025-07-24 14:22:09,328 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2389 sites
2025-07-24 14:22:13,513 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=5.283, H2=-1.084, H3=-2.302
2025-07-24 14:22:13,544 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3405 sites
2025-07-24 14:22:19,603 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=13.024, H2=5.594, H3=2.660
2025-07-24 14:22:19,633 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2660 sites
2025-07-24 14:22:24,307 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.663, H2=2.303, H3=3.641
2025-07-24 14:22:24,335 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1502 sites
2025-07-24 14:22:26,974 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.009, H2=-2.656, H3=-1.411
2025-07-24 14:22:27,001 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1013 sites
2025-07-24 14:22:28,780 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=2.815, H2=-0.168, H3=-5.604
2025-07-24 14:22:28,808 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1624 sites
2025-07-24 14:22:31,683 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=0.380, H2=1.921, H3=2.285
2025-07-24 14:22:31,710 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 651 sites
2025-07-24 14:22:32,873 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-1.082, H2=2.728, H3=-0.719
2025-07-24 14:22:32,901 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1462 sites
2025-07-24 14:22:35,478 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=7.608, H2=0.640, H3=0.968
2025-07-24 14:22:35,503 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-07-24 14:22:35,503 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-07-24 14:22:35,505 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-07-24 14:22:35,536 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:22:42,029 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:22:42,062 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3670 sites
2025-07-24 14:22:48,487 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.215, H2=7.458, H3=5.845
2025-07-24 14:22:48,517 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2125 sites
2025-07-24 14:22:52,253 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.342, H2=-2.650, H3=-9.986
2025-07-24 14:22:52,280 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 829 sites
2025-07-24 14:22:53,734 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.470, H2=0.247, H3=-4.813
2025-07-24 14:22:53,760 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 344 sites
2025-07-24 14:22:54,362 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-2.638, H2=-6.295, H3=3.594
2025-07-24 14:22:54,387 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 178 sites
2025-07-24 14:22:54,698 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.985, H2=-4.972, H3=-0.665
2025-07-24 14:22:54,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1037 sites
2025-07-24 14:22:56,550 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.981, H2=2.543, H3=1.573
2025-07-24 14:22:56,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3272 sites
2025-07-24 14:23:02,329 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.437, H2=3.534, H3=7.195
2025-07-24 14:23:02,355 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 790 sites
2025-07-24 14:23:03,752 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-7.419, H2=-6.441, H3=-7.879
2025-07-24 14:23:03,778 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 684 sites
2025-07-24 14:23:04,984 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-1.786, H2=-6.460, H3=-7.372
2025-07-24 14:23:05,012 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1611 sites
2025-07-24 14:23:07,833 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-3.043, H2=0.260, H3=2.562
2025-07-24 14:23:07,860 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1168 sites
2025-07-24 14:23:09,916 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.790, H2=-4.336, H3=-4.021
2025-07-24 14:23:09,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 373 sites
2025-07-24 14:23:10,607 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-2.808, H2=-1.544, H3=-2.664
2025-07-24 14:23:10,634 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 819 sites
2025-07-24 14:23:12,081 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=21.299, H2=0.076, H3=1.525
2025-07-24 14:23:12,110 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1412 sites
2025-07-24 14:23:14,582 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=36.387, H2=-0.928, H3=-0.486
2025-07-24 14:23:14,615 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4752 sites
2025-07-24 14:23:22,999 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=29.462, H2=-2.623, H3=-1.859
2025-07-24 14:23:23,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2389 sites
2025-07-24 14:23:27,240 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=10.837, H2=-2.822, H3=-1.182
2025-07-24 14:23:27,269 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2160 sites
2025-07-24 14:23:31,079 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=32.601, H2=-0.613, H3=2.062
2025-07-24 14:23:31,112 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 4240 sites
2025-07-24 14:23:38,601 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=18.966, H2=1.342, H3=-1.985
2025-07-24 14:23:38,631 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2124 sites
2025-07-24 14:23:42,355 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=6.296, H2=2.074, H3=1.114
2025-07-24 14:23:42,384 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2389 sites
2025-07-24 14:23:46,577 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=8.104, H2=-1.423, H3=-1.880
2025-07-24 14:23:46,608 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3405 sites
2025-07-24 14:23:52,602 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=14.417, H2=5.351, H3=1.955
2025-07-24 14:23:52,633 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2660 sites
2025-07-24 14:23:57,311 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=1.466, H2=2.402, H3=2.614
2025-07-24 14:23:57,339 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1502 sites
2025-07-24 14:23:59,983 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-3.209, H2=-4.669, H3=-2.049
2025-07-24 14:24:00,010 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1013 sites
2025-07-24 14:24:01,791 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=3.137, H2=-0.517, H3=-5.926
2025-07-24 14:24:01,819 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1624 sites
2025-07-24 14:24:04,675 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=0.278, H2=2.681, H3=3.349
2025-07-24 14:24:04,702 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 651 sites
2025-07-24 14:24:05,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.624, H2=2.101, H3=-1.058
2025-07-24 14:24:05,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1462 sites
2025-07-24 14:24:08,484 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=7.004, H2=0.819, H3=0.773
2025-07-24 14:24:08,509 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-07-24 14:24:08,510 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-07-24 14:24:08,529 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-07-24 14:24:08,562 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:24:15,066 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:24:15,098 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3670 sites
2025-07-24 14:24:21,608 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.215, H2=7.458, H3=5.845
2025-07-24 14:24:21,637 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2125 sites
2025-07-24 14:24:25,410 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.342, H2=-2.650, H3=-9.986
2025-07-24 14:24:25,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 829 sites
2025-07-24 14:24:26,923 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.470, H2=0.247, H3=-4.813
2025-07-24 14:24:26,949 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 344 sites
2025-07-24 14:24:27,563 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=-2.638, H2=-6.295, H3=3.594
2025-07-24 14:24:27,589 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 178 sites
2025-07-24 14:24:27,908 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=5.985, H2=-4.972, H3=-0.665
2025-07-24 14:24:27,935 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1037 sites
2025-07-24 14:24:29,801 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.981, H2=2.543, H3=1.573
2025-07-24 14:24:29,832 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3272 sites
2025-07-24 14:24:35,670 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.437, H2=3.534, H3=7.195
2025-07-24 14:24:35,697 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 790 sites
2025-07-24 14:24:37,109 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-7.419, H2=-6.441, H3=-7.879
2025-07-24 14:24:37,135 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 684 sites
2025-07-24 14:24:38,344 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-1.786, H2=-6.460, H3=-7.372
2025-07-24 14:24:38,372 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1611 sites
2025-07-24 14:24:41,242 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-3.043, H2=0.260, H3=2.562
2025-07-24 14:24:41,270 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1168 sites
2025-07-24 14:24:43,330 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=12.790, H2=-4.336, H3=-4.021
2025-07-24 14:24:43,356 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 373 sites
2025-07-24 14:24:44,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-2.808, H2=-1.544, H3=-2.664
2025-07-24 14:24:44,043 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 819 sites
2025-07-24 14:24:45,480 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=21.299, H2=0.076, H3=1.525
2025-07-24 14:24:45,507 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1412 sites
2025-07-24 14:24:48,007 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=36.387, H2=-0.928, H3=-0.486
2025-07-24 14:24:48,041 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4752 sites
2025-07-24 14:24:56,437 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=29.462, H2=-2.623, H3=-1.859
2025-07-24 14:24:56,467 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2389 sites
2025-07-24 14:25:00,688 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=10.837, H2=-2.822, H3=-1.182
2025-07-24 14:25:00,717 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2160 sites
2025-07-24 14:25:04,525 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=32.601, H2=-0.613, H3=2.062
2025-07-24 14:25:04,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2293 sites
2025-07-24 14:25:08,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=11.171, H2=-0.276, H3=-6.062
2025-07-24 14:25:08,634 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1947 sites
2025-07-24 14:25:12,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=14.069, H2=1.185, H3=3.714
2025-07-24 14:25:12,100 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2124 sites
2025-07-24 14:25:15,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=11.007, H2=1.981, H3=1.056
2025-07-24 14:25:15,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2389 sites
2025-07-24 14:25:20,110 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=7.103, H2=-0.827, H3=-2.335
2025-07-24 14:25:20,141 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3405 sites
2025-07-24 14:25:26,146 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=10.734, H2=4.350, H3=2.094
2025-07-24 14:25:26,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 2660 sites
2025-07-24 14:25:30,900 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=1.829, H2=3.355, H3=4.619
2025-07-24 14:25:30,928 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1502 sites
2025-07-24 14:25:33,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-4.539, H2=-2.577, H3=-1.673
2025-07-24 14:25:33,617 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1013 sites
2025-07-24 14:25:35,408 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=1.769, H2=-0.347, H3=-5.384
2025-07-24 14:25:35,436 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1624 sites
2025-07-24 14:25:38,323 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=0.840, H2=1.737, H3=2.714
2025-07-24 14:25:38,349 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 651 sites
2025-07-24 14:25:39,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-0.977, H2=3.040, H3=-0.680
2025-07-24 14:25:39,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1462 sites
2025-07-24 14:25:42,115 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=8.899, H2=0.324, H3=0.988
2025-07-24 14:25:42,140 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-07-24 14:25:42,140 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-07-24 14:25:42,142 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-07-24 14:25:42,183 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 3676 sites
2025-07-24 14:25:48,673 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=7.179, H2=5.903, H3=4.172
2025-07-24 14:25:48,705 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3670 sites
2025-07-24 14:25:55,144 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=11.215, H2=7.458, H3=5.845
2025-07-24 14:25:55,174 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2125 sites
2025-07-24 14:25:58,901 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=14.342, H2=-2.650, H3=-9.986
2025-07-24 14:25:58,927 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 320 sites
2025-07-24 14:25:59,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=8.870, H2=-5.311, H3=-5.571
2025-07-24 14:25:59,520 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 509 sites
2025-07-24 14:26:00,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.556, H2=1.303, H3=-4.847
2025-07-24 14:26:00,445 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 344 sites
2025-07-24 14:26:01,053 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=-3.003, H2=-7.437, H3=3.548
2025-07-24 14:26:01,078 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 178 sites
2025-07-24 14:26:01,393 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=7.254, H2=-4.176, H3=-1.113
2025-07-24 14:26:01,421 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1037 sites
2025-07-24 14:26:03,258 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=7.579, H2=2.309, H3=1.057
2025-07-24 14:26:03,289 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3272 sites
2025-07-24 14:26:09,031 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.307, H2=5.080, H3=8.501
2025-07-24 14:26:09,058 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 790 sites
2025-07-24 14:26:10,446 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-6.723, H2=-5.497, H3=-6.524
2025-07-24 14:26:10,473 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 684 sites
2025-07-24 14:26:11,671 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-1.483, H2=-5.371, H3=-6.117
2025-07-24 14:26:11,700 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1611 sites
2025-07-24 14:26:14,530 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-4.436, H2=0.174, H3=2.229
2025-07-24 14:26:14,557 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1168 sites
2025-07-24 14:26:16,612 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.863, H2=-5.501, H3=-3.636
2025-07-24 14:26:16,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 373 sites
2025-07-24 14:26:17,292 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.514, H2=-2.155, H3=-3.959
2025-07-24 14:26:17,319 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 819 sites
2025-07-24 14:26:18,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=21.000, H2=0.228, H3=1.481
2025-07-24 14:26:18,784 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1412 sites
2025-07-24 14:26:21,271 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=24.140, H2=-0.331, H3=-0.327
2025-07-24 14:26:21,305 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4752 sites
