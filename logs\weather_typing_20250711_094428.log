2025-07-11 09:44:28,165 - __main__ - INFO - run_heterogeneity_analysis.py:525 - Starting heterogeneity analysis
2025-07-11 09:44:28,166 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-11 09:44:28,166 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Cluster data: regionalization_w_WT_2_hierarchical/CI_results_hierarchical.nc
2025-07-11 09:44:28,166 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Output directory: heterogeneity_results/regionalization_w_WT_2_hierarchical
2025-07-11 09:44:28,166 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Maximum clusters: 125
2025-07-11 09:44:28,166 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Number of simulations: 50
2025-07-11 09:44:28,166 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Remap clusters: True
2025-07-11 09:44:28,166 - __main__ - INFO - run_heterogeneity_analysis.py:540 - Remapping clusters from 1D to 2D grid format
2025-07-11 09:44:28,166 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_2_hierarchical/CI_results_hierarchical.nc
2025-07-11 09:44:28,166 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-11 09:44:30,356 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-11 09:44:31,155 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-11 09:44:31,194 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-11 09:44:31,220 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-11 09:44:31,223 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-11 09:44:31,249 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-11 09:44:31,304 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-11 09:44:31,331 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-11 09:44:31,345 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-11 09:44:31,397 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-11 09:44:31,399 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-11 09:44:31,401 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-11 09:44:31,444 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-11 09:44:31,445 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-11 09:44:31,447 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-11 09:44:31,449 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-11 09:44:31,508 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-11 09:44:31,509 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-11 09:44:31,511 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-11 09:44:31,516 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-11 09:44:31,516 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-11 09:44:31,518 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-11 09:44:31,519 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-11 09:44:31,519 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-11 09:44:31,521 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-11 09:44:31,523 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-11 09:44:31,524 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-11 09:44:31,526 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-11 09:44:31,527 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-11 09:44:31,529 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-11 09:44:31,531 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-11 09:44:31,533 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-11 09:44:31,535 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-11 09:44:31,537 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-11 09:44:31,538 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-11 09:44:31,540 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-11 09:44:31,541 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-11 09:44:31,543 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-11 09:44:31,545 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-11 09:44:31,545 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-11 09:44:31,545 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-11 09:44:31,680 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-07-11 09:44:31,682 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-11 09:44:31,686 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-11 09:44:31,686 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-11 09:44:31,688 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-11 09:44:31,688 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-11 09:44:31,692 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-11 09:44:31,692 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-11 09:44:31,695 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-11 09:44:31,695 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-11 09:44:31,698 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-11 09:44:31,698 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-11 09:44:31,701 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-11 09:44:31,701 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-11 09:44:31,705 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-11 09:44:31,705 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-11 09:44:31,707 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-11 09:44:31,707 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-11 09:44:31,711 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-11 09:44:31,711 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-11 09:44:31,714 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-11 09:44:31,714 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-11 09:44:31,717 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-11 09:44:31,717 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-11 09:44:31,720 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-11 09:44:31,720 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-11 09:44:31,723 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-11 09:44:31,723 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-11 09:44:31,727 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-11 09:44:31,727 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-11 09:44:31,730 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-11 09:44:31,730 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-11 09:44:31,732 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-11 09:44:31,733 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-11 09:44:31,736 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-11 09:44:31,736 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-11 09:44:31,739 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-11 09:44:31,739 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-11 09:44:31,742 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-11 09:44:31,742 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-11 09:44:31,745 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-11 09:44:31,745 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-11 09:44:31,748 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-11 09:44:31,748 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-11 09:44:31,751 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-11 09:44:31,752 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-11 09:44:31,755 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-11 09:44:31,755 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-11 09:44:31,757 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-11 09:44:31,757 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-11 09:44:31,761 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-11 09:44:31,761 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-11 09:44:31,764 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-11 09:44:31,764 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-11 09:44:31,767 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-11 09:44:31,767 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-11 09:44:31,770 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-11 09:44:31,770 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-11 09:44:31,773 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-11 09:44:31,773 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-11 09:44:31,776 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-11 09:44:31,776 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-11 09:44:31,779 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-11 09:44:31,779 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-11 09:44:31,782 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-11 09:44:31,782 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-11 09:44:31,785 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-11 09:44:31,785 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-11 09:44:31,788 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-11 09:44:31,788 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-11 09:44:31,791 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-11 09:44:31,791 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-11 09:44:31,794 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-11 09:44:31,794 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-11 09:44:31,797 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-11 09:44:31,797 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-11 09:44:31,801 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-11 09:44:31,801 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-11 09:44:31,804 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-11 09:44:31,804 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-11 09:44:31,806 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-11 09:44:31,806 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-11 09:44:31,809 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-11 09:44:31,810 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-11 09:44:31,813 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-11 09:44:31,813 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-11 09:44:31,815 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-11 09:44:31,815 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-11 09:44:31,818 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-11 09:44:31,819 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-11 09:44:31,822 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-11 09:44:31,822 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-11 09:44:31,825 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-11 09:44:31,825 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-11 09:44:31,828 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-11 09:44:31,828 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-11 09:44:31,830 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-11 09:44:31,831 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-11 09:44:31,834 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-11 09:44:31,834 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-11 09:44:31,837 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-11 09:44:31,837 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-11 09:44:31,839 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-11 09:44:31,839 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-11 09:44:31,842 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-11 09:44:31,843 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-11 09:44:31,846 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-11 09:44:31,846 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-11 09:44:31,849 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-11 09:44:31,849 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-11 09:44:31,852 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-11 09:44:31,852 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-11 09:44:31,854 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-11 09:44:31,855 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-11 09:44:31,858 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-11 09:44:31,858 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-11 09:44:31,861 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-11 09:44:31,861 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-11 09:44:31,863 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-11 09:44:31,863 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-11 09:44:31,866 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-11 09:44:31,866 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-11 09:44:31,870 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-11 09:44:31,870 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-11 09:44:31,873 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-11 09:44:31,873 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-11 09:44:31,876 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-11 09:44:31,876 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-07-11 09:44:31,878 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-07-11 09:44:31,878 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-07-11 09:44:31,881 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-07-11 09:44:31,882 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-07-11 09:44:31,885 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-07-11 09:44:31,885 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-07-11 09:44:31,887 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-07-11 09:44:31,887 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-07-11 09:44:31,890 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-07-11 09:44:31,890 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-07-11 09:44:31,893 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-07-11 09:44:31,893 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-07-11 09:44:31,896 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-07-11 09:44:31,897 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-07-11 09:44:31,900 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-07-11 09:44:31,900 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-07-11 09:44:31,902 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-07-11 09:44:31,902 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-07-11 09:44:31,905 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-07-11 09:44:31,905 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-07-11 09:44:31,908 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-07-11 09:44:31,908 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-07-11 09:44:31,911 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-07-11 09:44:31,912 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-07-11 09:44:31,915 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-07-11 09:44:31,915 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-07-11 09:44:31,917 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-07-11 09:44:31,917 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-07-11 09:44:31,920 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-07-11 09:44:31,920 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-07-11 09:44:31,923 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-07-11 09:44:31,923 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-07-11 09:44:31,941 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-07-11 09:44:31,942 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-07-11 09:44:32,110 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-07-11 09:44:32,110 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-07-11 09:44:32,113 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-07-11 09:44:32,113 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-07-11 09:44:32,116 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-07-11 09:44:32,116 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-07-11 09:44:32,119 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-07-11 09:44:32,120 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-07-11 09:44:32,122 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-07-11 09:44:32,122 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-07-11 09:44:32,125 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-07-11 09:44:32,125 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-07-11 09:44:32,128 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-07-11 09:44:32,128 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-07-11 09:44:32,131 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-07-11 09:44:32,131 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-07-11 09:44:32,134 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-07-11 09:44:32,134 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-07-11 09:44:32,137 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-07-11 09:44:32,137 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-07-11 09:44:32,140 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-07-11 09:44:32,140 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-07-11 09:44:32,143 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-07-11 09:44:32,143 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-07-11 09:44:32,145 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-07-11 09:44:32,146 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-07-11 09:44:32,149 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-07-11 09:44:32,149 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-07-11 09:44:32,152 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-07-11 09:44:32,152 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-07-11 09:44:32,154 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-07-11 09:44:32,154 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-07-11 09:44:32,157 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-07-11 09:44:32,157 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-07-11 09:44:32,160 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-07-11 09:44:32,160 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-07-11 09:44:32,163 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-07-11 09:44:32,163 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-07-11 09:44:32,166 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-07-11 09:44:32,166 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-07-11 09:44:32,169 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-07-11 09:44:32,169 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-07-11 09:44:32,172 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-07-11 09:44:32,172 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-07-11 09:44:32,175 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-07-11 09:44:32,175 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-07-11 09:44:32,177 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-07-11 09:44:32,177 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-07-11 09:44:32,180 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-07-11 09:44:32,181 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-07-11 09:44:32,183 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-07-11 09:44:32,184 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-07-11 09:44:32,186 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-07-11 09:44:32,187 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-07-11 09:44:32,189 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-07-11 09:44:32,190 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-07-11 09:44:32,192 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-07-11 09:44:32,192 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-07-11 09:44:32,195 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-07-11 09:44:32,195 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-07-11 09:44:32,198 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-07-11 09:44:32,198 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-07-11 09:44:32,201 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-07-11 09:44:32,201 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-07-11 09:44:32,204 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-07-11 09:44:32,204 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-07-11 09:44:32,207 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-07-11 09:44:32,207 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-07-11 09:44:32,210 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-07-11 09:44:32,210 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-07-11 09:44:32,213 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-07-11 09:44:32,213 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-07-11 09:44:32,215 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-07-11 09:44:32,215 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-07-11 09:44:32,218 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-07-11 09:44:32,218 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-07-11 09:44:32,221 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-07-11 09:44:32,221 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-07-11 09:44:32,224 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-07-11 09:44:32,224 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-07-11 09:44:32,226 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-07-11 09:44:32,227 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-07-11 09:44:32,229 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-07-11 09:44:32,230 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-07-11 09:44:32,232 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-07-11 09:44:32,232 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-07-11 09:44:32,235 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-07-11 09:44:32,235 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-07-11 09:44:32,238 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-07-11 09:44:32,238 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_2_hierarchical/CI_results_hierarchical_remap.nc
2025-07-11 09:44:32,434 - __main__ - ERROR - run_heterogeneity_analysis.py:550 - Failed to remap clusters: [Errno 13] Permission denied: b'/lcrc/project/Hydro-model/sgev/GDO/test2/regionalization_w_WT_2_hierarchical/CI_results_hierarchical_remap.nc'
2025-07-11 09:44:32,434 - __main__ - INFO - run_heterogeneity_analysis.py:551 - Continuing with original cluster file
2025-07-11 09:44:32,436 - __main__ - INFO - run_heterogeneity_analysis.py:554 - Loading data
2025-07-11 09:44:32,437 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-11 09:44:32,498 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-07-11 09:44:42,208 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_2_hierarchical/CI_results_hierarchical.nc
2025-07-11 09:44:42,586 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-07-11 09:44:42,586 - __main__ - INFO - run_heterogeneity_analysis.py:558 - Running heterogeneity analysis
2025-07-11 09:44:42,586 - __main__ - INFO - run_heterogeneity_analysis.py:562 - Processing k=2
2025-07-11 09:44:42,587 - __main__ - WARNING - run_heterogeneity_analysis.py:575 - Region [1 2]
2025-07-11 09:44:42,587 - __main__ - ERROR - run_heterogeneity_analysis.py:603 - Error in heterogeneity analysis: too many indices for array: array is 1-dimensional, but 2 were indexed
Traceback (most recent call last):
  File "/lcrc/project/Hydro-model/sgev/GDO/test2/src/models/run_heterogeneity_analysis.py", line 578, in main
    region_data = extract_region_data(precip_data, cluster_assignments, region_id)
  File "/lcrc/project/Hydro-model/sgev/GDO/test2/src/models/run_heterogeneity_analysis.py", line 173, in extract_region_data
    if region_mask[i, j]:
IndexError: too many indices for array: array is 1-dimensional, but 2 were indexed
