2025-08-04 14:44:49,321 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-08-04 14:44:49,321 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-04 14:44:49,321 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_13_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-04 14:44:49,321 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_13_hierarchical_singlevariable
2025-08-04 14:44:49,321 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-08-04 14:44:49,321 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-08-04 14:44:49,322 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-08-04 14:44:49,324 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-08-04 14:44:49,325 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_13_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-04 14:44:49,325 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-08-04 14:44:50,264 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-08-04 14:44:50,525 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-08-04 14:44:50,529 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-08-04 14:44:50,531 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-08-04 14:44:50,534 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-08-04 14:44:50,535 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-08-04 14:44:50,536 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-08-04 14:44:50,538 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-08-04 14:44:50,560 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-08-04 14:44:50,615 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-08-04 14:44:50,617 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-08-04 14:44:50,620 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-08-04 14:44:50,622 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-08-04 14:44:50,623 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-08-04 14:44:50,625 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-08-04 14:44:50,627 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-08-04 14:44:50,628 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-08-04 14:44:50,629 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-08-04 14:44:50,631 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-08-04 14:44:50,635 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-08-04 14:44:50,635 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-08-04 14:44:50,637 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-08-04 14:44:50,638 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-08-04 14:44:50,638 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-08-04 14:44:50,640 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-08-04 14:44:50,642 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-08-04 14:44:50,643 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-08-04 14:44:50,645 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-08-04 14:44:50,647 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-08-04 14:44:50,649 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-08-04 14:44:50,652 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-08-04 14:44:50,654 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-08-04 14:44:50,656 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-08-04 14:44:50,658 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-08-04 14:44:50,660 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-08-04 14:44:50,661 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-08-04 14:44:50,664 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-08-04 14:44:50,666 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-08-04 14:44:50,668 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-08-04 14:44:50,679 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-08-04 14:44:50,679 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-08-04 14:44:50,679 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-08-04 14:44:50,680 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 125 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100', 'k=101', 'k=102', 'k=103', 'k=104', 'k=105', 'k=106', 'k=107', 'k=108', 'k=109', 'k=110', 'k=111', 'k=112', 'k=113', 'k=114', 'k=115', 'k=116', 'k=117', 'k=118', 'k=119', 'k=120', 'k=121', 'k=122', 'k=123', 'k=124', 'k=125']
2025-08-04 14:44:50,681 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-08-04 14:44:50,685 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-08-04 14:44:50,685 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-08-04 14:44:50,687 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-08-04 14:44:50,687 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-08-04 14:44:50,689 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-08-04 14:44:50,689 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-08-04 14:44:50,691 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-08-04 14:44:50,691 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-08-04 14:44:50,693 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-08-04 14:44:50,693 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-08-04 14:44:50,696 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-08-04 14:44:50,696 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-08-04 14:44:50,698 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-08-04 14:44:50,699 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-08-04 14:44:50,702 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-08-04 14:44:50,702 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-08-04 14:44:50,705 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-08-04 14:44:50,705 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-08-04 14:44:50,707 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-08-04 14:44:50,707 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-08-04 14:44:50,710 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-08-04 14:44:50,711 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-08-04 14:44:50,714 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-08-04 14:44:50,714 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-08-04 14:44:50,717 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-08-04 14:44:50,717 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-08-04 14:44:50,720 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-08-04 14:44:50,720 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-08-04 14:44:50,722 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-08-04 14:44:50,723 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-08-04 14:44:50,725 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-08-04 14:44:50,726 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-08-04 14:44:50,729 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-08-04 14:44:50,729 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-08-04 14:44:50,731 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-08-04 14:44:50,731 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-08-04 14:44:50,734 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-08-04 14:44:50,734 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-08-04 14:44:50,737 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-08-04 14:44:50,737 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-08-04 14:44:50,740 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-08-04 14:44:50,741 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-08-04 14:44:50,743 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-08-04 14:44:50,744 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-08-04 14:44:50,746 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-08-04 14:44:50,746 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-08-04 14:44:50,749 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-08-04 14:44:50,749 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-08-04 14:44:50,752 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-08-04 14:44:50,752 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-08-04 14:44:50,755 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-08-04 14:44:50,755 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-08-04 14:44:50,758 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-08-04 14:44:50,758 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-08-04 14:44:50,761 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-08-04 14:44:50,761 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-08-04 14:44:50,764 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-08-04 14:44:50,764 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-08-04 14:44:50,767 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-08-04 14:44:50,767 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-08-04 14:44:50,769 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-08-04 14:44:50,770 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-08-04 14:44:50,772 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-08-04 14:44:50,773 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-08-04 14:44:50,775 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-08-04 14:44:50,776 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-08-04 14:44:50,778 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-08-04 14:44:50,778 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-08-04 14:44:50,781 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-08-04 14:44:50,781 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-08-04 14:44:50,784 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-08-04 14:44:50,784 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-08-04 14:44:50,787 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-08-04 14:44:50,787 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-08-04 14:44:50,790 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-08-04 14:44:50,790 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-08-04 14:44:50,793 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-08-04 14:44:50,793 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-08-04 14:44:50,796 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-08-04 14:44:50,796 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-08-04 14:44:50,799 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-08-04 14:44:50,799 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-08-04 14:44:50,801 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-08-04 14:44:50,801 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-08-04 14:44:50,804 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-08-04 14:44:50,804 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-08-04 14:44:50,807 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-08-04 14:44:50,807 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-08-04 14:44:50,810 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-08-04 14:44:50,810 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-08-04 14:44:50,813 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-08-04 14:44:50,813 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-08-04 14:44:50,816 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-08-04 14:44:50,816 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-08-04 14:44:50,819 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-08-04 14:44:50,819 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-08-04 14:44:50,822 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-08-04 14:44:50,822 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-08-04 14:44:50,825 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-08-04 14:44:50,825 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-08-04 14:44:50,828 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-08-04 14:44:50,828 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-08-04 14:44:50,830 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-08-04 14:44:50,830 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-08-04 14:44:50,833 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-08-04 14:44:50,833 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-08-04 14:44:50,836 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-08-04 14:44:50,836 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-08-04 14:44:50,839 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-08-04 14:44:50,839 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-08-04 14:44:50,842 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-08-04 14:44:50,842 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-08-04 14:44:50,845 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-08-04 14:44:50,845 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-08-04 14:44:50,848 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-08-04 14:44:50,848 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-08-04 14:44:50,851 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-08-04 14:44:50,851 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-08-04 14:44:50,853 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-08-04 14:44:50,853 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-08-04 14:44:50,856 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-08-04 14:44:50,856 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-08-04 14:44:50,859 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-08-04 14:44:50,859 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-08-04 14:44:50,861 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-08-04 14:44:50,862 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-08-04 14:44:50,864 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-08-04 14:44:50,865 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-08-04 14:44:50,867 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-08-04 14:44:50,868 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-08-04 14:44:50,870 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-08-04 14:44:50,871 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-08-04 14:44:50,873 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-08-04 14:44:50,874 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-08-04 14:44:50,876 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-08-04 14:44:50,876 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-08-04 14:44:50,879 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-08-04 14:44:50,879 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-08-04 14:44:50,882 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-08-04 14:44:50,882 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-08-04 14:44:50,884 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-08-04 14:44:50,884 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-08-04 14:44:50,887 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-08-04 14:44:50,887 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-08-04 14:44:50,890 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-08-04 14:44:50,890 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-08-04 14:44:50,893 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-08-04 14:44:50,893 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-08-04 14:44:50,896 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-08-04 14:44:50,896 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-08-04 14:44:50,898 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-08-04 14:44:50,899 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-08-04 14:44:50,901 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-08-04 14:44:50,901 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-08-04 14:44:50,904 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-08-04 14:44:50,904 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-08-04 14:44:50,907 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-08-04 14:44:50,907 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-08-04 14:44:50,928 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-08-04 14:44:50,929 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-08-04 14:44:51,215 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-08-04 14:44:51,215 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-08-04 14:44:51,218 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-08-04 14:44:51,218 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-08-04 14:44:51,221 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-08-04 14:44:51,221 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-08-04 14:44:51,223 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-08-04 14:44:51,223 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-08-04 14:44:51,226 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-08-04 14:44:51,226 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-08-04 14:44:51,229 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-08-04 14:44:51,229 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-08-04 14:44:51,231 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-08-04 14:44:51,231 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-08-04 14:44:51,234 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-08-04 14:44:51,234 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-08-04 14:44:51,237 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-08-04 14:44:51,237 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-08-04 14:44:51,240 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-08-04 14:44:51,240 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-08-04 14:44:51,243 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-08-04 14:44:51,243 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-08-04 14:44:51,245 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-08-04 14:44:51,245 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-08-04 14:44:51,248 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-08-04 14:44:51,248 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-08-04 14:44:51,251 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-08-04 14:44:51,251 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-08-04 14:44:51,254 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-08-04 14:44:51,254 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-08-04 14:44:51,256 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-08-04 14:44:51,257 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-08-04 14:44:51,259 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-08-04 14:44:51,260 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-08-04 14:44:51,262 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-08-04 14:44:51,262 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-08-04 14:44:51,265 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-08-04 14:44:51,265 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-08-04 14:44:51,268 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-08-04 14:44:51,268 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=101
2025-08-04 14:44:51,270 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=101
2025-08-04 14:44:51,271 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=102
2025-08-04 14:44:51,273 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=102
2025-08-04 14:44:51,274 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=103
2025-08-04 14:44:51,276 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=103
2025-08-04 14:44:51,276 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=104
2025-08-04 14:44:51,279 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=104
2025-08-04 14:44:51,279 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=105
2025-08-04 14:44:51,282 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=105
2025-08-04 14:44:51,282 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=106
2025-08-04 14:44:51,284 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=106
2025-08-04 14:44:51,285 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=107
2025-08-04 14:44:51,287 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=107
2025-08-04 14:44:51,288 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=108
2025-08-04 14:44:51,290 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=108
2025-08-04 14:44:51,290 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=109
2025-08-04 14:44:51,293 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=109
2025-08-04 14:44:51,293 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=110
2025-08-04 14:44:51,295 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=110
2025-08-04 14:44:51,296 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=111
2025-08-04 14:44:51,298 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=111
2025-08-04 14:44:51,298 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=112
2025-08-04 14:44:51,301 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=112
2025-08-04 14:44:51,301 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=113
2025-08-04 14:44:51,304 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=113
2025-08-04 14:44:51,304 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=114
2025-08-04 14:44:51,306 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=114
2025-08-04 14:44:51,307 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=115
2025-08-04 14:44:51,309 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=115
2025-08-04 14:44:51,310 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=116
2025-08-04 14:44:51,312 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=116
2025-08-04 14:44:51,312 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=117
2025-08-04 14:44:51,315 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=117
2025-08-04 14:44:51,315 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=118
2025-08-04 14:44:51,317 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=118
2025-08-04 14:44:51,318 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=119
2025-08-04 14:44:51,320 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=119
2025-08-04 14:44:51,320 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=120
2025-08-04 14:44:51,323 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=120
2025-08-04 14:44:51,323 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=121
2025-08-04 14:44:51,326 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=121
2025-08-04 14:44:51,326 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=122
2025-08-04 14:44:51,328 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=122
2025-08-04 14:44:51,329 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=123
2025-08-04 14:44:51,331 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=123
2025-08-04 14:44:51,331 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=124
2025-08-04 14:44:51,334 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=124
2025-08-04 14:44:51,334 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=125
2025-08-04 14:44:51,336 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=125
2025-08-04 14:44:51,337 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_13_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-04 14:44:52,302 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-08-04 14:44:52,302 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-08-04 14:44:52,302 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-08-04 14:44:52,303 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_13_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-04 14:44:52,303 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_13_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-04 14:44:52,303 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 125 variables
2025-08-04 14:44:52,303 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100, k=101, k=102, k=103, k=104, k=105, k=106, k=107, k=108, k=109, k=110, k=111, k=112, k=113, k=114, k=115, k=116, k=117, k=118, k=119, k=120, k=121, k=122, k=123, k=124, k=125
2025-08-04 14:44:52,303 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-08-04 14:44:52,306 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_13_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-04 14:44:52,306 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-08-04 14:44:52,306 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-04 14:44:52,312 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-08-04 14:44:58,288 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_13_hierarchical_onevariable/CI_results_hierarchical_remap.nc
2025-08-04 14:44:58,321 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-08-04 14:44:58,321 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-08-04 14:44:58,321 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-08-04 14:44:58,342 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-08-04 14:44:58,398 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16327 sites
2025-08-04 14:45:27,378 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=88.752, H2=0.109, H3=-0.437
2025-08-04 14:45:27,486 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 36032 sites
2025-08-04 14:46:31,657 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=102.242, H2=24.024, H3=8.505
2025-08-04 14:46:31,684 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-08-04 14:46:31,684 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-08-04 14:46:31,686 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-08-04 14:46:31,742 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16327 sites
2025-08-04 14:47:00,598 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=88.752, H2=0.109, H3=-0.437
2025-08-04 14:47:00,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 18122 sites
2025-08-04 14:47:32,768 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=48.280, H2=13.773, H3=2.459
2025-08-04 14:47:32,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 17910 sites
2025-08-04 14:48:04,488 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=119.156, H2=32.990, H3=6.949
2025-08-04 14:48:04,514 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-08-04 14:48:04,514 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-08-04 14:48:04,516 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-08-04 14:48:04,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16327 sites
2025-08-04 14:48:33,406 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=88.752, H2=0.109, H3=-0.437
2025-08-04 14:48:33,465 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 18122 sites
2025-08-04 14:49:05,616 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=48.280, H2=13.773, H3=2.459
2025-08-04 14:49:05,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2321 sites
2025-08-04 14:49:09,723 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=50.425, H2=0.675, H3=-3.806
2025-08-04 14:49:09,777 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 15589 sites
2025-08-04 14:49:37,327 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=56.977, H2=14.613, H3=8.439
2025-08-04 14:49:37,352 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-08-04 14:49:37,352 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-08-04 14:49:37,354 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-08-04 14:49:37,410 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16327 sites
2025-08-04 14:50:06,359 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=88.752, H2=0.109, H3=-0.437
2025-08-04 14:50:06,402 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8380 sites
2025-08-04 14:50:21,296 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.138, H2=4.505, H3=1.758
2025-08-04 14:50:21,340 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 9742 sites
2025-08-04 14:50:38,757 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=39.319, H2=14.548, H3=2.007
2025-08-04 14:50:38,787 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2321 sites
2025-08-04 14:50:42,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=47.824, H2=0.985, H3=-2.836
2025-08-04 14:50:43,008 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 15589 sites
2025-08-04 14:51:11,093 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=75.008, H2=13.335, H3=6.767
2025-08-04 14:51:11,119 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-08-04 14:51:11,119 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-08-04 14:51:11,121 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-08-04 14:51:11,176 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16327 sites
2025-08-04 14:51:40,485 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=88.752, H2=0.109, H3=-0.437
2025-08-04 14:51:40,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8380 sites
2025-08-04 14:51:55,649 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.138, H2=4.505, H3=1.758
2025-08-04 14:51:55,693 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 9742 sites
2025-08-04 14:52:13,241 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=39.319, H2=14.548, H3=2.007
2025-08-04 14:52:13,271 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2321 sites
2025-08-04 14:52:17,438 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=47.824, H2=0.985, H3=-2.836
2025-08-04 14:52:17,481 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9512 sites
2025-08-04 14:52:34,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=43.611, H2=12.818, H3=7.496
2025-08-04 14:52:34,519 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6077 sites
2025-08-04 14:52:45,303 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=30.099, H2=5.388, H3=1.854
2025-08-04 14:52:45,328 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-08-04 14:52:45,328 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-08-04 14:52:45,330 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-08-04 14:52:45,385 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16327 sites
2025-08-04 14:53:14,478 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=88.752, H2=0.109, H3=-0.437
2025-08-04 14:53:14,520 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8380 sites
2025-08-04 14:53:29,441 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.138, H2=4.505, H3=1.758
2025-08-04 14:53:29,486 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 9742 sites
2025-08-04 14:53:46,813 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=39.319, H2=14.548, H3=2.007
2025-08-04 14:53:46,843 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2321 sites
2025-08-04 14:53:50,970 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=47.824, H2=0.985, H3=-2.836
2025-08-04 14:53:51,013 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9512 sites
2025-08-04 14:54:07,936 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=43.611, H2=12.818, H3=7.496
2025-08-04 14:54:07,968 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3509 sites
2025-08-04 14:54:14,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=2.427, H2=1.186, H3=2.271
2025-08-04 14:54:14,238 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2568 sites
2025-08-04 14:54:18,796 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.513, H2=8.656, H3=0.610
2025-08-04 14:54:18,821 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-08-04 14:54:18,821 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-08-04 14:54:18,823 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-08-04 14:54:18,879 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 16327 sites
2025-08-04 14:54:48,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=88.752, H2=0.109, H3=-0.437
2025-08-04 14:54:48,057 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 8380 sites
2025-08-04 14:55:02,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=17.138, H2=4.505, H3=1.758
2025-08-04 14:55:02,966 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 9742 sites
2025-08-04 14:55:20,267 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=39.319, H2=14.548, H3=2.007
2025-08-04 14:55:20,294 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 547 sites
2025-08-04 14:55:21,260 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=2.313, H2=-8.508, H3=2.027
2025-08-04 14:55:21,288 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1774 sites
2025-08-04 14:55:24,419 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=35.141, H2=3.579, H3=-5.733
2025-08-04 14:55:24,462 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 9512 sites
2025-08-04 14:55:41,282 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=32.745, H2=12.551, H3=7.699
2025-08-04 14:55:41,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3509 sites
2025-08-04 14:55:47,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=2.252, H2=1.688, H3=2.839
2025-08-04 14:55:47,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2568 sites
2025-08-04 14:55:52,127 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=7.911, H2=6.095, H3=-0.104
2025-08-04 14:55:52,152 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-08-04 14:55:52,153 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-08-04 14:55:52,154 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-08-04 14:55:52,200 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5548 sites
2025-08-04 14:56:02,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=59.158, H2=-3.187, H3=0.452
2025-08-04 14:56:02,103 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 10779 sites
2025-08-04 14:56:21,334 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=71.114, H2=1.754, H3=-1.107
2025-08-04 14:56:21,375 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 8380 sites
2025-08-04 14:56:36,230 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=16.887, H2=4.779, H3=2.966
2025-08-04 14:56:36,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 9742 sites
2025-08-04 14:56:53,626 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=47.817, H2=14.025, H3=1.595
2025-08-04 14:56:53,653 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 547 sites
2025-08-04 14:56:54,625 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=2.383, H2=-6.778, H3=2.719
2025-08-04 14:56:54,654 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1774 sites
2025-08-04 14:56:57,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=34.746, H2=3.996, H3=-5.501
2025-08-04 14:56:57,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 9512 sites
2025-08-04 14:57:14,913 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=24.948, H2=14.153, H3=7.197
2025-08-04 14:57:14,946 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3509 sites
2025-08-04 14:57:21,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=2.792, H2=1.260, H3=2.334
2025-08-04 14:57:21,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2568 sites
2025-08-04 14:57:25,831 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=8.641, H2=11.199, H3=0.110
2025-08-04 14:57:25,856 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-08-04 14:57:25,856 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-08-04 14:57:25,858 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-08-04 14:57:25,893 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5548 sites
2025-08-04 14:57:35,863 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=59.158, H2=-3.187, H3=0.452
2025-08-04 14:57:35,898 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4599 sites
2025-08-04 14:57:44,102 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=67.453, H2=-0.854, H3=-0.466
2025-08-04 14:57:44,140 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6180 sites
2025-08-04 14:57:55,257 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.701, H2=0.501, H3=-1.532
2025-08-04 14:57:55,299 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8380 sites
2025-08-04 14:58:10,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.264, H2=5.100, H3=1.746
2025-08-04 14:58:10,182 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9742 sites
2025-08-04 14:58:27,651 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=49.871, H2=12.170, H3=1.201
2025-08-04 14:58:27,678 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 547 sites
2025-08-04 14:58:28,650 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=1.938, H2=-7.570, H3=2.711
2025-08-04 14:58:28,678 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1774 sites
2025-08-04 14:58:31,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=41.052, H2=4.873, H3=-5.188
2025-08-04 14:58:31,864 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 9512 sites
2025-08-04 14:58:48,824 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=28.345, H2=13.016, H3=7.596
2025-08-04 14:58:48,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3509 sites
2025-08-04 14:58:55,083 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=2.713, H2=1.315, H3=2.314
2025-08-04 14:58:55,114 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2568 sites
2025-08-04 14:58:59,698 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=8.960, H2=6.969, H3=-0.052
2025-08-04 14:58:59,723 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-08-04 14:58:59,724 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-08-04 14:58:59,725 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-08-04 14:58:59,760 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5548 sites
2025-08-04 14:59:09,682 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=59.158, H2=-3.187, H3=0.452
2025-08-04 14:59:09,716 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4599 sites
2025-08-04 14:59:17,889 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=67.453, H2=-0.854, H3=-0.466
2025-08-04 14:59:17,927 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6180 sites
2025-08-04 14:59:28,969 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.701, H2=0.501, H3=-1.532
2025-08-04 14:59:29,010 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8380 sites
2025-08-04 14:59:43,862 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.264, H2=5.100, H3=1.746
2025-08-04 14:59:43,905 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9742 sites
2025-08-04 15:00:01,291 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=49.871, H2=12.170, H3=1.201
2025-08-04 15:00:01,318 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 547 sites
2025-08-04 15:00:02,287 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=1.938, H2=-7.570, H3=2.711
2025-08-04 15:00:02,317 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1774 sites
2025-08-04 15:00:05,449 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=41.052, H2=4.873, H3=-5.188
2025-08-04 15:00:05,478 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2284 sites
2025-08-04 15:00:09,539 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-0.352, H2=-1.351, H3=-4.473
2025-08-04 15:00:09,578 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 7228 sites
2025-08-04 15:00:22,458 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=28.391, H2=14.833, H3=10.646
2025-08-04 15:00:22,491 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3509 sites
2025-08-04 15:00:28,773 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.969, H2=1.626, H3=1.992
2025-08-04 15:00:28,804 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2568 sites
2025-08-04 15:00:33,355 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=9.397, H2=6.695, H3=0.398
2025-08-04 15:00:33,380 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-08-04 15:00:33,380 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-08-04 15:00:33,382 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-08-04 15:00:33,417 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5548 sites
2025-08-04 15:00:43,273 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=59.158, H2=-3.187, H3=0.452
2025-08-04 15:00:43,307 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4599 sites
2025-08-04 15:00:51,490 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=67.453, H2=-0.854, H3=-0.466
2025-08-04 15:00:51,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6180 sites
2025-08-04 15:01:02,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.701, H2=0.501, H3=-1.532
2025-08-04 15:01:02,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8380 sites
2025-08-04 15:01:17,319 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.264, H2=5.100, H3=1.746
2025-08-04 15:01:17,363 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9742 sites
2025-08-04 15:01:34,611 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=49.871, H2=12.170, H3=1.201
2025-08-04 15:01:34,638 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 547 sites
2025-08-04 15:01:35,601 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=1.938, H2=-7.570, H3=2.711
2025-08-04 15:01:35,631 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1774 sites
2025-08-04 15:01:38,802 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=41.052, H2=4.873, H3=-5.188
2025-08-04 15:01:38,832 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2284 sites
2025-08-04 15:01:42,923 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=-0.352, H2=-1.351, H3=-4.473
2025-08-04 15:01:42,953 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2306 sites
2025-08-04 15:01:47,079 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.658, H2=-1.233, H3=3.308
2025-08-04 15:01:47,113 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4922 sites
2025-08-04 15:01:55,899 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=41.786, H2=13.049, H3=7.275
2025-08-04 15:01:55,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3509 sites
2025-08-04 15:02:02,207 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.356, H2=1.373, H3=3.324
2025-08-04 15:02:02,248 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2568 sites
2025-08-04 15:02:06,840 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=10.777, H2=7.673, H3=0.382
2025-08-04 15:02:06,865 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-08-04 15:02:06,865 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-08-04 15:02:06,867 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-08-04 15:02:06,903 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 5548 sites
2025-08-04 15:02:16,937 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=59.158, H2=-3.187, H3=0.452
2025-08-04 15:02:16,971 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 4599 sites
2025-08-04 15:02:25,246 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=67.453, H2=-0.854, H3=-0.466
2025-08-04 15:02:25,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6180 sites
2025-08-04 15:02:36,305 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=28.701, H2=0.501, H3=-1.532
2025-08-04 15:02:36,346 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 8380 sites
2025-08-04 15:02:51,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.264, H2=5.100, H3=1.746
2025-08-04 15:02:51,355 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1708 sites
2025-08-04 15:02:54,400 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=5.359, H2=-7.867, H3=-10.362
2025-08-04 15:02:54,441 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8034 sites
2025-08-04 15:03:08,797 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=38.550, H2=13.831, H3=5.110
2025-08-04 15:03:08,825 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 547 sites
2025-08-04 15:03:09,788 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=2.627, H2=-5.767, H3=3.498
2025-08-04 15:03:09,817 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1774 sites
2025-08-04 15:03:12,951 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.997, H2=3.583, H3=-6.003
2025-08-04 15:03:12,982 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2284 sites
2025-08-04 15:03:17,043 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-0.371, H2=-2.478, H3=-3.295
2025-08-04 15:03:17,073 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2306 sites
2025-08-04 15:03:21,181 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.240, H2=-1.009, H3=2.968
2025-08-04 15:03:21,217 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4922 sites
2025-08-04 15:03:29,981 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=27.505, H2=18.478, H3=10.179
2025-08-04 15:03:30,013 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3509 sites
2025-08-04 15:03:36,300 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.652, H2=1.677, H3=2.932
2025-08-04 15:03:36,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2568 sites
2025-08-04 15:03:40,903 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.997, H2=6.569, H3=-0.214
2025-08-04 15:03:40,929 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-08-04 15:03:40,929 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-08-04 15:03:40,931 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-08-04 15:03:40,961 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:03:44,693 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:03:44,725 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:03:50,827 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:03:50,861 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4599 sites
2025-08-04 15:03:59,094 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=103.693, H2=-0.957, H3=-0.296
2025-08-04 15:03:59,130 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6180 sites
2025-08-04 15:04:10,058 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=27.239, H2=0.511, H3=-1.775
2025-08-04 15:04:10,100 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8380 sites
2025-08-04 15:04:25,044 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=18.723, H2=3.932, H3=1.343
2025-08-04 15:04:25,073 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1708 sites
2025-08-04 15:04:28,104 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=4.690, H2=-6.379, H3=-10.470
2025-08-04 15:04:28,144 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 8034 sites
2025-08-04 15:04:42,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=25.348, H2=14.624, H3=4.929
2025-08-04 15:04:42,488 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 547 sites
2025-08-04 15:04:43,458 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=1.935, H2=-7.953, H3=2.514
2025-08-04 15:04:43,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1774 sites
2025-08-04 15:04:46,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=46.736, H2=3.873, H3=-5.411
2025-08-04 15:04:46,676 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2284 sites
2025-08-04 15:04:50,723 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-0.664, H2=-1.697, H3=-3.350
2025-08-04 15:04:50,754 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2306 sites
2025-08-04 15:04:54,829 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.971, H2=-0.724, H3=3.590
2025-08-04 15:04:54,863 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 4922 sites
2025-08-04 15:05:03,636 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=43.518, H2=20.140, H3=10.383
2025-08-04 15:05:03,668 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3509 sites
2025-08-04 15:05:09,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=2.804, H2=1.302, H3=1.880
2025-08-04 15:05:09,885 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2568 sites
2025-08-04 15:05:14,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=6.794, H2=7.192, H3=0.402
2025-08-04 15:05:14,493 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-08-04 15:05:14,493 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-08-04 15:05:14,494 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-08-04 15:05:14,524 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:05:18,283 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:05:18,315 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:05:24,429 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:05:24,463 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4599 sites
2025-08-04 15:05:32,640 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=103.693, H2=-0.957, H3=-0.296
2025-08-04 15:05:32,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3107 sites
2025-08-04 15:05:38,163 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.331, H2=-0.619, H3=-4.138
2025-08-04 15:05:38,195 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3073 sites
2025-08-04 15:05:43,659 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.773, H2=0.431, H3=1.047
2025-08-04 15:05:43,700 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 8380 sites
2025-08-04 15:05:58,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.879, H2=3.566, H3=1.410
2025-08-04 15:05:58,583 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1708 sites
2025-08-04 15:06:01,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=6.779, H2=-5.074, H3=-11.429
2025-08-04 15:06:01,657 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8034 sites
2025-08-04 15:06:15,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=37.353, H2=14.513, H3=5.036
2025-08-04 15:06:15,847 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 547 sites
2025-08-04 15:06:16,816 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=2.718, H2=-7.633, H3=2.715
2025-08-04 15:06:16,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1774 sites
2025-08-04 15:06:20,010 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=31.786, H2=3.977, H3=-4.735
2025-08-04 15:06:20,039 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2284 sites
2025-08-04 15:06:24,100 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-0.277, H2=-1.502, H3=-3.020
2025-08-04 15:06:24,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2306 sites
2025-08-04 15:06:28,267 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.927, H2=-0.797, H3=4.709
2025-08-04 15:06:28,312 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 4922 sites
2025-08-04 15:06:37,052 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=29.481, H2=13.925, H3=8.563
2025-08-04 15:06:37,083 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3509 sites
2025-08-04 15:06:43,353 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.687, H2=1.005, H3=2.708
2025-08-04 15:06:43,383 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2568 sites
2025-08-04 15:06:47,949 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=12.558, H2=8.151, H3=0.159
2025-08-04 15:06:47,974 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-08-04 15:06:47,974 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-08-04 15:06:47,976 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-08-04 15:06:48,005 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:06:51,745 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:06:51,777 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:06:57,887 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:06:57,921 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4599 sites
2025-08-04 15:07:06,075 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=103.693, H2=-0.957, H3=-0.296
2025-08-04 15:07:06,107 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3107 sites
2025-08-04 15:07:11,640 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.331, H2=-0.619, H3=-4.138
2025-08-04 15:07:11,672 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3073 sites
2025-08-04 15:07:17,123 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.773, H2=0.431, H3=1.047
2025-08-04 15:07:17,157 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3893 sites
2025-08-04 15:07:24,056 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.895, H2=3.744, H3=1.870
2025-08-04 15:07:24,090 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4487 sites
2025-08-04 15:07:32,117 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=7.133, H2=0.467, H3=0.151
2025-08-04 15:07:32,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1708 sites
2025-08-04 15:07:35,163 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.076, H2=-4.911, H3=-10.397
2025-08-04 15:07:35,203 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8034 sites
2025-08-04 15:07:49,481 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=30.873, H2=17.660, H3=5.300
2025-08-04 15:07:49,508 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 547 sites
2025-08-04 15:07:50,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.444, H2=-8.772, H3=1.985
2025-08-04 15:07:50,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1774 sites
2025-08-04 15:07:53,690 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=35.641, H2=4.167, H3=-7.554
2025-08-04 15:07:53,721 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2284 sites
2025-08-04 15:07:57,812 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.146, H2=-1.764, H3=-5.466
2025-08-04 15:07:57,843 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2306 sites
2025-08-04 15:08:01,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=3.560, H2=-1.426, H3=2.749
2025-08-04 15:08:01,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 4922 sites
2025-08-04 15:08:10,788 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=37.953, H2=15.554, H3=9.897
2025-08-04 15:08:10,820 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3509 sites
2025-08-04 15:08:17,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=4.334, H2=1.221, H3=2.171
2025-08-04 15:08:17,163 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2568 sites
2025-08-04 15:08:21,722 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=7.519, H2=7.915, H3=-0.136
2025-08-04 15:08:21,747 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-08-04 15:08:21,747 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-08-04 15:08:21,749 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-08-04 15:08:21,791 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:08:25,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:08:25,576 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:08:31,748 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:08:31,782 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4599 sites
2025-08-04 15:08:39,991 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=103.693, H2=-0.957, H3=-0.296
2025-08-04 15:08:40,023 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3107 sites
2025-08-04 15:08:45,558 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.331, H2=-0.619, H3=-4.138
2025-08-04 15:08:45,590 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3073 sites
2025-08-04 15:08:51,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.773, H2=0.431, H3=1.047
2025-08-04 15:08:51,117 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3893 sites
2025-08-04 15:08:58,009 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.895, H2=3.744, H3=1.870
2025-08-04 15:08:58,042 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4487 sites
2025-08-04 15:09:06,010 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=7.133, H2=0.467, H3=0.151
2025-08-04 15:09:06,039 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1708 sites
2025-08-04 15:09:09,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.076, H2=-4.911, H3=-10.397
2025-08-04 15:09:09,111 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8034 sites
2025-08-04 15:09:23,435 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=30.873, H2=17.660, H3=5.300
2025-08-04 15:09:23,461 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 547 sites
2025-08-04 15:09:24,432 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.444, H2=-8.772, H3=1.985
2025-08-04 15:09:24,458 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 393 sites
2025-08-04 15:09:25,157 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-4.108, H2=-0.446, H3=-1.604
2025-08-04 15:09:25,185 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1381 sites
2025-08-04 15:09:27,666 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=60.349, H2=-0.978, H3=-6.160
2025-08-04 15:09:27,696 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2284 sites
2025-08-04 15:09:31,785 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-0.835, H2=-2.057, H3=-3.465
2025-08-04 15:09:31,815 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2306 sites
2025-08-04 15:09:35,911 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.874, H2=-1.341, H3=2.780
2025-08-04 15:09:35,946 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4922 sites
2025-08-04 15:09:44,697 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=34.863, H2=15.586, H3=8.178
2025-08-04 15:09:44,730 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3509 sites
2025-08-04 15:09:50,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.734, H2=1.519, H3=2.181
2025-08-04 15:09:50,988 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2568 sites
2025-08-04 15:09:55,543 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=10.669, H2=6.274, H3=0.132
2025-08-04 15:09:55,568 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-08-04 15:09:55,568 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-08-04 15:09:55,570 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-08-04 15:09:55,599 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:09:59,369 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:09:59,402 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:10:05,508 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:10:05,543 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4599 sites
2025-08-04 15:10:13,763 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=103.693, H2=-0.957, H3=-0.296
2025-08-04 15:10:13,794 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3107 sites
2025-08-04 15:10:19,294 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.331, H2=-0.619, H3=-4.138
2025-08-04 15:10:19,325 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3073 sites
2025-08-04 15:10:24,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.773, H2=0.431, H3=1.047
2025-08-04 15:10:24,826 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3893 sites
2025-08-04 15:10:31,739 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.895, H2=3.744, H3=1.870
2025-08-04 15:10:31,774 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4487 sites
2025-08-04 15:10:39,806 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=7.133, H2=0.467, H3=0.151
2025-08-04 15:10:39,835 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1708 sites
2025-08-04 15:10:42,851 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.076, H2=-4.911, H3=-10.397
2025-08-04 15:10:42,892 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 8034 sites
2025-08-04 15:10:57,175 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=30.873, H2=17.660, H3=5.300
2025-08-04 15:10:57,203 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 547 sites
2025-08-04 15:10:58,171 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.444, H2=-8.772, H3=1.985
2025-08-04 15:10:58,197 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 393 sites
2025-08-04 15:10:58,890 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-4.108, H2=-0.446, H3=-1.604
2025-08-04 15:10:58,919 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1381 sites
2025-08-04 15:11:01,356 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=60.349, H2=-0.978, H3=-6.160
2025-08-04 15:11:01,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2284 sites
2025-08-04 15:11:05,426 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-0.835, H2=-2.057, H3=-3.465
2025-08-04 15:11:05,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2306 sites
2025-08-04 15:11:09,567 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.874, H2=-1.341, H3=2.780
2025-08-04 15:11:09,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4922 sites
2025-08-04 15:11:18,315 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=34.863, H2=15.586, H3=8.178
2025-08-04 15:11:18,347 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3509 sites
2025-08-04 15:11:24,575 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.734, H2=1.519, H3=2.181
2025-08-04 15:11:24,603 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1298 sites
2025-08-04 15:11:26,911 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=5.614, H2=0.108, H3=0.927
2025-08-04 15:11:26,940 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1270 sites
2025-08-04 15:11:29,174 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-1.205, H2=3.002, H3=-2.308
2025-08-04 15:11:29,200 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-08-04 15:11:29,200 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-08-04 15:11:29,202 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-08-04 15:11:29,232 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:11:32,966 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:11:32,999 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:11:39,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:11:39,105 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4599 sites
2025-08-04 15:11:47,267 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=103.693, H2=-0.957, H3=-0.296
2025-08-04 15:11:47,299 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3107 sites
2025-08-04 15:11:52,787 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.331, H2=-0.619, H3=-4.138
2025-08-04 15:11:52,818 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3073 sites
2025-08-04 15:11:58,274 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.773, H2=0.431, H3=1.047
2025-08-04 15:11:58,313 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3893 sites
2025-08-04 15:12:05,201 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.895, H2=3.744, H3=1.870
2025-08-04 15:12:05,235 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4487 sites
2025-08-04 15:12:13,202 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=7.133, H2=0.467, H3=0.151
2025-08-04 15:12:13,232 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1708 sites
2025-08-04 15:12:16,236 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.076, H2=-4.911, H3=-10.397
2025-08-04 15:12:16,266 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2246 sites
2025-08-04 15:12:20,256 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-0.830, H2=6.519, H3=4.432
2025-08-04 15:12:20,293 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 5788 sites
2025-08-04 15:12:30,471 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=54.379, H2=16.250, H3=1.703
2025-08-04 15:12:30,498 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 547 sites
2025-08-04 15:12:31,460 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.971, H2=-4.949, H3=2.700
2025-08-04 15:12:31,486 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 393 sites
2025-08-04 15:12:32,181 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-5.660, H2=-0.623, H3=-2.093
2025-08-04 15:12:32,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1381 sites
2025-08-04 15:12:34,649 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=32.704, H2=-1.371, H3=-6.420
2025-08-04 15:12:34,679 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2284 sites
2025-08-04 15:12:38,703 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.697, H2=-2.188, H3=-2.674
2025-08-04 15:12:38,732 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2306 sites
2025-08-04 15:12:42,768 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.896, H2=-0.946, H3=3.143
2025-08-04 15:12:42,802 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4922 sites
2025-08-04 15:12:51,482 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=29.472, H2=16.075, H3=8.888
2025-08-04 15:12:51,515 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3509 sites
2025-08-04 15:12:57,678 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.341, H2=1.012, H3=2.350
2025-08-04 15:12:57,707 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1298 sites
2025-08-04 15:13:00,021 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=4.905, H2=0.004, H3=1.035
2025-08-04 15:13:00,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1270 sites
2025-08-04 15:13:02,304 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-1.459, H2=3.442, H3=-3.095
2025-08-04 15:13:02,329 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-08-04 15:13:02,329 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-08-04 15:13:02,331 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-08-04 15:13:02,361 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:13:06,128 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:13:06,161 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:13:12,274 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:13:12,308 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 4599 sites
2025-08-04 15:13:20,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=103.693, H2=-0.957, H3=-0.296
2025-08-04 15:13:20,522 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 3107 sites
2025-08-04 15:13:26,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=17.331, H2=-0.619, H3=-4.138
2025-08-04 15:13:26,048 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3073 sites
2025-08-04 15:13:31,480 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.773, H2=0.431, H3=1.047
2025-08-04 15:13:31,513 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3893 sites
2025-08-04 15:13:38,455 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=8.895, H2=3.744, H3=1.870
2025-08-04 15:13:38,490 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4487 sites
2025-08-04 15:13:46,468 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=7.133, H2=0.467, H3=0.151
2025-08-04 15:13:46,511 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1708 sites
2025-08-04 15:13:49,544 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.076, H2=-4.911, H3=-10.397
2025-08-04 15:13:49,574 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2246 sites
2025-08-04 15:13:53,566 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=-0.830, H2=6.519, H3=4.432
2025-08-04 15:13:53,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 5788 sites
2025-08-04 15:14:03,844 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=54.379, H2=16.250, H3=1.703
2025-08-04 15:14:03,871 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 547 sites
2025-08-04 15:14:04,834 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=1.971, H2=-4.949, H3=2.700
2025-08-04 15:14:04,860 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 393 sites
2025-08-04 15:14:05,554 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-5.660, H2=-0.623, H3=-2.093
2025-08-04 15:14:05,583 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1381 sites
2025-08-04 15:14:08,016 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=32.704, H2=-1.371, H3=-6.420
2025-08-04 15:14:08,047 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2284 sites
2025-08-04 15:14:12,071 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-0.697, H2=-2.188, H3=-2.674
2025-08-04 15:14:12,101 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2306 sites
2025-08-04 15:14:16,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.896, H2=-0.946, H3=3.143
2025-08-04 15:14:16,214 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4922 sites
2025-08-04 15:14:24,942 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=29.472, H2=16.075, H3=8.888
2025-08-04 15:14:24,969 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 999 sites
2025-08-04 15:14:26,732 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=1.671, H2=1.610, H3=4.080
2025-08-04 15:14:26,763 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2510 sites
2025-08-04 15:14:31,228 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=0.495, H2=0.620, H3=0.508
2025-08-04 15:14:31,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1298 sites
2025-08-04 15:14:33,567 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=7.092, H2=0.265, H3=0.616
2025-08-04 15:14:33,596 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1270 sites
2025-08-04 15:14:35,838 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-1.040, H2=3.701, H3=-3.273
2025-08-04 15:14:35,863 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-08-04 15:14:35,864 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-08-04 15:14:35,865 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-08-04 15:14:35,895 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:14:39,614 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:14:39,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:14:45,768 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:14:45,799 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2634 sites
2025-08-04 15:14:50,445 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.160, H2=-1.192, H3=-1.464
2025-08-04 15:14:50,474 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1965 sites
2025-08-04 15:14:53,954 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=23.840, H2=-0.896, H3=0.133
2025-08-04 15:14:53,986 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3107 sites
2025-08-04 15:14:59,467 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=13.403, H2=-1.020, H3=-4.705
2025-08-04 15:14:59,499 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3073 sites
2025-08-04 15:15:04,949 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=22.579, H2=1.439, H3=1.804
2025-08-04 15:15:04,983 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3893 sites
2025-08-04 15:15:11,889 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.186, H2=4.575, H3=2.380
2025-08-04 15:15:11,923 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4487 sites
2025-08-04 15:15:19,902 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.738, H2=0.151, H3=-0.048
2025-08-04 15:15:19,931 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1708 sites
2025-08-04 15:15:22,965 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.508, H2=-7.576, H3=-9.789
2025-08-04 15:15:22,996 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2246 sites
2025-08-04 15:15:27,002 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-0.860, H2=3.204, H3=3.446
2025-08-04 15:15:27,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 5788 sites
2025-08-04 15:15:37,359 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=38.392, H2=24.417, H3=2.385
2025-08-04 15:15:37,386 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 547 sites
2025-08-04 15:15:38,359 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.053, H2=-7.541, H3=2.582
2025-08-04 15:15:38,385 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 393 sites
2025-08-04 15:15:39,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-3.779, H2=-0.528, H3=-2.049
2025-08-04 15:15:39,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1381 sites
2025-08-04 15:15:41,580 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=35.565, H2=-0.958, H3=-4.669
2025-08-04 15:15:41,611 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2284 sites
2025-08-04 15:15:45,678 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-0.684, H2=-1.858, H3=-3.551
2025-08-04 15:15:45,707 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2306 sites
2025-08-04 15:15:49,810 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.610, H2=-1.018, H3=3.648
2025-08-04 15:15:49,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4922 sites
2025-08-04 15:15:58,588 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=32.204, H2=12.972, H3=10.499
2025-08-04 15:15:58,616 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 999 sites
2025-08-04 15:16:00,380 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=1.422, H2=1.524, H3=5.289
2025-08-04 15:16:00,411 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2510 sites
2025-08-04 15:16:04,855 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=0.694, H2=0.281, H3=-0.435
2025-08-04 15:16:04,884 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1298 sites
2025-08-04 15:16:07,190 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=4.380, H2=0.262, H3=0.911
2025-08-04 15:16:07,218 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1270 sites
2025-08-04 15:16:09,471 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-1.086, H2=3.297, H3=-2.686
2025-08-04 15:16:09,497 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-08-04 15:16:09,497 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-08-04 15:16:09,499 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-08-04 15:16:09,529 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:16:13,276 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:16:13,308 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:16:19,423 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:16:19,453 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2634 sites
2025-08-04 15:16:24,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.160, H2=-1.192, H3=-1.464
2025-08-04 15:16:24,137 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1965 sites
2025-08-04 15:16:27,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=23.840, H2=-0.896, H3=0.133
2025-08-04 15:16:27,661 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3107 sites
2025-08-04 15:16:33,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=13.403, H2=-1.020, H3=-4.705
2025-08-04 15:16:33,211 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3073 sites
2025-08-04 15:16:38,648 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=22.579, H2=1.439, H3=1.804
2025-08-04 15:16:38,681 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3893 sites
2025-08-04 15:16:45,576 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.186, H2=4.575, H3=2.380
2025-08-04 15:16:45,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4487 sites
2025-08-04 15:16:53,587 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.738, H2=0.151, H3=-0.048
2025-08-04 15:16:53,616 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1708 sites
2025-08-04 15:16:56,640 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=6.508, H2=-7.576, H3=-9.789
2025-08-04 15:16:56,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2246 sites
2025-08-04 15:17:00,652 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-0.860, H2=3.204, H3=3.446
2025-08-04 15:17:00,688 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 5788 sites
2025-08-04 15:17:10,970 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=38.392, H2=24.417, H3=2.385
2025-08-04 15:17:10,997 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 547 sites
2025-08-04 15:17:11,959 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=3.053, H2=-7.541, H3=2.582
2025-08-04 15:17:11,985 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 393 sites
2025-08-04 15:17:12,681 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-3.779, H2=-0.528, H3=-2.049
2025-08-04 15:17:12,710 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1381 sites
2025-08-04 15:17:15,138 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=35.565, H2=-0.958, H3=-4.669
2025-08-04 15:17:15,165 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 538 sites
2025-08-04 15:17:16,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-2.105, H2=-1.055, H3=-3.083
2025-08-04 15:17:16,145 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1746 sites
2025-08-04 15:17:19,227 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-1.610, H2=-2.379, H3=-2.492
2025-08-04 15:17:19,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 2306 sites
2025-08-04 15:17:23,337 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.893, H2=-0.787, H3=3.502
2025-08-04 15:17:23,371 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 4922 sites
2025-08-04 15:17:32,137 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=37.511, H2=15.577, H3=9.122
2025-08-04 15:17:32,165 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 999 sites
2025-08-04 15:17:33,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=2.646, H2=1.992, H3=5.354
2025-08-04 15:17:33,978 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2510 sites
2025-08-04 15:17:38,471 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.003, H2=0.419, H3=0.038
2025-08-04 15:17:38,499 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1298 sites
2025-08-04 15:17:40,798 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=8.433, H2=0.015, H3=0.829
2025-08-04 15:17:40,826 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1270 sites
2025-08-04 15:17:43,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-1.054, H2=3.757, H3=-2.737
2025-08-04 15:17:43,111 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-08-04 15:17:43,111 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-08-04 15:17:43,113 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-08-04 15:17:43,142 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:17:46,933 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:17:46,966 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3442 sites
2025-08-04 15:17:53,171 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=28.039, H2=-1.533, H3=0.542
2025-08-04 15:17:53,202 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 2634 sites
2025-08-04 15:17:57,910 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=35.160, H2=-1.192, H3=-1.464
2025-08-04 15:17:57,939 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1965 sites
2025-08-04 15:18:01,437 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=23.840, H2=-0.896, H3=0.133
2025-08-04 15:18:01,468 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3107 sites
2025-08-04 15:18:07,032 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=13.403, H2=-1.020, H3=-4.705
2025-08-04 15:18:07,082 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3073 sites
2025-08-04 15:18:12,586 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=22.579, H2=1.439, H3=1.804
2025-08-04 15:18:12,618 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3893 sites
2025-08-04 15:18:19,517 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=8.186, H2=4.575, H3=2.380
2025-08-04 15:18:19,547 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2179 sites
2025-08-04 15:18:23,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=5.984, H2=0.376, H3=0.378
2025-08-04 15:18:23,441 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2308 sites
2025-08-04 15:18:27,522 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=7.470, H2=-0.538, H3=-0.668
2025-08-04 15:18:27,550 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1708 sites
2025-08-04 15:18:30,586 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=7.408, H2=-6.421, H3=-9.459
2025-08-04 15:18:30,615 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2246 sites
2025-08-04 15:18:34,569 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-0.745, H2=3.777, H3=4.672
2025-08-04 15:18:34,606 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 5788 sites
2025-08-04 15:18:44,937 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=62.742, H2=20.248, H3=2.525
2025-08-04 15:18:44,963 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 547 sites
2025-08-04 15:18:45,923 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.511, H2=-8.194, H3=2.968
2025-08-04 15:18:45,950 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 393 sites
2025-08-04 15:18:46,647 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-3.988, H2=-1.243, H3=-2.913
2025-08-04 15:18:46,675 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1381 sites
2025-08-04 15:18:49,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=43.257, H2=-0.972, H3=-6.873
2025-08-04 15:18:49,143 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 538 sites
2025-08-04 15:18:50,092 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-2.445, H2=-1.455, H3=-3.045
2025-08-04 15:18:50,121 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1746 sites
2025-08-04 15:18:53,219 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-0.916, H2=-1.740, H3=-2.134
2025-08-04 15:18:53,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 2306 sites
2025-08-04 15:18:57,374 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.253, H2=-0.596, H3=3.885
2025-08-04 15:18:57,408 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 4922 sites
2025-08-04 15:19:06,088 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=33.490, H2=15.059, H3=11.419
2025-08-04 15:19:06,116 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 999 sites
2025-08-04 15:19:07,891 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=1.179, H2=2.015, H3=3.411
2025-08-04 15:19:07,922 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2510 sites
2025-08-04 15:19:12,380 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=1.152, H2=0.505, H3=-0.029
2025-08-04 15:19:12,408 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1298 sites
2025-08-04 15:19:14,705 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=6.676, H2=0.591, H3=0.734
2025-08-04 15:19:14,734 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1270 sites
2025-08-04 15:19:16,980 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-1.017, H2=3.842, H3=-2.189
2025-08-04 15:19:17,005 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-08-04 15:19:17,006 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-08-04 15:19:17,007 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-08-04 15:19:17,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:19:20,778 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:19:20,808 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2296 sites
2025-08-04 15:19:24,871 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=19.625, H2=-1.487, H3=0.653
2025-08-04 15:19:24,899 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1146 sites
2025-08-04 15:19:26,958 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=22.993, H2=-0.546, H3=0.454
2025-08-04 15:19:26,988 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2634 sites
2025-08-04 15:19:31,686 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.193, H2=-1.042, H3=-1.877
2025-08-04 15:19:31,716 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1965 sites
2025-08-04 15:19:35,187 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.612, H2=-1.872, H3=0.252
2025-08-04 15:19:35,219 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3107 sites
2025-08-04 15:19:40,750 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.876, H2=-0.262, H3=-3.658
2025-08-04 15:19:40,782 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3073 sites
2025-08-04 15:19:46,233 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=23.564, H2=1.113, H3=1.619
2025-08-04 15:19:46,266 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3893 sites
2025-08-04 15:19:53,196 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.942, H2=3.461, H3=1.765
2025-08-04 15:19:53,226 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2179 sites
2025-08-04 15:19:57,085 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=8.812, H2=0.165, H3=0.510
2025-08-04 15:19:57,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2308 sites
2025-08-04 15:20:01,218 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.200, H2=-0.366, H3=-0.870
2025-08-04 15:20:01,247 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1708 sites
2025-08-04 15:20:04,260 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.623, H2=-5.242, H3=-10.021
2025-08-04 15:20:04,290 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2246 sites
2025-08-04 15:20:08,272 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.608, H2=4.405, H3=5.703
2025-08-04 15:20:08,309 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 5788 sites
2025-08-04 15:20:18,506 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=42.819, H2=18.859, H3=1.783
2025-08-04 15:20:18,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 547 sites
2025-08-04 15:20:19,503 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=2.064, H2=-8.020, H3=2.507
2025-08-04 15:20:19,529 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 393 sites
2025-08-04 15:20:20,228 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-3.905, H2=-0.383, H3=-2.456
2025-08-04 15:20:20,256 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1381 sites
2025-08-04 15:20:22,711 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=46.588, H2=-0.607, H3=-6.012
2025-08-04 15:20:22,738 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 538 sites
2025-08-04 15:20:23,690 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-2.010, H2=-0.871, H3=-2.870
2025-08-04 15:20:23,719 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1746 sites
2025-08-04 15:20:26,825 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-1.358, H2=-1.791, H3=-2.076
2025-08-04 15:20:26,855 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 2306 sites
2025-08-04 15:20:30,936 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=2.725, H2=-1.177, H3=3.858
2025-08-04 15:20:30,970 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 4922 sites
2025-08-04 15:20:39,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=30.484, H2=15.686, H3=11.035
2025-08-04 15:20:39,718 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 999 sites
2025-08-04 15:20:41,477 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=2.222, H2=2.275, H3=4.350
2025-08-04 15:20:41,507 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2510 sites
2025-08-04 15:20:45,969 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.201, H2=1.068, H3=-0.079
2025-08-04 15:20:45,997 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1298 sites
2025-08-04 15:20:48,293 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=5.184, H2=0.617, H3=0.701
2025-08-04 15:20:48,321 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1270 sites
2025-08-04 15:20:50,582 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-1.031, H2=3.413, H3=-2.249
2025-08-04 15:20:50,608 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-08-04 15:20:50,608 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-08-04 15:20:50,610 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-08-04 15:20:50,647 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:20:54,383 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:20:54,414 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2296 sites
2025-08-04 15:20:58,462 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=19.625, H2=-1.487, H3=0.653
2025-08-04 15:20:58,491 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1146 sites
2025-08-04 15:21:00,520 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=22.993, H2=-0.546, H3=0.454
2025-08-04 15:21:00,552 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2634 sites
2025-08-04 15:21:05,230 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.193, H2=-1.042, H3=-1.877
2025-08-04 15:21:05,260 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1965 sites
2025-08-04 15:21:08,788 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.612, H2=-1.872, H3=0.252
2025-08-04 15:21:08,820 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3107 sites
2025-08-04 15:21:14,386 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.876, H2=-0.262, H3=-3.658
2025-08-04 15:21:14,418 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3073 sites
2025-08-04 15:21:19,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=23.564, H2=1.113, H3=1.619
2025-08-04 15:21:19,955 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3893 sites
2025-08-04 15:21:26,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.942, H2=3.461, H3=1.765
2025-08-04 15:21:26,977 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2179 sites
2025-08-04 15:21:30,887 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=8.812, H2=0.165, H3=0.510
2025-08-04 15:21:30,917 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2308 sites
2025-08-04 15:21:35,039 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.200, H2=-0.366, H3=-0.870
2025-08-04 15:21:35,068 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1708 sites
2025-08-04 15:21:38,112 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.623, H2=-5.242, H3=-10.021
2025-08-04 15:21:38,143 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2246 sites
2025-08-04 15:21:42,150 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.608, H2=4.405, H3=5.703
2025-08-04 15:21:42,182 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2616 sites
2025-08-04 15:21:46,834 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=10.036, H2=-0.605, H3=2.973
2025-08-04 15:21:46,866 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3172 sites
2025-08-04 15:21:52,504 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=32.372, H2=13.121, H3=-1.392
2025-08-04 15:21:52,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 547 sites
2025-08-04 15:21:53,497 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.153, H2=-5.999, H3=2.702
2025-08-04 15:21:53,523 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 393 sites
2025-08-04 15:21:54,223 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-4.756, H2=-1.038, H3=-1.980
2025-08-04 15:21:54,252 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1381 sites
2025-08-04 15:21:56,692 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=39.024, H2=-0.826, H3=-6.170
2025-08-04 15:21:56,718 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 538 sites
2025-08-04 15:21:57,669 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-1.769, H2=-1.442, H3=-3.263
2025-08-04 15:21:57,698 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1746 sites
2025-08-04 15:22:00,793 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-1.149, H2=-2.160, H3=-2.732
2025-08-04 15:22:00,824 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2306 sites
2025-08-04 15:22:04,895 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=2.848, H2=-0.969, H3=3.704
2025-08-04 15:22:04,929 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 4922 sites
2025-08-04 15:22:13,655 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=38.290, H2=18.866, H3=11.515
2025-08-04 15:22:13,682 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 999 sites
2025-08-04 15:22:15,448 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.545, H2=2.386, H3=3.640
2025-08-04 15:22:15,478 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2510 sites
2025-08-04 15:22:19,914 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=0.925, H2=0.689, H3=0.269
2025-08-04 15:22:19,955 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1298 sites
2025-08-04 15:22:22,249 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=5.627, H2=0.546, H3=1.008
2025-08-04 15:22:22,277 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1270 sites
2025-08-04 15:22:24,510 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-1.923, H2=3.278, H3=-3.087
2025-08-04 15:22:24,535 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-08-04 15:22:24,535 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-08-04 15:22:24,537 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-08-04 15:22:24,566 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:22:28,312 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:22:28,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2296 sites
2025-08-04 15:22:32,421 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=19.625, H2=-1.487, H3=0.653
2025-08-04 15:22:32,449 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1146 sites
2025-08-04 15:22:34,461 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=22.993, H2=-0.546, H3=0.454
2025-08-04 15:22:34,491 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2634 sites
2025-08-04 15:22:39,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.193, H2=-1.042, H3=-1.877
2025-08-04 15:22:39,162 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1965 sites
2025-08-04 15:22:42,637 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.612, H2=-1.872, H3=0.252
2025-08-04 15:22:42,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3107 sites
2025-08-04 15:22:48,111 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.876, H2=-0.262, H3=-3.658
2025-08-04 15:22:48,143 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3073 sites
2025-08-04 15:22:53,612 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=23.564, H2=1.113, H3=1.619
2025-08-04 15:22:53,645 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3893 sites
2025-08-04 15:23:00,502 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.942, H2=3.461, H3=1.765
2025-08-04 15:23:00,532 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2179 sites
2025-08-04 15:23:04,370 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=8.812, H2=0.165, H3=0.510
2025-08-04 15:23:04,400 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2308 sites
2025-08-04 15:23:08,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=5.200, H2=-0.366, H3=-0.870
2025-08-04 15:23:08,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1708 sites
2025-08-04 15:23:11,521 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.623, H2=-5.242, H3=-10.021
2025-08-04 15:23:11,550 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2246 sites
2025-08-04 15:23:15,518 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-0.608, H2=4.405, H3=5.703
2025-08-04 15:23:15,550 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2616 sites
2025-08-04 15:23:20,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=10.036, H2=-0.605, H3=2.973
2025-08-04 15:23:20,210 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3172 sites
2025-08-04 15:23:25,807 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=32.372, H2=13.121, H3=-1.392
2025-08-04 15:23:25,834 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 547 sites
2025-08-04 15:23:26,802 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=2.153, H2=-5.999, H3=2.702
2025-08-04 15:23:26,828 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 393 sites
2025-08-04 15:23:27,523 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-4.756, H2=-1.038, H3=-1.980
2025-08-04 15:23:27,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1381 sites
2025-08-04 15:23:30,006 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=39.024, H2=-0.826, H3=-6.170
2025-08-04 15:23:30,033 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 538 sites
2025-08-04 15:23:30,990 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-1.769, H2=-1.442, H3=-3.263
2025-08-04 15:23:31,032 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1746 sites
2025-08-04 15:23:34,116 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-1.149, H2=-2.160, H3=-2.732
2025-08-04 15:23:34,146 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 2306 sites
2025-08-04 15:23:38,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=2.848, H2=-0.969, H3=3.704
2025-08-04 15:23:38,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 4922 sites
2025-08-04 15:23:46,995 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=38.290, H2=18.866, H3=11.515
2025-08-04 15:23:47,022 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 999 sites
2025-08-04 15:23:48,787 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=1.545, H2=2.386, H3=3.640
2025-08-04 15:23:48,816 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1322 sites
2025-08-04 15:23:51,146 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=-3.403, H2=-2.452, H3=-1.020
2025-08-04 15:23:51,174 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1188 sites
2025-08-04 15:23:53,278 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=1.981, H2=1.851, H3=1.876
2025-08-04 15:23:53,307 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1298 sites
2025-08-04 15:23:55,605 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=5.840, H2=-0.150, H3=1.052
2025-08-04 15:23:55,633 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1270 sites
2025-08-04 15:23:57,885 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-1.385, H2=3.444, H3=-2.553
2025-08-04 15:23:57,910 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=26 has insufficient data
2025-08-04 15:23:57,910 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=27
2025-08-04 15:23:57,912 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
 -9223372036854775808]
2025-08-04 15:23:57,942 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:24:01,726 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:24:01,757 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2296 sites
2025-08-04 15:24:05,834 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=19.625, H2=-1.487, H3=0.653
2025-08-04 15:24:05,862 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1146 sites
2025-08-04 15:24:07,901 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=22.993, H2=-0.546, H3=0.454
2025-08-04 15:24:07,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2634 sites
2025-08-04 15:24:12,622 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.193, H2=-1.042, H3=-1.877
2025-08-04 15:24:12,651 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1965 sites
2025-08-04 15:24:16,132 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.612, H2=-1.872, H3=0.252
2025-08-04 15:24:16,164 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3107 sites
2025-08-04 15:24:21,721 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.876, H2=-0.262, H3=-3.658
2025-08-04 15:24:21,753 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3073 sites
2025-08-04 15:24:27,190 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=23.564, H2=1.113, H3=1.619
2025-08-04 15:24:27,220 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2879 sites
2025-08-04 15:24:32,366 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.719, H2=2.191, H3=1.408
2025-08-04 15:24:32,394 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1014 sites
2025-08-04 15:24:34,188 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.414, H2=5.522, H3=1.000
2025-08-04 15:24:34,218 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2179 sites
2025-08-04 15:24:38,086 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=7.443, H2=0.268, H3=0.336
2025-08-04 15:24:38,115 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2308 sites
2025-08-04 15:24:42,231 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.454, H2=-0.177, H3=-0.319
2025-08-04 15:24:42,259 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1708 sites
2025-08-04 15:24:45,326 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.863, H2=-5.511, H3=-10.131
2025-08-04 15:24:45,355 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2246 sites
2025-08-04 15:24:49,367 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-1.607, H2=4.169, H3=4.284
2025-08-04 15:24:49,397 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2616 sites
2025-08-04 15:24:54,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=9.810, H2=-0.507, H3=4.114
2025-08-04 15:24:54,095 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3172 sites
2025-08-04 15:24:59,832 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=42.900, H2=12.408, H3=-1.387
2025-08-04 15:24:59,858 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 547 sites
2025-08-04 15:25:00,835 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.438, H2=-9.708, H3=2.450
2025-08-04 15:25:00,861 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 393 sites
2025-08-04 15:25:01,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-4.380, H2=-0.631, H3=-1.882
2025-08-04 15:25:01,593 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1381 sites
2025-08-04 15:25:04,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=39.410, H2=-1.129, H3=-5.873
2025-08-04 15:25:04,068 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 538 sites
2025-08-04 15:25:05,025 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-2.323, H2=-1.200, H3=-3.327
2025-08-04 15:25:05,054 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1746 sites
2025-08-04 15:25:08,142 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-0.962, H2=-2.108, H3=-2.658
2025-08-04 15:25:08,173 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2306 sites
2025-08-04 15:25:12,291 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=3.420, H2=-1.068, H3=3.719
2025-08-04 15:25:12,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 4922 sites
2025-08-04 15:25:21,022 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=38.248, H2=18.292, H3=11.187
2025-08-04 15:25:21,049 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 999 sites
2025-08-04 15:25:22,814 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=1.655, H2=2.317, H3=3.373
2025-08-04 15:25:22,842 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1322 sites
2025-08-04 15:25:25,184 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=-3.247, H2=-2.082, H3=-1.463
2025-08-04 15:25:25,213 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1188 sites
2025-08-04 15:25:27,309 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=2.265, H2=1.466, H3=1.146
2025-08-04 15:25:27,337 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1298 sites
2025-08-04 15:25:29,641 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=4.637, H2=0.134, H3=1.322
2025-08-04 15:25:29,669 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1270 sites
2025-08-04 15:25:31,922 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-0.696, H2=3.100, H3=-3.411
2025-08-04 15:25:31,947 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=27 has insufficient data
2025-08-04 15:25:31,947 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=28
2025-08-04 15:25:31,949 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28 -9223372036854775808]
2025-08-04 15:25:31,978 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:25:35,685 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:25:35,715 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2296 sites
2025-08-04 15:25:39,753 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=19.625, H2=-1.487, H3=0.653
2025-08-04 15:25:39,781 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1146 sites
2025-08-04 15:25:41,796 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=22.993, H2=-0.546, H3=0.454
2025-08-04 15:25:41,828 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2634 sites
2025-08-04 15:25:46,484 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.193, H2=-1.042, H3=-1.877
2025-08-04 15:25:46,514 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1965 sites
2025-08-04 15:25:49,995 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.612, H2=-1.872, H3=0.252
2025-08-04 15:25:50,028 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3107 sites
2025-08-04 15:25:55,508 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.876, H2=-0.262, H3=-3.658
2025-08-04 15:25:55,540 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3073 sites
2025-08-04 15:26:00,985 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=23.564, H2=1.113, H3=1.619
2025-08-04 15:26:01,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2879 sites
2025-08-04 15:26:06,079 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=8.719, H2=2.191, H3=1.408
2025-08-04 15:26:06,107 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1014 sites
2025-08-04 15:26:07,899 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=3.414, H2=5.522, H3=1.000
2025-08-04 15:26:07,928 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2179 sites
2025-08-04 15:26:11,784 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=7.443, H2=0.268, H3=0.336
2025-08-04 15:26:11,814 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2308 sites
2025-08-04 15:26:15,907 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=4.454, H2=-0.177, H3=-0.319
2025-08-04 15:26:15,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1708 sites
2025-08-04 15:26:18,967 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.863, H2=-5.511, H3=-10.131
2025-08-04 15:26:18,996 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2246 sites
2025-08-04 15:26:22,990 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-1.607, H2=4.169, H3=4.284
2025-08-04 15:26:23,021 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2616 sites
2025-08-04 15:26:27,667 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=9.810, H2=-0.507, H3=4.114
2025-08-04 15:26:27,699 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3172 sites
2025-08-04 15:26:33,301 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=42.900, H2=12.408, H3=-1.387
2025-08-04 15:26:33,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 547 sites
2025-08-04 15:26:34,289 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=2.438, H2=-9.708, H3=2.450
2025-08-04 15:26:34,315 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 393 sites
2025-08-04 15:26:35,010 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-4.380, H2=-0.631, H3=-1.882
2025-08-04 15:26:35,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1381 sites
2025-08-04 15:26:37,460 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=39.410, H2=-1.129, H3=-5.873
2025-08-04 15:26:37,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 538 sites
2025-08-04 15:26:38,436 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-2.323, H2=-1.200, H3=-3.327
2025-08-04 15:26:38,464 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1746 sites
2025-08-04 15:26:41,523 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-0.962, H2=-2.108, H3=-2.658
2025-08-04 15:26:41,552 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 2306 sites
2025-08-04 15:26:45,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=3.420, H2=-1.068, H3=3.719
2025-08-04 15:26:45,658 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1862 sites
2025-08-04 15:26:48,949 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=16.769, H2=13.501, H3=3.660
2025-08-04 15:26:48,980 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 3060 sites
2025-08-04 15:26:54,400 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=7.578, H2=6.187, H3=6.764
2025-08-04 15:26:54,427 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 999 sites
2025-08-04 15:26:56,176 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=1.550, H2=2.122, H3=4.440
2025-08-04 15:26:56,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1322 sites
2025-08-04 15:26:58,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=-5.197, H2=-3.116, H3=-1.674
2025-08-04 15:26:58,557 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1188 sites
2025-08-04 15:27:00,659 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=1.710, H2=2.068, H3=1.721
2025-08-04 15:27:00,686 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1298 sites
2025-08-04 15:27:02,988 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=6.045, H2=0.304, H3=1.054
2025-08-04 15:27:03,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1270 sites
2025-08-04 15:27:05,266 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=-1.142, H2=4.127, H3=-1.841
2025-08-04 15:27:05,293 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=28 has insufficient data
2025-08-04 15:27:05,293 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=29
2025-08-04 15:27:05,306 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29 -9223372036854775808]
2025-08-04 15:27:05,335 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:27:09,069 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:27:09,098 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2296 sites
2025-08-04 15:27:13,161 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=19.625, H2=-1.487, H3=0.653
2025-08-04 15:27:13,189 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1146 sites
2025-08-04 15:27:15,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=22.993, H2=-0.546, H3=0.454
2025-08-04 15:27:15,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2634 sites
2025-08-04 15:27:19,890 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.193, H2=-1.042, H3=-1.877
2025-08-04 15:27:19,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1965 sites
2025-08-04 15:27:23,423 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=21.612, H2=-1.872, H3=0.252
2025-08-04 15:27:23,455 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3107 sites
2025-08-04 15:27:28,932 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=10.876, H2=-0.262, H3=-3.658
2025-08-04 15:27:28,960 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 966 sites
2025-08-04 15:27:30,677 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=11.341, H2=-0.004, H3=-0.288
2025-08-04 15:27:30,708 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2107 sites
2025-08-04 15:27:34,441 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=11.013, H2=1.313, H3=1.319
2025-08-04 15:27:34,472 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2879 sites
2025-08-04 15:27:39,570 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=13.406, H2=2.865, H3=1.093
2025-08-04 15:27:39,597 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1014 sites
2025-08-04 15:27:41,408 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=2.875, H2=4.014, H3=0.818
2025-08-04 15:27:41,439 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2179 sites
2025-08-04 15:27:45,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.897, H2=0.411, H3=1.093
2025-08-04 15:27:45,356 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2308 sites
2025-08-04 15:27:49,452 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.058, H2=-0.088, H3=-0.795
2025-08-04 15:27:49,480 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1708 sites
2025-08-04 15:27:52,516 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=5.521, H2=-6.225, H3=-10.402
2025-08-04 15:27:52,546 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2246 sites
2025-08-04 15:27:56,538 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-1.017, H2=4.464, H3=5.229
2025-08-04 15:27:56,568 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2616 sites
2025-08-04 15:28:01,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=14.185, H2=-0.400, H3=3.224
2025-08-04 15:28:01,275 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3172 sites
2025-08-04 15:28:06,937 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=27.883, H2=13.294, H3=-2.544
2025-08-04 15:28:06,964 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 547 sites
2025-08-04 15:28:07,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=2.919, H2=-6.194, H3=2.542
2025-08-04 15:28:07,973 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 393 sites
2025-08-04 15:28:08,682 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-4.524, H2=-0.786, H3=-2.363
2025-08-04 15:28:08,710 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1381 sites
2025-08-04 15:28:11,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=54.618, H2=-0.765, H3=-6.541
2025-08-04 15:28:11,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 538 sites
2025-08-04 15:28:12,178 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-1.747, H2=-1.211, H3=-3.575
2025-08-04 15:28:12,206 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1746 sites
2025-08-04 15:28:15,326 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-1.223, H2=-2.367, H3=-2.277
2025-08-04 15:28:15,356 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 2306 sites
2025-08-04 15:28:19,449 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=3.340, H2=-0.689, H3=3.939
2025-08-04 15:28:19,478 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1862 sites
2025-08-04 15:28:22,796 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=12.703, H2=10.001, H3=2.783
2025-08-04 15:28:22,827 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 3060 sites
2025-08-04 15:28:28,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=8.993, H2=5.898, H3=9.948
2025-08-04 15:28:28,272 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 999 sites
2025-08-04 15:28:30,050 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=1.914, H2=1.531, H3=3.686
2025-08-04 15:28:30,077 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 1322 sites
2025-08-04 15:28:32,412 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=-3.348, H2=-1.967, H3=-1.175
2025-08-04 15:28:32,440 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1188 sites
2025-08-04 15:28:34,543 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=2.510, H2=1.409, H3=1.544
2025-08-04 15:28:34,571 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1298 sites
2025-08-04 15:28:36,885 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=4.797, H2=0.443, H3=0.722
2025-08-04 15:28:36,913 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1270 sites
2025-08-04 15:28:39,170 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=-1.005, H2=3.258, H3=-3.116
2025-08-04 15:28:39,195 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=29 has insufficient data
2025-08-04 15:28:39,195 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=30
2025-08-04 15:28:39,197 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
 -9223372036854775808]
2025-08-04 15:28:39,227 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:28:42,985 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:28:43,016 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2296 sites
2025-08-04 15:28:47,081 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=19.625, H2=-1.487, H3=0.653
2025-08-04 15:28:47,109 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1146 sites
2025-08-04 15:28:49,153 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=22.993, H2=-0.546, H3=0.454
2025-08-04 15:28:49,183 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2634 sites
2025-08-04 15:28:53,853 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.193, H2=-1.042, H3=-1.877
2025-08-04 15:28:53,881 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1061 sites
2025-08-04 15:28:55,760 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.109, H2=2.088, H3=2.909
2025-08-04 15:28:55,788 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 904 sites
2025-08-04 15:28:57,396 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.837, H2=-3.669, H3=-1.898
2025-08-04 15:28:57,429 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3107 sites
2025-08-04 15:29:02,951 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.056, H2=-0.247, H3=-5.552
2025-08-04 15:29:02,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 966 sites
2025-08-04 15:29:04,694 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=7.722, H2=-0.219, H3=-0.715
2025-08-04 15:29:04,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2107 sites
2025-08-04 15:29:08,485 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.898, H2=1.261, H3=1.405
2025-08-04 15:29:08,516 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2879 sites
2025-08-04 15:29:13,609 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.867, H2=2.145, H3=1.010
2025-08-04 15:29:13,637 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1014 sites
2025-08-04 15:29:15,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.831, H2=3.653, H3=1.229
2025-08-04 15:29:15,477 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2179 sites
2025-08-04 15:29:19,336 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=11.843, H2=0.510, H3=0.771
2025-08-04 15:29:19,366 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2308 sites
2025-08-04 15:29:23,476 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=5.465, H2=-0.681, H3=-0.951
2025-08-04 15:29:23,505 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1708 sites
2025-08-04 15:29:26,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=4.843, H2=-6.183, H3=-8.702
2025-08-04 15:29:26,558 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2246 sites
2025-08-04 15:29:30,520 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-1.016, H2=3.668, H3=4.382
2025-08-04 15:29:30,551 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 2616 sites
2025-08-04 15:29:35,198 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=11.204, H2=-0.038, H3=3.064
2025-08-04 15:29:35,229 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3172 sites
2025-08-04 15:29:40,847 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=26.865, H2=9.451, H3=-2.020
2025-08-04 15:29:40,874 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 547 sites
2025-08-04 15:29:41,840 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=2.493, H2=-8.651, H3=2.853
2025-08-04 15:29:41,867 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 393 sites
2025-08-04 15:29:42,565 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-4.875, H2=-0.211, H3=-2.470
2025-08-04 15:29:42,594 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1381 sites
2025-08-04 15:29:45,027 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=39.617, H2=-1.080, H3=-6.618
2025-08-04 15:29:45,054 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 538 sites
2025-08-04 15:29:46,011 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=-2.658, H2=-1.449, H3=-3.791
2025-08-04 15:29:46,040 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1746 sites
2025-08-04 15:29:49,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=-1.422, H2=-1.849, H3=-2.895
2025-08-04 15:29:49,150 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 2306 sites
2025-08-04 15:29:53,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=2.398, H2=-0.590, H3=3.093
2025-08-04 15:29:53,244 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1862 sites
2025-08-04 15:29:56,541 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=20.667, H2=11.919, H3=4.787
2025-08-04 15:29:56,573 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 3060 sites
2025-08-04 15:30:01,965 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=10.353, H2=4.296, H3=7.795
2025-08-04 15:30:01,993 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 26 with 999 sites
2025-08-04 15:30:03,751 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 26: H1=0.959, H2=1.706, H3=4.219
2025-08-04 15:30:03,780 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 27 with 1322 sites
2025-08-04 15:30:06,106 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 27: H1=-3.095, H2=-1.399, H3=-1.572
2025-08-04 15:30:06,134 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 28 with 1188 sites
2025-08-04 15:30:08,230 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 28: H1=2.151, H2=1.870, H3=1.013
2025-08-04 15:30:08,258 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 29 with 1298 sites
2025-08-04 15:30:10,547 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 29: H1=6.234, H2=-0.068, H3=0.939
2025-08-04 15:30:10,575 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 30 with 1270 sites
2025-08-04 15:30:12,832 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 30: H1=-1.169, H2=4.051, H3=-1.991
2025-08-04 15:30:12,858 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=30 has insufficient data
2025-08-04 15:30:12,858 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=31
2025-08-04 15:30:12,860 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26                   27
                   28                   29                   30
                   31 -9223372036854775808]
2025-08-04 15:30:12,902 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 2106 sites
2025-08-04 15:30:16,622 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=29.868, H2=-3.042, H3=-0.328
2025-08-04 15:30:16,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 2296 sites
2025-08-04 15:30:20,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=19.625, H2=-1.487, H3=0.653
2025-08-04 15:30:20,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1146 sites
2025-08-04 15:30:22,800 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=22.993, H2=-0.546, H3=0.454
2025-08-04 15:30:22,831 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 2634 sites
2025-08-04 15:30:27,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=35.193, H2=-1.042, H3=-1.877
2025-08-04 15:30:27,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1061 sites
2025-08-04 15:30:29,392 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=15.109, H2=2.088, H3=2.909
2025-08-04 15:30:29,420 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 904 sites
2025-08-04 15:30:31,021 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=6.837, H2=-3.669, H3=-1.898
2025-08-04 15:30:31,053 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3107 sites
2025-08-04 15:30:36,583 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=14.056, H2=-0.247, H3=-5.552
2025-08-04 15:30:36,611 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 966 sites
2025-08-04 15:30:38,315 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=7.722, H2=-0.219, H3=-0.715
2025-08-04 15:30:38,345 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2107 sites
2025-08-04 15:30:42,072 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.898, H2=1.261, H3=1.405
2025-08-04 15:30:42,104 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2879 sites
2025-08-04 15:30:47,193 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=9.867, H2=2.145, H3=1.010
2025-08-04 15:30:47,220 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1014 sites
2025-08-04 15:30:49,001 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=2.831, H2=3.653, H3=1.229
2025-08-04 15:30:49,031 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2179 sites
2025-08-04 15:30:52,864 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=11.843, H2=0.510, H3=0.771
2025-08-04 15:30:52,894 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2308 sites
2025-08-04 15:30:57,054 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=5.465, H2=-0.681, H3=-0.951
2025-08-04 15:30:57,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1708 sites
