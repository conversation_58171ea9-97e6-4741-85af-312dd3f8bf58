2025-07-23 09:37:54,342 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-07-23 09:37:54,342 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-23 09:37:54,342 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_22_hierarchical/CI_results_hierarchical.nc
2025-07-23 09:37:54,342 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_22_hierarchical
2025-07-23 09:37:54,359 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-07-23 09:37:54,359 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-07-23 09:37:54,360 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-07-23 09:37:54,360 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-07-23 09:37:54,361 - src.models.remap_clusters - INFO - remap_clusters.py:203 - Starting cluster remapping for: regionalization_w_WT_22_hierarchical/CI_results_hierarchical.nc
2025-07-23 09:37:54,361 - src.models.remap_clusters - INFO - remap_clusters.py:206 - Loading cluster data
2025-07-23 09:37:54,954 - src.models.remap_clusters - INFO - remap_clusters.py:59 - Loading statistics from: cluster_precip_stats.nc
2025-07-23 09:37:55,301 - src.models.remap_clusters - INFO - remap_clusters.py:120 - Initializing variable standardizer for remapping
2025-07-23 09:37:55,333 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'elevation': 75025 valid values out of 308485 total
2025-07-23 09:37:55,366 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lat': 308485 valid values out of 308485 total
2025-07-23 09:37:55,368 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'lon': 308485 valid values out of 308485 total
2025-07-23 09:37:55,386 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mam': 52597 valid values out of 308485 total
2025-07-23 09:37:55,411 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mat': 308485 valid values out of 308485 total
2025-07-23 09:37:55,438 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mad': 52597 valid values out of 308485 total
2025-07-23 09:37:55,461 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msm_djf' contains only NaN values - skipping
2025-07-23 09:37:55,511 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_mam': 52597 valid values out of 308485 total
2025-07-23 09:37:55,522 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_jja': 52597 valid values out of 308485 total
2025-07-23 09:37:55,524 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msm_son': 52597 valid values out of 308485 total
2025-07-23 09:37:55,550 - src.features.standardize - WARNING - standardize.py:62 - Variable 'mst_djf' contains only NaN values - skipping
2025-07-23 09:37:55,551 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_mam': 308485 valid values out of 308485 total
2025-07-23 09:37:55,553 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_jja': 308485 valid values out of 308485 total
2025-07-23 09:37:55,556 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'mst_son': 308485 valid values out of 308485 total
2025-07-23 09:37:55,622 - src.features.standardize - WARNING - standardize.py:62 - Variable 'msd_djf' contains only NaN values - skipping
2025-07-23 09:37:55,623 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_mam': 52597 valid values out of 308485 total
2025-07-23 09:37:55,626 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_jja': 52597 valid values out of 308485 total
2025-07-23 09:37:55,629 - src.features.standardize - INFO - standardize.py:73 - Loaded variable 'msd_son': 52597 valid values out of 308485 total
2025-07-23 09:37:55,629 - src.features.standardize - INFO - standardize.py:81 - Skipped variables due to all NaN values: ['msm_djf', 'mst_djf', 'msd_djf']
2025-07-23 09:37:55,632 - src.features.standardize - INFO - standardize.py:94 - Common mask created: 52359 valid points out of 308485 total
2025-07-23 09:37:55,632 - src.models.remap_clusters - INFO - remap_clusters.py:124 - Fitting standardizer to establish grid structure
2025-07-23 09:37:55,632 - src.features.standardize - INFO - standardize.py:103 - Computing statistics for standardization:
2025-07-23 09:37:55,635 - src.features.standardize - INFO - standardize.py:108 -   elevation: mean=750.4659, std=693.5920
2025-07-23 09:37:55,636 - src.features.standardize - INFO - standardize.py:108 -   lat: mean=48.0043, std=15.3765
2025-07-23 09:37:55,638 - src.features.standardize - INFO - standardize.py:108 -   lon: mean=-104.4326, std=36.2606
2025-07-23 09:37:55,640 - src.features.standardize - INFO - standardize.py:108 -   mam: mean=44.7558, std=21.5373
2025-07-23 09:37:55,642 - src.features.standardize - INFO - standardize.py:108 -   mat: mean=71.7459, std=182.9391
2025-07-23 09:37:55,644 - src.features.standardize - INFO - standardize.py:108 -   mad: mean=2.3677, std=1.2507
2025-07-23 09:37:55,646 - src.features.standardize - INFO - standardize.py:108 -   msm_mam: mean=23.6376, std=12.1947
2025-07-23 09:37:55,662 - src.features.standardize - INFO - standardize.py:108 -   msm_jja: mean=34.1348, std=16.7019
2025-07-23 09:37:55,664 - src.features.standardize - INFO - standardize.py:108 -   msm_son: mean=31.6128, std=16.2878
2025-07-23 09:37:55,666 - src.features.standardize - INFO - standardize.py:108 -   mst_mam: mean=13.5243, std=34.3658
2025-07-23 09:37:55,668 - src.features.standardize - INFO - standardize.py:108 -   mst_jja: mean=37.7684, std=99.0316
2025-07-23 09:37:55,669 - src.features.standardize - INFO - standardize.py:108 -   mst_son: mean=20.4533, std=52.6869
2025-07-23 09:37:55,672 - src.features.standardize - INFO - standardize.py:108 -   msd_mam: mean=2.6135, std=1.3616
2025-07-23 09:37:55,674 - src.features.standardize - INFO - standardize.py:108 -   msd_jja: mean=2.4078, std=1.4096
2025-07-23 09:37:55,676 - src.features.standardize - INFO - standardize.py:108 -   msd_son: mean=2.1663, std=1.1903
2025-07-23 09:37:55,686 - src.features.standardize - INFO - standardize.py:136 - Transformed data shape: (52359, 15) [52359 points x 15 variables]
2025-07-23 09:37:55,686 - src.models.remap_clusters - INFO - remap_clusters.py:127 - Grid structure established: (515, 599)
2025-07-23 09:37:55,686 - src.models.remap_clusters - INFO - remap_clusters.py:128 - Valid points: 52359
2025-07-23 09:37:55,687 - src.models.remap_clusters - INFO - remap_clusters.py:218 - Found 100 cluster variables: ['k=1', 'k=2', 'k=3', 'k=4', 'k=5', 'k=6', 'k=7', 'k=8', 'k=9', 'k=10', 'k=11', 'k=12', 'k=13', 'k=14', 'k=15', 'k=16', 'k=17', 'k=18', 'k=19', 'k=20', 'k=21', 'k=22', 'k=23', 'k=24', 'k=25', 'k=26', 'k=27', 'k=28', 'k=29', 'k=30', 'k=31', 'k=32', 'k=33', 'k=34', 'k=35', 'k=36', 'k=37', 'k=38', 'k=39', 'k=40', 'k=41', 'k=42', 'k=43', 'k=44', 'k=45', 'k=46', 'k=47', 'k=48', 'k=49', 'k=50', 'k=51', 'k=52', 'k=53', 'k=54', 'k=55', 'k=56', 'k=57', 'k=58', 'k=59', 'k=60', 'k=61', 'k=62', 'k=63', 'k=64', 'k=65', 'k=66', 'k=67', 'k=68', 'k=69', 'k=70', 'k=71', 'k=72', 'k=73', 'k=74', 'k=75', 'k=76', 'k=77', 'k=78', 'k=79', 'k=80', 'k=81', 'k=82', 'k=83', 'k=84', 'k=85', 'k=86', 'k=87', 'k=88', 'k=89', 'k=90', 'k=91', 'k=92', 'k=93', 'k=94', 'k=95', 'k=96', 'k=97', 'k=98', 'k=99', 'k=100']
2025-07-23 09:37:55,688 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=1
2025-07-23 09:37:55,692 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=1
2025-07-23 09:37:55,692 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=2
2025-07-23 09:37:55,694 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=2
2025-07-23 09:37:55,694 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=3
2025-07-23 09:37:55,695 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=3
2025-07-23 09:37:55,696 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=4
2025-07-23 09:37:55,697 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=4
2025-07-23 09:37:55,698 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=5
2025-07-23 09:37:55,699 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=5
2025-07-23 09:37:55,700 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=6
2025-07-23 09:37:55,702 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=6
2025-07-23 09:37:55,702 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=7
2025-07-23 09:37:55,705 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=7
2025-07-23 09:37:55,705 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=8
2025-07-23 09:37:55,708 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=8
2025-07-23 09:37:55,708 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=9
2025-07-23 09:37:55,711 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=9
2025-07-23 09:37:55,711 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=10
2025-07-23 09:37:55,714 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=10
2025-07-23 09:37:55,714 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=11
2025-07-23 09:37:55,716 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=11
2025-07-23 09:37:55,716 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=12
2025-07-23 09:37:55,719 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=12
2025-07-23 09:37:55,726 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=13
2025-07-23 09:37:55,729 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=13
2025-07-23 09:37:55,729 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=14
2025-07-23 09:37:55,732 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=14
2025-07-23 09:37:55,732 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=15
2025-07-23 09:37:55,735 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=15
2025-07-23 09:37:55,735 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=16
2025-07-23 09:37:55,737 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=16
2025-07-23 09:37:55,737 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=17
2025-07-23 09:37:55,740 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=17
2025-07-23 09:37:55,740 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=18
2025-07-23 09:37:55,743 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=18
2025-07-23 09:37:55,743 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=19
2025-07-23 09:37:55,745 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=19
2025-07-23 09:37:55,746 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=20
2025-07-23 09:37:55,748 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=20
2025-07-23 09:37:55,749 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=21
2025-07-23 09:37:55,751 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=21
2025-07-23 09:37:55,751 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=22
2025-07-23 09:37:55,754 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=22
2025-07-23 09:37:55,754 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=23
2025-07-23 09:37:55,757 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=23
2025-07-23 09:37:55,757 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=24
2025-07-23 09:37:55,760 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=24
2025-07-23 09:37:55,760 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=25
2025-07-23 09:37:55,762 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=25
2025-07-23 09:37:55,763 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=26
2025-07-23 09:37:55,765 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=26
2025-07-23 09:37:55,765 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=27
2025-07-23 09:37:55,768 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=27
2025-07-23 09:37:55,768 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=28
2025-07-23 09:37:55,771 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=28
2025-07-23 09:37:55,771 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=29
2025-07-23 09:37:55,774 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=29
2025-07-23 09:37:55,774 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=30
2025-07-23 09:37:55,776 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=30
2025-07-23 09:37:55,777 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=31
2025-07-23 09:37:55,779 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=31
2025-07-23 09:37:55,779 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=32
2025-07-23 09:37:55,782 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=32
2025-07-23 09:37:55,782 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=33
2025-07-23 09:37:55,785 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=33
2025-07-23 09:37:55,785 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=34
2025-07-23 09:37:55,787 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=34
2025-07-23 09:37:55,788 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=35
2025-07-23 09:37:55,790 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=35
2025-07-23 09:37:55,793 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=36
2025-07-23 09:37:55,796 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=36
2025-07-23 09:37:55,796 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=37
2025-07-23 09:37:55,799 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=37
2025-07-23 09:37:55,799 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=38
2025-07-23 09:37:55,802 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=38
2025-07-23 09:37:55,802 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=39
2025-07-23 09:37:55,805 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=39
2025-07-23 09:37:55,805 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=40
2025-07-23 09:37:55,807 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=40
2025-07-23 09:37:55,807 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=41
2025-07-23 09:37:55,810 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=41
2025-07-23 09:37:55,810 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=42
2025-07-23 09:37:55,813 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=42
2025-07-23 09:37:55,813 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=43
2025-07-23 09:37:55,815 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=43
2025-07-23 09:37:55,815 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=44
2025-07-23 09:37:55,818 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=44
2025-07-23 09:37:55,818 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=45
2025-07-23 09:37:55,821 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=45
2025-07-23 09:37:55,821 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=46
2025-07-23 09:37:55,824 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=46
2025-07-23 09:37:55,824 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=47
2025-07-23 09:37:55,826 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=47
2025-07-23 09:37:55,827 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=48
2025-07-23 09:37:55,829 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=48
2025-07-23 09:37:55,829 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=49
2025-07-23 09:37:55,832 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=49
2025-07-23 09:37:55,832 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=50
2025-07-23 09:37:55,835 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=50
2025-07-23 09:37:55,835 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=51
2025-07-23 09:37:55,837 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=51
2025-07-23 09:37:55,837 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=52
2025-07-23 09:37:55,840 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=52
2025-07-23 09:37:55,840 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=53
2025-07-23 09:37:55,843 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=53
2025-07-23 09:37:55,843 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=54
2025-07-23 09:37:55,845 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=54
2025-07-23 09:37:55,846 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=55
2025-07-23 09:37:55,848 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=55
2025-07-23 09:37:55,848 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=56
2025-07-23 09:37:55,851 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=56
2025-07-23 09:37:55,851 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=57
2025-07-23 09:37:55,853 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=57
2025-07-23 09:37:55,854 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=58
2025-07-23 09:37:55,856 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=58
2025-07-23 09:37:55,859 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=59
2025-07-23 09:37:55,862 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=59
2025-07-23 09:37:55,862 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=60
2025-07-23 09:37:55,865 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=60
2025-07-23 09:37:55,865 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=61
2025-07-23 09:37:55,867 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=61
2025-07-23 09:37:55,868 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=62
2025-07-23 09:37:55,870 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=62
2025-07-23 09:37:55,870 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=63
2025-07-23 09:37:55,873 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=63
2025-07-23 09:37:55,873 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=64
2025-07-23 09:37:55,875 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=64
2025-07-23 09:37:55,875 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=65
2025-07-23 09:37:55,878 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=65
2025-07-23 09:37:55,878 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=66
2025-07-23 09:37:55,881 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=66
2025-07-23 09:37:55,881 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=67
2025-07-23 09:37:55,883 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=67
2025-07-23 09:37:55,883 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=68
2025-07-23 09:37:55,886 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=68
2025-07-23 09:37:55,886 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=69
2025-07-23 09:37:55,889 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=69
2025-07-23 09:37:55,889 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=70
2025-07-23 09:37:55,892 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=70
2025-07-23 09:37:55,892 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=71
2025-07-23 09:37:55,894 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=71
2025-07-23 09:37:55,895 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=72
2025-07-23 09:37:55,897 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=72
2025-07-23 09:37:55,897 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=73
2025-07-23 09:37:55,900 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=73
2025-07-23 09:37:55,900 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=74
2025-07-23 09:37:55,902 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=74
2025-07-23 09:37:55,902 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=75
2025-07-23 09:37:55,905 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=75
2025-07-23 09:37:55,905 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=76
2025-07-23 09:37:55,907 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=76
2025-07-23 09:37:55,908 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=77
2025-07-23 09:37:55,910 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=77
2025-07-23 09:37:55,910 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=78
2025-07-23 09:37:55,913 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=78
2025-07-23 09:37:55,913 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=79
2025-07-23 09:37:55,916 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=79
2025-07-23 09:37:55,916 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=80
2025-07-23 09:37:55,918 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=80
2025-07-23 09:37:55,918 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=81
2025-07-23 09:37:55,921 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=81
2025-07-23 09:37:55,926 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=82
2025-07-23 09:37:55,929 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=82
2025-07-23 09:37:55,929 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=83
2025-07-23 09:37:55,931 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=83
2025-07-23 09:37:55,931 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=84
2025-07-23 09:37:55,934 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=84
2025-07-23 09:37:55,934 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=85
2025-07-23 09:37:55,937 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=85
2025-07-23 09:37:55,937 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=86
2025-07-23 09:37:55,940 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=86
2025-07-23 09:37:55,940 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=87
2025-07-23 09:37:55,942 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=87
2025-07-23 09:37:55,942 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=88
2025-07-23 09:37:55,945 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=88
2025-07-23 09:37:55,945 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=89
2025-07-23 09:37:55,947 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=89
2025-07-23 09:37:55,947 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=90
2025-07-23 09:37:55,950 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=90
2025-07-23 09:37:55,950 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=91
2025-07-23 09:37:55,953 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=91
2025-07-23 09:37:55,953 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=92
2025-07-23 09:37:55,956 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=92
2025-07-23 09:37:55,956 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=93
2025-07-23 09:37:55,958 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=93
2025-07-23 09:37:55,958 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=94
2025-07-23 09:37:55,961 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=94
2025-07-23 09:37:55,961 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=95
2025-07-23 09:37:55,964 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=95
2025-07-23 09:37:55,964 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=96
2025-07-23 09:37:55,966 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=96
2025-07-23 09:37:55,966 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=97
2025-07-23 09:37:55,969 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=97
2025-07-23 09:37:55,969 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=98
2025-07-23 09:37:55,971 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=98
2025-07-23 09:37:55,972 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=99
2025-07-23 09:37:55,974 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=99
2025-07-23 09:37:55,974 - src.models.remap_clusters - INFO - remap_clusters.py:229 - Remapping variable: k=100
2025-07-23 09:37:55,977 - src.models.remap_clusters - INFO - remap_clusters.py:256 - ✓ Successfully remapped k=100
2025-07-23 09:37:55,977 - src.models.remap_clusters - INFO - remap_clusters.py:267 - Saving remapped clusters to: regionalization_w_WT_22_hierarchical/CI_results_hierarchical_remap.nc
2025-07-23 09:37:56,340 - src.models.remap_clusters - INFO - remap_clusters.py:271 - ============================================================
2025-07-23 09:37:56,340 - src.models.remap_clusters - INFO - remap_clusters.py:272 - CLUSTER REMAPPING SUMMARY
2025-07-23 09:37:56,340 - src.models.remap_clusters - INFO - remap_clusters.py:273 - ============================================================
2025-07-23 09:37:56,340 - src.models.remap_clusters - INFO - remap_clusters.py:274 - Input file: regionalization_w_WT_22_hierarchical/CI_results_hierarchical.nc
2025-07-23 09:37:56,340 - src.models.remap_clusters - INFO - remap_clusters.py:275 - Output file: regionalization_w_WT_22_hierarchical/CI_results_hierarchical_remap.nc
2025-07-23 09:37:56,359 - src.models.remap_clusters - INFO - remap_clusters.py:276 - Successfully remapped: 100 variables
2025-07-23 09:37:56,359 - src.models.remap_clusters - INFO - remap_clusters.py:278 -   - k=1, k=2, k=3, k=4, k=5, k=6, k=7, k=8, k=9, k=10, k=11, k=12, k=13, k=14, k=15, k=16, k=17, k=18, k=19, k=20, k=21, k=22, k=23, k=24, k=25, k=26, k=27, k=28, k=29, k=30, k=31, k=32, k=33, k=34, k=35, k=36, k=37, k=38, k=39, k=40, k=41, k=42, k=43, k=44, k=45, k=46, k=47, k=48, k=49, k=50, k=51, k=52, k=53, k=54, k=55, k=56, k=57, k=58, k=59, k=60, k=61, k=62, k=63, k=64, k=65, k=66, k=67, k=68, k=69, k=70, k=71, k=72, k=73, k=74, k=75, k=76, k=77, k=78, k=79, k=80, k=81, k=82, k=83, k=84, k=85, k=86, k=87, k=88, k=89, k=90, k=91, k=92, k=93, k=94, k=95, k=96, k=97, k=98, k=99, k=100
2025-07-23 09:37:56,360 - src.models.remap_clusters - INFO - remap_clusters.py:285 - ============================================================
2025-07-23 09:37:56,362 - __main__ - INFO - run_heterogeneity_analysis.py:549 - Using remapped cluster file: regionalization_w_WT_22_hierarchical/CI_results_hierarchical_remap.nc
2025-07-23 09:37:56,362 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-07-23 09:37:56,362 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-07-23 09:37:56,403 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-07-23 09:38:06,233 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_22_hierarchical/CI_results_hierarchical_remap.nc
2025-07-23 09:38:06,799 - __main__ - INFO - run_heterogeneity_analysis.py:137 - Data loaded successfully
2025-07-23 09:38:06,799 - __main__ - INFO - run_heterogeneity_analysis.py:559 - Running heterogeneity analysis
2025-07-23 09:38:06,799 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=2
2025-07-23 09:38:06,801 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2 -9223372036854775808]
2025-07-23 09:38:06,865 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 21336 sites
2025-07-23 09:38:45,493 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=128.169, H2=5.543, H3=0.211
2025-07-23 09:38:45,576 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 31023 sites
2025-07-23 09:39:42,506 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=77.636, H2=24.226, H3=5.853
2025-07-23 09:39:42,533 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=2 has insufficient data
2025-07-23 09:39:42,533 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=3
2025-07-23 09:39:42,535 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
 -9223372036854775808]
2025-07-23 09:39:42,598 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 21336 sites
2025-07-23 09:40:21,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=128.169, H2=5.543, H3=0.211
2025-07-23 09:40:21,367 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 16772 sites
2025-07-23 09:40:51,518 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=39.037, H2=12.202, H3=0.413
2025-07-23 09:40:51,570 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 14251 sites
2025-07-23 09:41:17,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=74.260, H2=28.092, H3=15.116
2025-07-23 09:41:17,029 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=3 has insufficient data
2025-07-23 09:41:17,029 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=4
2025-07-23 09:41:17,031 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4 -9223372036854775808]
2025-07-23 09:41:17,094 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 21336 sites
2025-07-23 09:41:55,227 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=128.169, H2=5.543, H3=0.211
2025-07-23 09:41:55,283 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 16772 sites
2025-07-23 09:42:25,009 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=39.037, H2=12.202, H3=0.413
2025-07-23 09:42:25,038 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1508 sites
2025-07-23 09:42:27,733 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=18.470, H2=1.376, H3=-1.891
2025-07-23 09:42:27,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 12743 sites
2025-07-23 09:42:50,516 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=54.679, H2=16.706, H3=11.509
2025-07-23 09:42:50,542 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=4 has insufficient data
2025-07-23 09:42:50,542 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=5
2025-07-23 09:42:50,544 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5 -9223372036854775808]
2025-07-23 09:42:50,606 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 21336 sites
2025-07-23 09:43:28,682 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=128.169, H2=5.543, H3=0.211
2025-07-23 09:43:28,721 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 6915 sites
2025-07-23 09:43:40,993 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=30.482, H2=9.190, H3=-1.643
2025-07-23 09:43:41,036 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 9857 sites
2025-07-23 09:43:58,580 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=18.315, H2=5.812, H3=2.436
2025-07-23 09:43:58,609 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1508 sites
2025-07-23 09:44:01,293 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=18.617, H2=1.484, H3=-2.100
2025-07-23 09:44:01,342 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 12743 sites
2025-07-23 09:44:24,045 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=62.150, H2=15.709, H3=9.099
2025-07-23 09:44:24,071 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=5 has insufficient data
2025-07-23 09:44:24,071 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=6
2025-07-23 09:44:24,072 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
 -9223372036854775808]
2025-07-23 09:44:24,106 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4287 sites
2025-07-23 09:44:31,677 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=20.580, H2=5.173, H3=1.193
2025-07-23 09:44:31,734 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 17049 sites
2025-07-23 09:45:01,938 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=104.542, H2=1.009, H3=-0.489
2025-07-23 09:45:01,977 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 6915 sites
2025-07-23 09:45:14,248 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=27.609, H2=8.403, H3=-2.105
2025-07-23 09:45:14,291 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 9857 sites
2025-07-23 09:45:31,754 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=18.857, H2=4.779, H3=2.005
2025-07-23 09:45:31,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1508 sites
2025-07-23 09:45:34,458 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=13.867, H2=1.405, H3=-2.313
2025-07-23 09:45:34,506 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 12743 sites
2025-07-23 09:45:57,213 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=56.312, H2=21.173, H3=8.578
2025-07-23 09:45:57,239 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=6 has insufficient data
2025-07-23 09:45:57,239 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=7
2025-07-23 09:45:57,241 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7 -9223372036854775808]
2025-07-23 09:45:57,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4287 sites
2025-07-23 09:46:04,904 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=20.580, H2=5.173, H3=1.193
2025-07-23 09:46:04,936 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3328 sites
2025-07-23 09:46:10,859 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=70.165, H2=-1.603, H3=-1.033
2025-07-23 09:46:10,909 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13721 sites
2025-07-23 09:46:35,268 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.833, H2=0.665, H3=-0.915
2025-07-23 09:46:35,306 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6915 sites
2025-07-23 09:46:47,532 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.340, H2=8.996, H3=-2.792
2025-07-23 09:46:47,575 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9857 sites
2025-07-23 09:47:05,031 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.000, H2=3.839, H3=1.627
2025-07-23 09:47:05,060 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1508 sites
2025-07-23 09:47:07,729 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.903, H2=1.896, H3=-2.211
2025-07-23 09:47:07,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 12743 sites
2025-07-23 09:47:30,304 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=41.479, H2=18.852, H3=8.968
2025-07-23 09:47:30,329 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=7 has insufficient data
2025-07-23 09:47:30,330 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=8
2025-07-23 09:47:30,331 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8 -9223372036854775808]
2025-07-23 09:47:30,364 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4287 sites
2025-07-23 09:47:37,948 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=20.580, H2=5.173, H3=1.193
2025-07-23 09:47:37,980 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3328 sites
2025-07-23 09:47:43,897 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=70.165, H2=-1.603, H3=-1.033
2025-07-23 09:47:43,947 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13721 sites
2025-07-23 09:48:08,431 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.833, H2=0.665, H3=-0.915
2025-07-23 09:48:08,469 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6915 sites
2025-07-23 09:48:20,751 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.340, H2=8.996, H3=-2.792
2025-07-23 09:48:20,794 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9857 sites
2025-07-23 09:48:38,320 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.000, H2=3.839, H3=1.627
2025-07-23 09:48:38,349 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1508 sites
2025-07-23 09:48:41,017 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.903, H2=1.896, H3=-2.211
2025-07-23 09:48:41,050 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3790 sites
2025-07-23 09:48:47,735 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=13.178, H2=3.039, H3=-0.852
2025-07-23 09:48:47,776 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 8953 sites
2025-07-23 09:49:03,632 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=36.897, H2=16.211, H3=11.568
2025-07-23 09:49:03,658 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=8 has insufficient data
2025-07-23 09:49:03,658 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=9
2025-07-23 09:49:03,660 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
 -9223372036854775808]
2025-07-23 09:49:03,692 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4287 sites
2025-07-23 09:49:11,284 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=20.580, H2=5.173, H3=1.193
2025-07-23 09:49:11,316 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3328 sites
2025-07-23 09:49:17,202 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=70.165, H2=-1.603, H3=-1.033
2025-07-23 09:49:17,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13721 sites
2025-07-23 09:49:41,469 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.833, H2=0.665, H3=-0.915
2025-07-23 09:49:41,507 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6915 sites
2025-07-23 09:49:53,805 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.340, H2=8.996, H3=-2.792
2025-07-23 09:49:53,848 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9857 sites
2025-07-23 09:50:11,314 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.000, H2=3.839, H3=1.627
2025-07-23 09:50:11,343 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 1508 sites
2025-07-23 09:50:14,005 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=12.903, H2=1.896, H3=-2.211
2025-07-23 09:50:14,037 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3790 sites
2025-07-23 09:50:20,726 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=13.178, H2=3.039, H3=-0.852
2025-07-23 09:50:20,755 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1803 sites
2025-07-23 09:50:23,950 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=4.052, H2=-1.280, H3=-0.841
2025-07-23 09:50:23,989 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 7150 sites
2025-07-23 09:50:36,694 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=46.528, H2=21.794, H3=13.669
2025-07-23 09:50:36,720 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=9 has insufficient data
2025-07-23 09:50:36,721 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=10
2025-07-23 09:50:36,722 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10 -9223372036854775808]
2025-07-23 09:50:36,768 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4287 sites
2025-07-23 09:50:44,369 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=20.580, H2=5.173, H3=1.193
2025-07-23 09:50:44,401 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3328 sites
2025-07-23 09:50:50,290 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=70.165, H2=-1.603, H3=-1.033
2025-07-23 09:50:50,340 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13721 sites
2025-07-23 09:51:14,501 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.833, H2=0.665, H3=-0.915
2025-07-23 09:51:14,539 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6915 sites
2025-07-23 09:51:26,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.340, H2=8.996, H3=-2.792
2025-07-23 09:51:26,779 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 9857 sites
2025-07-23 09:51:44,179 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=23.000, H2=3.839, H3=1.627
2025-07-23 09:51:44,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 196 sites
2025-07-23 09:51:44,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=3.109, H2=-4.904, H3=1.031
2025-07-23 09:51:44,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 1312 sites
2025-07-23 09:51:46,900 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=10.577, H2=3.165, H3=-2.901
2025-07-23 09:51:46,932 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3790 sites
2025-07-23 09:51:53,641 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=15.031, H2=2.712, H3=-1.396
2025-07-23 09:51:53,670 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1803 sites
2025-07-23 09:51:56,849 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=5.323, H2=-2.659, H3=-0.707
2025-07-23 09:51:56,888 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 7150 sites
2025-07-23 09:52:09,576 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=60.462, H2=17.538, H3=9.457
2025-07-23 09:52:09,601 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=10 has insufficient data
2025-07-23 09:52:09,601 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=11
2025-07-23 09:52:09,603 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11 -9223372036854775808]
2025-07-23 09:52:09,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 4287 sites
2025-07-23 09:52:17,209 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=20.580, H2=5.173, H3=1.193
2025-07-23 09:52:17,240 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3328 sites
2025-07-23 09:52:23,124 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=70.165, H2=-1.603, H3=-1.033
2025-07-23 09:52:23,174 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 13721 sites
2025-07-23 09:52:47,638 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=82.833, H2=0.665, H3=-0.915
2025-07-23 09:52:47,677 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 6915 sites
2025-07-23 09:53:00,073 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=30.340, H2=8.996, H3=-2.792
2025-07-23 09:53:00,104 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 3464 sites
2025-07-23 09:53:06,322 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=9.373, H2=-1.401, H3=-2.326
2025-07-23 09:53:06,359 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6393 sites
2025-07-23 09:53:17,886 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=14.153, H2=4.298, H3=3.491
2025-07-23 09:53:17,911 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 196 sites
2025-07-23 09:53:18,259 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=5.191, H2=-6.334, H3=1.164
2025-07-23 09:53:18,287 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 1312 sites
2025-07-23 09:53:20,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=12.327, H2=2.602, H3=-2.865
2025-07-23 09:53:20,652 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3790 sites
2025-07-23 09:53:27,391 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=21.025, H2=2.942, H3=-1.197
2025-07-23 09:53:27,420 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1803 sites
2025-07-23 09:53:30,629 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=4.593, H2=-1.697, H3=-0.752
2025-07-23 09:53:30,667 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 7150 sites
2025-07-23 09:53:43,390 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=41.229, H2=28.072, H3=12.748
2025-07-23 09:53:43,434 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=11 has insufficient data
2025-07-23 09:53:43,435 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=12
2025-07-23 09:53:43,436 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
 -9223372036854775808]
2025-07-23 09:53:43,463 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 09:53:45,499 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 09:53:45,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 09:53:51,122 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 09:53:51,154 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3328 sites
2025-07-23 09:53:57,070 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=109.128, H2=-1.856, H3=-0.961
2025-07-23 09:53:57,121 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 13721 sites
2025-07-23 09:54:21,197 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=63.172, H2=0.146, H3=-1.619
2025-07-23 09:54:21,236 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6915 sites
2025-07-23 09:54:33,468 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=32.693, H2=8.869, H3=-2.234
2025-07-23 09:54:33,500 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3464 sites
2025-07-23 09:54:39,625 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=7.841, H2=-0.485, H3=-1.937
2025-07-23 09:54:39,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6393 sites
2025-07-23 09:54:50,941 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=11.407, H2=4.596, H3=3.726
2025-07-23 09:54:50,966 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 196 sites
2025-07-23 09:54:51,313 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.064, H2=-4.585, H3=1.066
2025-07-23 09:54:51,340 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1312 sites
2025-07-23 09:54:53,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.968, H2=3.003, H3=-2.669
2025-07-23 09:54:53,679 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3790 sites
2025-07-23 09:55:00,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=19.972, H2=2.707, H3=-1.067
2025-07-23 09:55:00,447 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1803 sites
2025-07-23 09:55:03,647 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=5.164, H2=-1.451, H3=-0.122
2025-07-23 09:55:03,686 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 7150 sites
2025-07-23 09:55:16,362 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=42.573, H2=21.607, H3=11.881
2025-07-23 09:55:16,387 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=12 has insufficient data
2025-07-23 09:55:16,387 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=13
2025-07-23 09:55:16,389 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13 -9223372036854775808]
2025-07-23 09:55:16,429 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 09:55:18,456 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 09:55:18,487 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 09:55:24,039 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 09:55:24,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3328 sites
2025-07-23 09:55:29,960 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=109.128, H2=-1.856, H3=-0.961
2025-07-23 09:55:30,010 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 13721 sites
2025-07-23 09:55:54,350 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=63.172, H2=0.146, H3=-1.619
2025-07-23 09:55:54,388 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 6915 sites
2025-07-23 09:56:06,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=32.693, H2=8.869, H3=-2.234
2025-07-23 09:56:06,587 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3464 sites
2025-07-23 09:56:12,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=7.841, H2=-0.485, H3=-1.937
2025-07-23 09:56:12,728 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6393 sites
2025-07-23 09:56:24,035 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=11.407, H2=4.596, H3=3.726
2025-07-23 09:56:24,061 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 196 sites
2025-07-23 09:56:24,410 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=3.064, H2=-4.585, H3=1.066
2025-07-23 09:56:24,437 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 1312 sites
2025-07-23 09:56:26,763 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.968, H2=3.003, H3=-2.669
2025-07-23 09:56:26,790 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 850 sites
2025-07-23 09:56:28,300 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=-7.710, H2=-6.827, H3=-6.551
2025-07-23 09:56:28,331 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2940 sites
2025-07-23 09:56:33,526 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=22.815, H2=2.684, H3=2.051
2025-07-23 09:56:33,554 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1803 sites
2025-07-23 09:56:36,744 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=6.532, H2=-2.757, H3=-0.679
2025-07-23 09:56:36,782 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 7150 sites
2025-07-23 09:56:49,541 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=42.839, H2=19.680, H3=12.580
2025-07-23 09:56:49,566 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=13 has insufficient data
2025-07-23 09:56:49,567 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=14
2025-07-23 09:56:49,568 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14 -9223372036854775808]
2025-07-23 09:56:49,595 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 09:56:51,619 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 09:56:51,651 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 09:56:57,265 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 09:56:57,296 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3328 sites
2025-07-23 09:57:03,223 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=109.128, H2=-1.856, H3=-0.961
2025-07-23 09:57:03,259 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5499 sites
2025-07-23 09:57:13,035 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=25.973, H2=4.117, H3=0.514
2025-07-23 09:57:13,076 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 8222 sites
2025-07-23 09:57:27,541 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=90.603, H2=-3.373, H3=-2.130
2025-07-23 09:57:27,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 6915 sites
2025-07-23 09:57:39,830 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=25.178, H2=7.868, H3=-1.622
2025-07-23 09:57:39,862 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3464 sites
2025-07-23 09:57:45,950 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=10.139, H2=-0.435, H3=-2.304
2025-07-23 09:57:45,988 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6393 sites
2025-07-23 09:57:57,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=14.816, H2=4.897, H3=3.661
2025-07-23 09:57:57,336 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 196 sites
2025-07-23 09:57:57,683 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=4.705, H2=-3.955, H3=1.021
2025-07-23 09:57:57,711 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 1312 sites
2025-07-23 09:58:00,042 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=12.163, H2=3.212, H3=-2.578
2025-07-23 09:58:00,069 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 850 sites
2025-07-23 09:58:01,581 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=-5.914, H2=-5.144, H3=-5.936
2025-07-23 09:58:01,612 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2940 sites
2025-07-23 09:58:06,857 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=32.042, H2=2.765, H3=1.666
2025-07-23 09:58:06,886 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1803 sites
2025-07-23 09:58:10,078 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=5.517, H2=-2.121, H3=-0.907
2025-07-23 09:58:10,117 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 7150 sites
2025-07-23 09:58:22,796 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=60.743, H2=21.923, H3=13.916
2025-07-23 09:58:22,821 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=14 has insufficient data
2025-07-23 09:58:22,822 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=15
2025-07-23 09:58:22,829 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
 -9223372036854775808]
2025-07-23 09:58:22,856 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 09:58:24,896 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 09:58:24,927 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 09:58:30,491 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 09:58:30,528 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3328 sites
2025-07-23 09:58:36,506 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=109.128, H2=-1.856, H3=-0.961
2025-07-23 09:58:36,542 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5499 sites
2025-07-23 09:58:46,318 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=25.973, H2=4.117, H3=0.514
2025-07-23 09:58:46,352 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4851 sites
2025-07-23 09:58:54,976 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=64.066, H2=-0.751, H3=-1.055
2025-07-23 09:58:55,008 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3371 sites
2025-07-23 09:59:00,986 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=28.186, H2=-7.685, H3=-2.486
2025-07-23 09:59:01,024 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6915 sites
2025-07-23 09:59:13,313 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.927, H2=7.795, H3=-1.808
2025-07-23 09:59:13,345 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3464 sites
2025-07-23 09:59:19,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.443, H2=-0.665, H3=-2.504
2025-07-23 09:59:19,526 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6393 sites
2025-07-23 09:59:30,829 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=15.871, H2=4.910, H3=3.470
2025-07-23 09:59:30,855 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 196 sites
2025-07-23 09:59:31,199 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.561, H2=-7.168, H3=0.841
2025-07-23 09:59:31,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1312 sites
2025-07-23 09:59:33,560 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.871, H2=2.738, H3=-2.114
2025-07-23 09:59:33,587 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 850 sites
2025-07-23 09:59:35,098 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-11.980, H2=-7.778, H3=-6.904
2025-07-23 09:59:35,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2940 sites
2025-07-23 09:59:40,381 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=17.762, H2=2.861, H3=1.364
2025-07-23 09:59:40,409 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1803 sites
2025-07-23 09:59:43,646 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=4.721, H2=-1.814, H3=-0.513
2025-07-23 09:59:43,684 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 7150 sites
2025-07-23 09:59:56,489 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=58.774, H2=19.722, H3=11.932
2025-07-23 09:59:56,515 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=15 has insufficient data
2025-07-23 09:59:56,515 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=16
2025-07-23 09:59:56,517 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16 -9223372036854775808]
2025-07-23 09:59:56,545 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 09:59:58,620 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 09:59:58,650 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:00:04,261 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:00:04,292 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 3328 sites
2025-07-23 10:00:10,218 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=109.128, H2=-1.856, H3=-0.961
2025-07-23 10:00:10,254 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 5499 sites
2025-07-23 10:00:20,021 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=25.973, H2=4.117, H3=0.514
2025-07-23 10:00:20,070 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 4851 sites
2025-07-23 10:00:28,669 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=64.066, H2=-0.751, H3=-1.055
2025-07-23 10:00:28,701 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 3371 sites
2025-07-23 10:00:34,696 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=28.186, H2=-7.685, H3=-2.486
2025-07-23 10:00:34,734 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 6915 sites
2025-07-23 10:00:46,947 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=21.927, H2=7.795, H3=-1.808
2025-07-23 10:00:46,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3464 sites
2025-07-23 10:00:53,142 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=9.443, H2=-0.665, H3=-2.504
2025-07-23 10:00:53,179 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 6393 sites
2025-07-23 10:01:04,445 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=15.871, H2=4.910, H3=3.470
2025-07-23 10:01:04,471 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 196 sites
2025-07-23 10:01:04,817 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=3.561, H2=-7.168, H3=0.841
2025-07-23 10:01:04,845 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 1312 sites
2025-07-23 10:01:07,166 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=10.871, H2=2.738, H3=-2.114
2025-07-23 10:01:07,193 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 850 sites
2025-07-23 10:01:08,686 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=-11.980, H2=-7.778, H3=-6.904
2025-07-23 10:01:08,717 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2940 sites
2025-07-23 10:01:13,897 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=17.762, H2=2.861, H3=1.364
2025-07-23 10:01:13,926 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 1803 sites
2025-07-23 10:01:17,095 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=4.721, H2=-1.814, H3=-0.513
2025-07-23 10:01:17,128 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 4111 sites
2025-07-23 10:01:24,408 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=4.319, H2=6.966, H3=9.754
2025-07-23 10:01:24,439 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3039 sites
2025-07-23 10:01:29,826 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=44.044, H2=14.863, H3=4.495
2025-07-23 10:01:29,851 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=16 has insufficient data
2025-07-23 10:01:29,851 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=17
2025-07-23 10:01:29,853 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17 -9223372036854775808]
2025-07-23 10:01:29,893 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:01:31,938 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:01:31,969 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:01:37,533 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:01:37,561 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:01:40,062 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:01:40,091 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1917 sites
2025-07-23 10:01:43,492 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=26.083, H2=0.929, H3=-0.557
2025-07-23 10:01:43,528 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 5499 sites
2025-07-23 10:01:53,256 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=27.345, H2=3.528, H3=0.298
2025-07-23 10:01:53,290 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4851 sites
2025-07-23 10:02:01,837 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=74.566, H2=0.097, H3=-0.927
2025-07-23 10:02:01,868 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3371 sites
2025-07-23 10:02:07,816 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=27.692, H2=-6.856, H3=-3.000
2025-07-23 10:02:07,854 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6915 sites
2025-07-23 10:02:20,103 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=24.216, H2=8.022, H3=-1.960
2025-07-23 10:02:20,135 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3464 sites
2025-07-23 10:02:26,238 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.120, H2=-0.842, H3=-2.634
2025-07-23 10:02:26,290 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6393 sites
2025-07-23 10:02:37,616 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.063, H2=5.164, H3=3.304
2025-07-23 10:02:37,642 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 196 sites
2025-07-23 10:02:37,988 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.901, H2=-3.717, H3=1.198
2025-07-23 10:02:38,017 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1312 sites
2025-07-23 10:02:40,338 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=18.333, H2=3.869, H3=-3.224
2025-07-23 10:02:40,365 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 850 sites
2025-07-23 10:02:41,865 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=-6.588, H2=-8.083, H3=-6.798
2025-07-23 10:02:41,896 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2940 sites
2025-07-23 10:02:47,102 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=21.245, H2=2.618, H3=1.774
2025-07-23 10:02:47,130 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1803 sites
2025-07-23 10:02:50,300 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=5.354, H2=-2.341, H3=-1.001
2025-07-23 10:02:50,333 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 4111 sites
2025-07-23 10:02:57,632 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=3.404, H2=6.673, H3=8.523
2025-07-23 10:02:57,663 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 3039 sites
2025-07-23 10:03:03,065 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=43.583, H2=12.188, H3=4.840
2025-07-23 10:03:03,099 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=17 has insufficient data
2025-07-23 10:03:03,099 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=18
2025-07-23 10:03:03,101 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
 -9223372036854775808]
2025-07-23 10:03:03,129 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:03:05,174 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:03:05,205 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:03:10,773 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:03:10,801 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:03:13,310 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:03:13,339 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1917 sites
2025-07-23 10:03:16,755 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=26.083, H2=0.929, H3=-0.557
2025-07-23 10:03:16,790 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 5499 sites
2025-07-23 10:03:26,506 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=27.345, H2=3.528, H3=0.298
2025-07-23 10:03:26,540 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4851 sites
2025-07-23 10:03:35,205 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=74.566, H2=0.097, H3=-0.927
2025-07-23 10:03:35,237 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3371 sites
2025-07-23 10:03:41,253 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=27.692, H2=-6.856, H3=-3.000
2025-07-23 10:03:41,291 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6915 sites
2025-07-23 10:03:53,639 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=24.216, H2=8.022, H3=-1.960
2025-07-23 10:03:53,671 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3464 sites
2025-07-23 10:03:59,850 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.120, H2=-0.842, H3=-2.634
2025-07-23 10:03:59,887 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6393 sites
2025-07-23 10:04:11,301 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.063, H2=5.164, H3=3.304
2025-07-23 10:04:11,327 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 196 sites
2025-07-23 10:04:11,678 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.901, H2=-3.717, H3=1.198
2025-07-23 10:04:11,705 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 593 sites
2025-07-23 10:04:12,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.885, H2=-7.193, H3=-6.534
2025-07-23 10:04:12,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 719 sites
2025-07-23 10:04:14,067 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=3.057, H2=-4.043, H3=-1.112
2025-07-23 10:04:14,106 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 850 sites
2025-07-23 10:04:15,617 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-7.921, H2=-5.803, H3=-7.370
2025-07-23 10:04:15,656 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2940 sites
2025-07-23 10:04:20,906 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=25.521, H2=2.626, H3=1.454
2025-07-23 10:04:20,934 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1803 sites
2025-07-23 10:04:24,140 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=4.183, H2=-1.765, H3=-0.954
2025-07-23 10:04:24,172 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 4111 sites
2025-07-23 10:04:31,445 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=3.350, H2=4.557, H3=10.004
2025-07-23 10:04:31,476 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 3039 sites
2025-07-23 10:04:36,898 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=42.781, H2=16.455, H3=5.895
2025-07-23 10:04:36,924 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=18 has insufficient data
2025-07-23 10:04:36,924 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=19
2025-07-23 10:04:36,926 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19 -9223372036854775808]
2025-07-23 10:04:36,953 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:04:39,004 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:04:39,036 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:04:44,628 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:04:44,656 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:04:47,159 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:04:47,188 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1917 sites
2025-07-23 10:04:50,614 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=26.083, H2=0.929, H3=-0.557
2025-07-23 10:04:50,649 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 5499 sites
2025-07-23 10:05:00,549 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=27.345, H2=3.528, H3=0.298
2025-07-23 10:05:00,585 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4851 sites
2025-07-23 10:05:09,333 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=74.566, H2=0.097, H3=-0.927
2025-07-23 10:05:09,365 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3371 sites
2025-07-23 10:05:15,472 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=27.692, H2=-6.856, H3=-3.000
2025-07-23 10:05:15,510 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 6915 sites
2025-07-23 10:05:28,099 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=24.216, H2=8.022, H3=-1.960
2025-07-23 10:05:28,131 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 3464 sites
2025-07-23 10:05:34,313 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=14.120, H2=-0.842, H3=-2.634
2025-07-23 10:05:34,350 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 6393 sites
2025-07-23 10:05:45,803 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=17.063, H2=5.164, H3=3.304
2025-07-23 10:05:45,853 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 196 sites
2025-07-23 10:05:46,202 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=3.901, H2=-3.717, H3=1.198
2025-07-23 10:05:46,228 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 593 sites
2025-07-23 10:05:47,284 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=7.885, H2=-7.193, H3=-6.534
2025-07-23 10:05:47,311 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 719 sites
2025-07-23 10:05:48,587 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=3.057, H2=-4.043, H3=-1.112
2025-07-23 10:05:48,614 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 850 sites
2025-07-23 10:05:50,125 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=-7.921, H2=-5.803, H3=-7.370
2025-07-23 10:05:50,153 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 1569 sites
2025-07-23 10:05:52,959 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-2.047, H2=-3.895, H3=-1.251
2025-07-23 10:05:52,987 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1371 sites
2025-07-23 10:05:55,432 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=9.906, H2=0.614, H3=-2.107
2025-07-23 10:05:55,461 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1803 sites
2025-07-23 10:05:58,660 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=6.031, H2=-1.303, H3=-0.515
2025-07-23 10:05:58,693 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 4111 sites
2025-07-23 10:06:06,003 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=3.412, H2=5.324, H3=8.429
2025-07-23 10:06:06,034 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 3039 sites
2025-07-23 10:06:11,440 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=39.297, H2=16.711, H3=5.432
2025-07-23 10:06:11,465 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=19 has insufficient data
2025-07-23 10:06:11,465 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=20
2025-07-23 10:06:11,467 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20 -9223372036854775808]
2025-07-23 10:06:11,494 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:06:13,526 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:06:13,557 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:06:19,153 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:06:19,182 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:06:21,684 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:06:21,713 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1917 sites
2025-07-23 10:06:25,112 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=26.083, H2=0.929, H3=-0.557
2025-07-23 10:06:25,147 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 5499 sites
2025-07-23 10:06:34,860 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=27.345, H2=3.528, H3=0.298
2025-07-23 10:06:34,903 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 4851 sites
2025-07-23 10:06:43,486 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=74.566, H2=0.097, H3=-0.927
2025-07-23 10:06:43,517 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 3371 sites
2025-07-23 10:06:49,463 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=27.692, H2=-6.856, H3=-3.000
2025-07-23 10:06:49,496 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4107 sites
2025-07-23 10:06:56,752 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=7.351, H2=6.632, H3=6.543
2025-07-23 10:06:56,782 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2808 sites
2025-07-23 10:07:01,768 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=27.171, H2=0.512, H3=-14.614
2025-07-23 10:07:01,800 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 3464 sites
2025-07-23 10:07:07,936 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=11.561, H2=-0.833, H3=-2.355
2025-07-23 10:07:07,972 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 6393 sites
2025-07-23 10:07:19,302 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=13.085, H2=7.159, H3=4.279
2025-07-23 10:07:19,329 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 196 sites
2025-07-23 10:07:19,677 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=4.060, H2=-4.527, H3=0.641
2025-07-23 10:07:19,704 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 593 sites
2025-07-23 10:07:20,756 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=6.808, H2=-7.553, H3=-6.195
2025-07-23 10:07:20,783 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 719 sites
2025-07-23 10:07:22,063 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.478, H2=-2.906, H3=-1.483
2025-07-23 10:07:22,090 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 850 sites
2025-07-23 10:07:23,598 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=-6.796, H2=-5.051, H3=-7.406
2025-07-23 10:07:23,626 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 1569 sites
2025-07-23 10:07:26,419 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-2.827, H2=-4.116, H3=-1.208
2025-07-23 10:07:26,448 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1371 sites
2025-07-23 10:07:28,884 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=8.772, H2=0.279, H3=-1.691
2025-07-23 10:07:28,913 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1803 sites
2025-07-23 10:07:32,119 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=4.895, H2=-1.440, H3=-0.172
2025-07-23 10:07:32,152 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 4111 sites
2025-07-23 10:07:39,466 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=3.348, H2=5.422, H3=8.989
2025-07-23 10:07:39,497 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 3039 sites
2025-07-23 10:07:44,903 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=39.043, H2=14.771, H3=7.985
2025-07-23 10:07:44,929 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=20 has insufficient data
2025-07-23 10:07:44,929 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=21
2025-07-23 10:07:44,931 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
 -9223372036854775808]
2025-07-23 10:07:45,005 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:07:47,049 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:07:47,080 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:07:52,665 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:07:52,692 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:07:55,192 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:07:55,221 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1917 sites
2025-07-23 10:07:58,604 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=26.083, H2=0.929, H3=-0.557
2025-07-23 10:07:58,635 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2873 sites
2025-07-23 10:08:03,717 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=17.979, H2=0.108, H3=-4.235
2025-07-23 10:08:03,748 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2626 sites
2025-07-23 10:08:08,406 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.563, H2=5.482, H3=7.611
2025-07-23 10:08:08,440 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4851 sites
2025-07-23 10:08:17,062 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=59.894, H2=0.269, H3=-0.981
2025-07-23 10:08:17,094 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3371 sites
2025-07-23 10:08:23,072 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.696, H2=-6.592, H3=-2.874
2025-07-23 10:08:23,105 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4107 sites
2025-07-23 10:08:30,349 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.331, H2=6.942, H3=6.346
2025-07-23 10:08:30,379 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2808 sites
2025-07-23 10:08:35,358 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=30.318, H2=0.622, H3=-10.499
2025-07-23 10:08:35,390 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3464 sites
2025-07-23 10:08:41,542 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.488, H2=-0.684, H3=-2.076
2025-07-23 10:08:41,579 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 6393 sites
2025-07-23 10:08:52,937 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=16.527, H2=6.784, H3=3.830
2025-07-23 10:08:52,963 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 196 sites
2025-07-23 10:08:53,311 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=3.364, H2=-5.133, H3=0.905
2025-07-23 10:08:53,337 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 593 sites
2025-07-23 10:08:54,387 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=7.500, H2=-6.789, H3=-5.677
2025-07-23 10:08:54,414 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 719 sites
2025-07-23 10:08:55,687 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=3.984, H2=-2.933, H3=-1.333
2025-07-23 10:08:55,714 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 850 sites
2025-07-23 10:08:57,217 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=-8.709, H2=-8.752, H3=-6.490
2025-07-23 10:08:57,257 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 1569 sites
2025-07-23 10:09:00,056 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-1.812, H2=-3.351, H3=-1.147
2025-07-23 10:09:00,084 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1371 sites
2025-07-23 10:09:02,524 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=9.632, H2=0.457, H3=-2.165
2025-07-23 10:09:02,556 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1803 sites
2025-07-23 10:09:05,730 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=4.858, H2=-2.041, H3=-0.805
2025-07-23 10:09:05,763 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 4111 sites
2025-07-23 10:09:13,015 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=2.911, H2=5.425, H3=7.971
2025-07-23 10:09:13,046 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 3039 sites
2025-07-23 10:09:18,428 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=60.289, H2=22.199, H3=7.466
2025-07-23 10:09:18,453 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=21 has insufficient data
2025-07-23 10:09:18,453 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=22
2025-07-23 10:09:18,455 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22 -9223372036854775808]
2025-07-23 10:09:18,482 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:09:20,528 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:09:20,560 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:09:26,170 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:09:26,198 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:09:28,737 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:09:28,766 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1917 sites
2025-07-23 10:09:32,214 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=26.083, H2=0.929, H3=-0.557
2025-07-23 10:09:32,245 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2873 sites
2025-07-23 10:09:37,452 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=17.979, H2=0.108, H3=-4.235
2025-07-23 10:09:37,483 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2626 sites
2025-07-23 10:09:42,227 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.563, H2=5.482, H3=7.611
2025-07-23 10:09:42,261 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4851 sites
2025-07-23 10:09:50,893 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=59.894, H2=0.269, H3=-0.981
2025-07-23 10:09:50,925 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3371 sites
2025-07-23 10:09:56,946 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.696, H2=-6.592, H3=-2.874
2025-07-23 10:09:56,979 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4107 sites
2025-07-23 10:10:04,325 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.331, H2=6.942, H3=6.346
2025-07-23 10:10:04,370 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2808 sites
2025-07-23 10:10:09,376 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=30.318, H2=0.622, H3=-10.499
2025-07-23 10:10:09,408 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3464 sites
2025-07-23 10:10:15,590 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.488, H2=-0.684, H3=-2.076
2025-07-23 10:10:15,620 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2416 sites
2025-07-23 10:10:19,901 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=2.265, H2=1.587, H3=5.241
2025-07-23 10:10:19,933 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3977 sites
2025-07-23 10:10:26,961 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=7.475, H2=5.742, H3=1.984
2025-07-23 10:10:26,987 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 196 sites
2025-07-23 10:10:27,333 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.039, H2=-4.535, H3=0.681
2025-07-23 10:10:27,359 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 593 sites
2025-07-23 10:10:28,409 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=10.329, H2=-6.064, H3=-7.830
2025-07-23 10:10:28,436 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 719 sites
2025-07-23 10:10:29,704 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=4.635, H2=-6.246, H3=-1.243
2025-07-23 10:10:29,730 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 850 sites
2025-07-23 10:10:31,234 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-6.578, H2=-4.932, H3=-6.113
2025-07-23 10:10:31,262 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1569 sites
2025-07-23 10:10:34,055 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-2.466, H2=-4.728, H3=-0.801
2025-07-23 10:10:34,083 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1371 sites
2025-07-23 10:10:36,505 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=12.982, H2=0.620, H3=-2.509
2025-07-23 10:10:36,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1803 sites
2025-07-23 10:10:39,724 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=4.064, H2=-1.327, H3=-0.680
2025-07-23 10:10:39,757 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 4111 sites
2025-07-23 10:10:47,006 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=3.653, H2=5.990, H3=7.903
2025-07-23 10:10:47,037 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 3039 sites
2025-07-23 10:10:52,401 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=52.628, H2=18.094, H3=7.301
2025-07-23 10:10:52,427 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=22 has insufficient data
2025-07-23 10:10:52,427 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=23
2025-07-23 10:10:52,429 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23 -9223372036854775808]
2025-07-23 10:10:52,456 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:10:54,510 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:10:54,543 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:11:00,055 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:11:00,083 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:11:02,584 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:11:02,613 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1917 sites
2025-07-23 10:11:05,998 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=26.083, H2=0.929, H3=-0.557
2025-07-23 10:11:06,029 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2873 sites
2025-07-23 10:11:11,103 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=17.979, H2=0.108, H3=-4.235
2025-07-23 10:11:11,133 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2626 sites
2025-07-23 10:11:15,776 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.563, H2=5.482, H3=7.611
2025-07-23 10:11:15,811 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4851 sites
2025-07-23 10:11:24,362 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=59.894, H2=0.269, H3=-0.981
2025-07-23 10:11:24,394 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 3371 sites
2025-07-23 10:11:30,336 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=29.696, H2=-6.592, H3=-2.874
2025-07-23 10:11:30,370 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 4107 sites
2025-07-23 10:11:37,658 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=11.331, H2=6.942, H3=6.346
2025-07-23 10:11:37,688 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 2808 sites
2025-07-23 10:11:42,630 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=30.318, H2=0.622, H3=-10.499
2025-07-23 10:11:42,662 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 3464 sites
2025-07-23 10:11:48,821 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=8.488, H2=-0.684, H3=-2.076
2025-07-23 10:11:48,851 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2416 sites
2025-07-23 10:11:53,125 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=2.265, H2=1.587, H3=5.241
2025-07-23 10:11:53,157 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3977 sites
2025-07-23 10:12:00,178 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=7.475, H2=5.742, H3=1.984
2025-07-23 10:12:00,204 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 196 sites
2025-07-23 10:12:00,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=3.039, H2=-4.535, H3=0.681
2025-07-23 10:12:00,577 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 593 sites
2025-07-23 10:12:01,630 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=10.329, H2=-6.064, H3=-7.830
2025-07-23 10:12:01,656 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 719 sites
2025-07-23 10:12:02,941 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=4.635, H2=-6.246, H3=-1.243
2025-07-23 10:12:02,968 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 850 sites
2025-07-23 10:12:04,474 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=-6.578, H2=-4.932, H3=-6.113
2025-07-23 10:12:04,502 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 1569 sites
2025-07-23 10:12:07,270 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-2.466, H2=-4.728, H3=-0.801
2025-07-23 10:12:07,309 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1371 sites
2025-07-23 10:12:09,730 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=12.982, H2=0.620, H3=-2.509
2025-07-23 10:12:09,759 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1803 sites
2025-07-23 10:12:12,941 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=4.064, H2=-1.327, H3=-0.680
2025-07-23 10:12:12,973 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 4111 sites
2025-07-23 10:12:20,245 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=3.653, H2=5.990, H3=7.903
2025-07-23 10:12:20,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1617 sites
2025-07-23 10:12:23,142 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=11.422, H2=3.792, H3=4.523
2025-07-23 10:12:23,170 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1422 sites
2025-07-23 10:12:25,702 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=11.169, H2=0.543, H3=-5.232
2025-07-23 10:12:25,727 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=23 has insufficient data
2025-07-23 10:12:25,727 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=24
2025-07-23 10:12:25,729 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
 -9223372036854775808]
2025-07-23 10:12:25,756 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:12:27,802 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:12:27,833 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:12:33,431 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:12:33,459 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:12:35,962 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:12:35,991 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 1917 sites
2025-07-23 10:12:39,394 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=26.083, H2=0.929, H3=-0.557
2025-07-23 10:12:39,425 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 2873 sites
2025-07-23 10:12:44,551 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=17.979, H2=0.108, H3=-4.235
2025-07-23 10:12:44,582 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2626 sites
2025-07-23 10:12:49,263 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=13.563, H2=5.482, H3=7.611
2025-07-23 10:12:49,297 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 4851 sites
2025-07-23 10:12:57,870 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=59.894, H2=0.269, H3=-0.981
2025-07-23 10:12:57,900 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 2702 sites
2025-07-23 10:13:02,697 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=26.684, H2=-6.101, H3=-3.226
2025-07-23 10:13:02,723 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 669 sites
2025-07-23 10:13:03,925 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=16.015, H2=-1.106, H3=-0.282
2025-07-23 10:13:03,958 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 4107 sites
2025-07-23 10:13:11,220 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=10.217, H2=7.822, H3=5.760
2025-07-23 10:13:11,250 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 2808 sites
2025-07-23 10:13:16,246 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=26.265, H2=0.705, H3=-11.528
2025-07-23 10:13:16,278 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 3464 sites
2025-07-23 10:13:22,395 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=13.249, H2=-0.538, H3=-2.068
2025-07-23 10:13:22,425 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 2416 sites
2025-07-23 10:13:26,713 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=1.551, H2=1.054, H3=2.911
2025-07-23 10:13:26,760 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3977 sites
2025-07-23 10:13:33,871 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=9.495, H2=4.723, H3=2.348
2025-07-23 10:13:33,897 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 196 sites
2025-07-23 10:13:34,246 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=4.154, H2=-3.599, H3=1.299
2025-07-23 10:13:34,273 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 593 sites
2025-07-23 10:13:35,337 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=8.299, H2=-7.660, H3=-6.722
2025-07-23 10:13:35,364 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 719 sites
2025-07-23 10:13:36,663 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=4.543, H2=-3.190, H3=-1.091
2025-07-23 10:13:36,690 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 850 sites
2025-07-23 10:13:38,215 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=-6.480, H2=-6.608, H3=-6.232
2025-07-23 10:13:38,243 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 1569 sites
2025-07-23 10:13:41,084 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-2.170, H2=-4.018, H3=-1.434
2025-07-23 10:13:41,112 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1371 sites
2025-07-23 10:13:43,574 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=10.067, H2=0.576, H3=-2.439
2025-07-23 10:13:43,602 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1803 sites
2025-07-23 10:13:46,828 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=7.239, H2=-1.822, H3=-0.736
2025-07-23 10:13:46,861 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 4111 sites
2025-07-23 10:13:54,180 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=3.801, H2=6.346, H3=11.048
2025-07-23 10:13:54,208 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 1617 sites
2025-07-23 10:13:57,068 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=7.018, H2=3.202, H3=4.139
2025-07-23 10:13:57,095 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1422 sites
2025-07-23 10:13:59,600 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=13.063, H2=0.795, H3=-6.403
2025-07-23 10:13:59,625 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=24 has insufficient data
2025-07-23 10:13:59,626 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=25
2025-07-23 10:13:59,627 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25 -9223372036854775808]
2025-07-23 10:13:59,668 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:14:01,691 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:14:01,722 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:14:07,221 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:14:07,249 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:14:09,725 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:14:09,751 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 593 sites
2025-07-23 10:14:10,790 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.891, H2=-0.214, H3=-2.249
2025-07-23 10:14:10,818 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1324 sites
2025-07-23 10:14:13,164 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.263, H2=0.199, H3=1.290
2025-07-23 10:14:13,195 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2873 sites
2025-07-23 10:14:18,245 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.512, H2=0.983, H3=-5.120
2025-07-23 10:14:18,275 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2626 sites
2025-07-23 10:14:22,925 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=12.412, H2=5.788, H3=7.404
2025-07-23 10:14:22,959 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4851 sites
2025-07-23 10:14:31,487 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=67.377, H2=-0.033, H3=-1.092
2025-07-23 10:14:31,518 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2702 sites
2025-07-23 10:14:36,274 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=32.977, H2=-10.650, H3=-2.707
2025-07-23 10:14:36,314 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 669 sites
2025-07-23 10:14:37,497 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=15.568, H2=-1.210, H3=-0.538
2025-07-23 10:14:37,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4107 sites
2025-07-23 10:14:44,742 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.799, H2=8.594, H3=7.586
2025-07-23 10:14:44,773 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 2808 sites
2025-07-23 10:14:49,724 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=29.006, H2=0.798, H3=-14.971
2025-07-23 10:14:49,756 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 3464 sites
2025-07-23 10:14:55,832 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=8.112, H2=-0.696, H3=-2.363
2025-07-23 10:14:55,862 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 2416 sites
2025-07-23 10:15:00,093 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=1.315, H2=1.230, H3=3.681
2025-07-23 10:15:00,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 3977 sites
2025-07-23 10:15:07,107 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=9.369, H2=5.598, H3=2.041
2025-07-23 10:15:07,143 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 196 sites
2025-07-23 10:15:07,487 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 16: H1=4.367, H2=-7.644, H3=0.883
2025-07-23 10:15:07,514 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 17 with 593 sites
2025-07-23 10:15:08,555 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 17: H1=9.938, H2=-5.575, H3=-7.286
2025-07-23 10:15:08,581 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 18 with 719 sites
2025-07-23 10:15:09,843 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 18: H1=4.604, H2=-3.844, H3=-0.943
2025-07-23 10:15:09,870 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 19 with 850 sites
2025-07-23 10:15:11,363 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 19: H1=-9.096, H2=-5.654, H3=-6.886
2025-07-23 10:15:11,391 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 20 with 1569 sites
2025-07-23 10:15:14,158 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 20: H1=-1.873, H2=-4.467, H3=-1.074
2025-07-23 10:15:14,186 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 21 with 1371 sites
2025-07-23 10:15:16,604 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 21: H1=12.609, H2=0.418, H3=-2.214
2025-07-23 10:15:16,632 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 22 with 1803 sites
2025-07-23 10:15:19,796 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 22: H1=5.313, H2=-1.563, H3=-1.016
2025-07-23 10:15:19,829 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 23 with 4111 sites
2025-07-23 10:15:27,038 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 23: H1=3.695, H2=6.101, H3=6.751
2025-07-23 10:15:27,067 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 24 with 1617 sites
2025-07-23 10:15:29,892 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 24: H1=9.556, H2=3.197, H3=6.331
2025-07-23 10:15:29,920 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 25 with 1422 sites
2025-07-23 10:15:32,418 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 25: H1=14.853, H2=0.044, H3=-5.586
2025-07-23 10:15:32,443 - __main__ - WARNING - run_heterogeneity_analysis.py:588 - Region -9223372036854775808 in k=25 has insufficient data
2025-07-23 10:15:32,443 - __main__ - INFO - run_heterogeneity_analysis.py:563 - Processing k=26
2025-07-23 10:15:32,445 - __main__ - WARNING - run_heterogeneity_analysis.py:576 - Region [                   1                    2                    3
                    4                    5                    6
                    7                    8                    9
                   10                   11                   12
                   13                   14                   15
                   16                   17                   18
                   19                   20                   21
                   22                   23                   24
                   25                   26 -9223372036854775808]
2025-07-23 10:15:32,473 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 1 with 1149 sites
2025-07-23 10:15:34,502 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 1: H1=3.115, H2=1.281, H3=-2.127
2025-07-23 10:15:34,534 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 2 with 3138 sites
2025-07-23 10:15:40,066 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 2: H1=9.517, H2=3.076, H3=2.158
2025-07-23 10:15:40,094 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 3 with 1411 sites
2025-07-23 10:15:42,576 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 3: H1=23.884, H2=-4.051, H3=-1.314
2025-07-23 10:15:42,603 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 4 with 593 sites
2025-07-23 10:15:43,669 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 4: H1=11.891, H2=-0.214, H3=-2.249
2025-07-23 10:15:43,697 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 5 with 1324 sites
2025-07-23 10:15:46,034 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 5: H1=16.263, H2=0.199, H3=1.290
2025-07-23 10:15:46,065 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 6 with 2873 sites
2025-07-23 10:15:51,096 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 6: H1=17.512, H2=0.983, H3=-5.120
2025-07-23 10:15:51,126 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 7 with 2626 sites
2025-07-23 10:15:55,736 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 7: H1=12.412, H2=5.788, H3=7.404
2025-07-23 10:15:55,770 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 8 with 4851 sites
2025-07-23 10:16:04,244 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 8: H1=67.377, H2=-0.033, H3=-1.092
2025-07-23 10:16:04,274 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 9 with 2702 sites
2025-07-23 10:16:09,018 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 9: H1=32.977, H2=-10.650, H3=-2.707
2025-07-23 10:16:09,045 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 10 with 669 sites
2025-07-23 10:16:10,220 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 10: H1=15.568, H2=-1.210, H3=-0.538
2025-07-23 10:16:10,253 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 11 with 4107 sites
2025-07-23 10:16:17,502 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 11: H1=6.799, H2=8.594, H3=7.586
2025-07-23 10:16:17,530 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 12 with 1029 sites
2025-07-23 10:16:19,340 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 12: H1=10.982, H2=-2.259, H3=-7.889
2025-07-23 10:16:19,369 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 13 with 1779 sites
2025-07-23 10:16:22,495 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 13: H1=10.500, H2=-4.818, H3=-9.256
2025-07-23 10:16:22,527 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 14 with 3464 sites
2025-07-23 10:16:28,628 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 14: H1=10.096, H2=-0.898, H3=-1.873
2025-07-23 10:16:28,658 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 15 with 2416 sites
2025-07-23 10:16:32,909 - __main__ - INFO - run_heterogeneity_analysis.py:259 - Region 15: H1=1.572, H2=1.090, H3=3.200
2025-07-23 10:16:32,941 - __main__ - INFO - run_heterogeneity_analysis.py:214 - Processing region 16 with 3977 sites
