#!/usr/bin/env python3
"""
Script to create grid plots for weather typing precipitation data.
"""

import os
import argparse
import logging
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap

# Set up logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import cartopy for mapping, fall back to basic plotting if not available
try:
    import cartopy.crs as ccrs
    import cartopy.feature as cfeature
    HAS_CARTOPY = True
except ImportError:
    HAS_CARTOPY = False
    logger.warning("Cartopy not available. Maps will use basic matplotlib plotting.")

def create_wt_precipitation_maps(data_path, output_dir, variables=None, latlon_path="sample_data/WRF_CCSM_lat_lon.nc"):
    """
    Create spatial maps for weather typing precipitation data.

    Parameters:
    -----------
    data_path : str
        Path to the NetCDF file with weather typing precipitation data
    output_dir : str
        Directory to save plots
    variables : list, optional
        List of variables to plot. If None, plots key variables.
    latlon_path : str, optional
        Path to the lat/lon coordinate file
    """
    logger.info(f"Loading weather typing data from {data_path}")
    logger.info(f"Loading coordinates from {latlon_path}")

    # Load the data
    try:
        ds = xr.open_dataset(data_path)
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        return

    # Load coordinates from separate file
    try:
        latlon_ds = xr.open_dataset(latlon_path)
        logger.info(f"Loaded coordinate file with variables: {list(latlon_ds.data_vars.keys())}")
        logger.info(f"Coordinate file dimensions: {dict(latlon_ds.dims)}")
    except Exception as e:
        logger.error(f"Error loading coordinate file: {e}")
        return
    
    # Default variables to plot if none specified
    if variables is None:
        variables = [
            'mean_annual_max',
            'mean_all_days', 
            'wt_freq_gridpoint',
            'wt_freq_ams_gridpoint',
            'mean_seasonal_max'
        ]
    
    # Get available k values and clusters
    # Only use k values that have actual data (k >= 2 and check for non-NaN data)
    k_values = []
    logger.info(f"Available k coordinates: {ds.k.values}")

    # Performance optimization: limit k values if requested
    available_k = ds.k.values
    if hasattr(create_wt_precipitation_maps, '_max_k') and create_wt_precipitation_maps._max_k:
        available_k = available_k[available_k <= create_wt_precipitation_maps._max_k]
        logger.info(f"Limited to k <= {create_wt_precipitation_maps._max_k}")

    if hasattr(create_wt_precipitation_maps, '_sample_k') and create_wt_precipitation_maps._sample_k:
        step = max(1, len(available_k) // create_wt_precipitation_maps._sample_k)
        available_k = available_k[::step]
        logger.info(f"Sampling every {step}th k value")

    for k in available_k:
        if k >= 2:
            # Check if this k value has any non-NaN data
            has_data = False
            for var_name in ds.data_vars.keys():
                try:
                    test_data = ds[var_name].sel(k=k)
                    # Check if any cluster has valid data for this k
                    for c in range(min(k, len(ds.cluster.values))):
                        try:
                            cluster_data = ds[var_name].sel(k=k, cluster=c)
                            if not np.all(np.isnan(cluster_data.values)):
                                has_data = True
                                break
                        except:
                            continue
                    if has_data:
                        break
                except:
                    continue

            if has_data:
                k_values.append(k)
                logger.info(f"Found valid data for k={k}")
            else:
                logger.debug(f"No valid data found for k={k}")

    if not k_values:
        logger.error("No valid k values found with data!")
        logger.info("Available variables:")
        for var_name in ds.data_vars.keys():
            var_data = ds[var_name]
            total_vals = var_data.size
            valid_vals = np.sum(~np.isnan(var_data.values))
            logger.info(f"  {var_name}: {valid_vals}/{total_vals} valid values")
        return

    logger.info(f"Will create plots for k values: {k_values}")
    
    logger.info(f"Found k values: {k_values}")
    logger.info(f"Available variables: {list(ds.data_vars.keys())}")
    
    # Create spatial maps for each variable
    for var_name in variables:
        if var_name not in ds.data_vars:
            logger.warning(f"Variable '{var_name}' not found in dataset")
            continue

        logger.info(f"Creating spatial maps for {var_name}")

        # Create spatial maps with coordinate information
        if var_name == 'mean_seasonal_max':
            create_seasonal_maps(ds, var_name, k_values, output_dir, latlon_ds)
        else:
            create_variable_maps(ds, var_name, k_values, output_dir, latlon_ds)

    ds.close()
    latlon_ds.close()
    logger.info("Finished creating weather typing precipitation maps")


# Seasonal bar plot functions removed - only spatial maps are created

def get_variable_ylabel(var_name):
    """Get appropriate y-axis label for each variable."""
    labels = {
        'mean_annual_max': 'Annual Maximum Precipitation (mm)',
        'mean_annual_total': 'Annual Total Precipitation (mm)',
        'mean_all_days': 'Mean Daily Precipitation (mm)',
        'mean_seasonal': 'Mean Seasonal Precipitation (mm)',
        'mean_seasonal_total': 'Seasonal Total Precipitation (mm)',
        'mean_seasonal_max': 'Seasonal Maximum Precipitation (mm)',
        'wt_frac': 'Weather Type Fraction',
        'wt_AMS_frac': 'AMS Fraction',
        'wt_freq_gridpoint': 'WT Frequency',
        'wt_freq_ams_gridpoint': 'WT AMS Frequency',
        'wt_entropy': 'Entropy',
        'wt_dominance': 'Dominance',
        'wt_freq_early': 'Early Season Frequency',
        'wt_freq_late': 'Late Season Frequency',
        'wt_drift_index': 'Drift Index'
    }
    return labels.get(var_name, var_name.replace('_', ' ').title())

def create_variable_maps(ds, var_name, k_values, output_dir, latlon_ds):
    """Create spatial maps for a variable showing each cluster for each k value."""

    var_data = ds[var_name]

    # Check if this is a spatial variable
    spatial_dims = [d for d in var_data.dims if d not in ['k', 'cluster', 'season']]
    if not spatial_dims:
        logger.warning(f"Variable {var_name} has no spatial dimensions, skipping maps")
        return

    # Get coordinate information from the separate lat/lon file
    lats, lons = None, None

    # Try different coordinate naming conventions in the lat/lon file
    coord_pairs = [
        ('latitude', 'longitude'),
        ('lat', 'lon'),
        ('y', 'x'),
        ('Y', 'X'),
        ('LAT', 'LON'),
        ('XLAT', 'XLONG')  # Common WRF coordinate names
    ]

    for lat_name, lon_name in coord_pairs:
        if lat_name in latlon_ds.data_vars and lon_name in latlon_ds.data_vars:
            lats = latlon_ds[lat_name].values
            lons = latlon_ds[lon_name].values
            logger.info(f"Using coordinates from lat/lon file: {lat_name}, {lon_name}")
            break
        elif lat_name in latlon_ds.coords and lon_name in latlon_ds.coords:
            lats = latlon_ds.coords[lat_name].values
            lons = latlon_ds.coords[lon_name].values
            logger.info(f"Using coordinate variables from lat/lon file: {lat_name}, {lon_name}")
            break

    if lats is None or lons is None:
        logger.error(f"No recognizable spatial coordinates found in lat/lon file")
        logger.info(f"Available data variables in lat/lon file: {list(latlon_ds.data_vars.keys())}")
        logger.info(f"Available coordinates in lat/lon file: {list(latlon_ds.coords.keys())}")
        return

    # Debug coordinate information
    logger.info(f"Coordinate ranges: lat [{lats.min():.2f}, {lats.max():.2f}], lon [{lons.min():.2f}, {lons.max():.2f}]")
    logger.info(f"Data shape: {var_data.shape}, dims: {var_data.dims}")

    # Create maps for each k value
    for k in k_values:
        logger.info(f"Creating {var_name} maps for k={k}")

        # Determine grid layout for this k
        n_clusters = k
        n_cols = min(3, n_clusters)  # Max 3 columns for maps
        n_rows = int(np.ceil(n_clusters / n_cols))

        # Create figure
        fig_width = min(20, max(12, n_cols * 6))
        fig_height = min(16, max(8, n_rows * 4))

        if HAS_CARTOPY:
            # Use cartopy for proper geographic projection
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height),
                                   subplot_kw={'projection': ccrs.PlateCarree()})
        else:
            # Use basic matplotlib
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height))

        # Handle single subplot case
        if n_rows == 1 and n_cols == 1:
            axes = np.array([[axes]])
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        elif n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Determine colormap and limits
        vmin, vmax = get_variable_limits(var_data, var_name, k)
        cmap = get_variable_colormap(var_name)

        # Plot data for each cluster
        for cluster_idx in range(n_clusters):
            row_idx = cluster_idx // n_cols
            col_idx = cluster_idx % n_cols
            ax = axes[row_idx, col_idx]

            try:
                # Get data slice for this cluster
                data_slice = var_data.sel(k=k, cluster=cluster_idx)

                # Check if data exists and has valid values
                if data_slice.size == 0:
                    raise ValueError("Empty data slice")

                plot_data = data_slice.values

                # Check for valid data
                valid_data = plot_data[~np.isnan(plot_data)]
                if len(valid_data) == 0:
                    raise ValueError("All data is NaN")

                logger.info(f"k={k}, cluster={cluster_idx}: {len(valid_data)}/{plot_data.size} valid values, range: [{valid_data.min():.3f}, {valid_data.max():.3f}]")

                if HAS_CARTOPY:
                    # Add map features
                    ax.add_feature(cfeature.COASTLINE, linewidth=0.5)
                    ax.add_feature(cfeature.BORDERS, linewidth=0.5)
                    ax.add_feature(cfeature.STATES, linewidth=0.3, alpha=0.5)

                    # Create meshgrid if coordinates are 1D
                    if len(lons.shape) == 1 and len(lats.shape) == 1:
                        lon_grid, lat_grid = np.meshgrid(lons, lats)
                    else:
                        lon_grid, lat_grid = lons, lats

                    # Plot data with reduced levels for performance
                    im = ax.pcolormesh(lon_grid, lat_grid, plot_data,
                                   cmap=cmap, vmin=vmin, vmax=vmax,
                                   transform=ccrs.PlateCarree())

                    # Set extent for CONUS
                    ax.set_extent([-125, -66.5, 25, 50], ccrs.PlateCarree())
                else:
                    # Basic matplotlib plotting
                    if len(lons.shape) == 1 and len(lats.shape) == 1:
                        lon_grid, lat_grid = np.meshgrid(lons, lats)
                    else:
                        lon_grid, lat_grid = lons, lats

                    im = ax.contourf(lon_grid, lat_grid, plot_data,
                                   levels=15, cmap=cmap, vmin=vmin, vmax=vmax, extend='both')
                    ax.set_xlim(lons.min(), lons.max())
                    ax.set_ylim(lats.min(), lats.max())
                    ax.set_xlabel('Longitude')
                    ax.set_ylabel('Latitude')

                ax.set_title(f'Cluster {cluster_idx}', fontsize=12, fontweight='bold')

                # Add colorbar for each subplot
                cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.02)
                cbar.set_label(get_variable_ylabel(var_name), fontsize=10)

            except Exception as e:
                logger.warning(f"Failed to plot k={k}, cluster={cluster_idx}: {e}")
                ax.text(0.5, 0.5, f'No Data\n({str(e)[:30]}...)', ha='center', va='center',
                       transform=ax.transAxes, fontsize=10, style='italic')
                ax.set_title(f'Cluster {cluster_idx}', fontsize=12, fontweight='bold')

        # Hide unused subplots
        for idx in range(n_clusters, n_rows * n_cols):
            row_idx = idx // n_cols
            col_idx = idx % n_cols
            axes[row_idx, col_idx].set_visible(False)

        # Add overall title
        var_title = var_name.replace("_", " ").title()
        fig.suptitle(f'{var_title} Maps - k={k} Weather Types',
                     fontsize=16, fontweight='bold', y=0.95)

        plt.tight_layout()
        plt.subplots_adjust(top=0.90, hspace=0.3, wspace=0.3)

        # Save plot
        output_path = os.path.join(output_dir, f"wt_k{k}_{var_name}_maps.png")
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Saved {var_name} k={k} maps to {output_path}")

def create_seasonal_maps(ds, var_name, k_values, output_dir, latlon_ds):
    """Create spatial maps for seasonal data."""

    var_data = ds[var_name]
    seasons = ['DJF', 'MAM', 'JJA', 'SON']

    # Check if this is a spatial variable
    spatial_dims = [d for d in var_data.dims if d not in ['k', 'cluster', 'season']]
    if not spatial_dims:
        logger.warning(f"Variable {var_name} has no spatial dimensions, skipping maps")
        return

    # Get coordinate information from the separate lat/lon file
    lats, lons = None, None

    # Try different coordinate naming conventions in the lat/lon file
    coord_pairs = [
        ('latitude', 'longitude'),
        ('lat', 'lon'),
        ('y', 'x'),
        ('Y', 'X'),
        ('LAT', 'LON'),
        ('XLAT', 'XLONG')  # Common WRF coordinate names
    ]

    for lat_name, lon_name in coord_pairs:
        if lat_name in latlon_ds.data_vars and lon_name in latlon_ds.data_vars:
            lats = latlon_ds[lat_name].values
            lons = latlon_ds[lon_name].values
            logger.info(f"Using coordinates from lat/lon file: {lat_name}, {lon_name}")
            break
        elif lat_name in latlon_ds.coords and lon_name in latlon_ds.coords:
            lats = latlon_ds.coords[lat_name].values
            lons = latlon_ds.coords[lon_name].values
            logger.info(f"Using coordinate variables from lat/lon file: {lat_name}, {lon_name}")
            break

    if lats is None or lons is None:
        logger.error(f"No recognizable spatial coordinates found in lat/lon file")
        logger.info(f"Available data variables in lat/lon file: {list(latlon_ds.data_vars.keys())}")
        logger.info(f"Available coordinates in lat/lon file: {list(latlon_ds.coords.keys())}")
        return

    # Create maps for each k value and each season
    for k in k_values:
        for season_idx, season in enumerate(seasons):
            logger.info(f"Creating {var_name} {season} maps for k={k}")

            # Use the same logic as regular maps but with seasonal data
            n_clusters = k
            n_cols = min(3, n_clusters)
            n_rows = int(np.ceil(n_clusters / n_cols))

            fig_width = min(20, max(12, n_cols * 6))
            fig_height = min(16, max(8, n_rows * 4))

            if HAS_CARTOPY:
                fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height),
                                       subplot_kw={'projection': ccrs.PlateCarree()})
            else:
                fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height))

            # Handle single subplot case
            if n_rows == 1 and n_cols == 1:
                axes = np.array([[axes]])
            elif n_rows == 1:
                axes = axes.reshape(1, -1)
            elif n_cols == 1:
                axes = axes.reshape(-1, 1)

            # Determine colormap and limits
            seasonal_data = var_data.sel(season=season_idx)
            vmin, vmax = get_variable_limits(seasonal_data, var_name, k)
            cmap = get_variable_colormap(var_name)

            # Plot data for each cluster
            for cluster_idx in range(n_clusters):
                row_idx = cluster_idx // n_cols
                col_idx = cluster_idx % n_cols
                ax = axes[row_idx, col_idx]

                try:
                    data_slice = var_data.sel(k=k, cluster=cluster_idx, season=season_idx)

                    if HAS_CARTOPY:
                        ax.add_feature(cfeature.COASTLINE, linewidth=0.5)
                        ax.add_feature(cfeature.BORDERS, linewidth=0.5)
                        ax.add_feature(cfeature.STATES, linewidth=0.3, alpha=0.5)

                        im = ax.contourf(lons, lats, data_slice.values,
                                       levels=20, cmap=cmap, vmin=vmin, vmax=vmax,
                                       transform=ccrs.PlateCarree())
                        ax.set_extent([-125, -66.5, 20, 50], ccrs.PlateCarree())
                    else:
                        im = ax.contourf(lons, lats, data_slice.values,
                                       levels=20, cmap=cmap, vmin=vmin, vmax=vmax)
                        ax.set_xlim(lons.min(), lons.max())
                        ax.set_ylim(lats.min(), lats.max())
                        ax.set_xlabel('Longitude')
                        ax.set_ylabel('Latitude')

                    ax.set_title(f'Cluster {cluster_idx}', fontsize=12, fontweight='bold')

                    cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.02)
                    cbar.set_label(get_variable_ylabel(var_name), fontsize=10)

                except (KeyError, IndexError):
                    ax.text(0.5, 0.5, 'No Data', ha='center', va='center',
                           transform=ax.transAxes, fontsize=12, style='italic')
                    ax.set_title(f'Cluster {cluster_idx}', fontsize=12, fontweight='bold')

            # Hide unused subplots
            for idx in range(n_clusters, n_rows * n_cols):
                row_idx = idx // n_cols
                col_idx = idx % n_cols
                axes[row_idx, col_idx].set_visible(False)

            # Add overall title
            var_title = var_name.replace("_", " ").title()
            fig.suptitle(f'{var_title} Maps - {season} - k={k} Weather Types',
                         fontsize=16, fontweight='bold', y=0.95)

            plt.tight_layout()
            plt.subplots_adjust(top=0.90, hspace=0.3, wspace=0.3)

            # Save plot
            output_path = os.path.join(output_dir, f"wt_k{k}_{var_name}_{season}_maps.png")
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"Saved {var_name} {season} k={k} maps to {output_path}")

def get_variable_limits(data, var_name, k):
    """Get appropriate color scale limits for a variable."""
    try:
        # Get data for this k value
        k_data = data.sel(k=k)

        # Calculate percentiles to avoid outliers
        valid_data = k_data.values[~np.isnan(k_data.values)]
        if len(valid_data) == 0:
            return 0, 1

        vmin = np.percentile(valid_data, 5)
        vmax = np.percentile(valid_data, 95)

        # Ensure reasonable range
        if vmax - vmin < 1e-6:
            vmax = vmin + 1

        return vmin, vmax
    except:
        return 0, 1

def get_variable_colormap(var_name):
    """Get appropriate colormap for each variable."""
    if 'freq' in var_name.lower() or 'frac' in var_name.lower():
        return 'viridis'
    elif 'precip' in var_name.lower() or 'max' in var_name.lower():
        return 'Blues'
    elif 'entropy' in var_name.lower():
        return 'plasma'
    elif 'dominance' in var_name.lower():
        return 'RdYlBu_r'
    else:
        return 'viridis'

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Create spatial maps for weather typing precipitation data')
    parser.add_argument('--data-path', type=str, required=True,
                        help='Path to weather typing precipitation NetCDF file')
    parser.add_argument('--latlon-path', type=str, default='sample_data/WRF_CCSM_lat_lon.nc',
                        help='Path to lat/lon coordinate NetCDF file')
    parser.add_argument('--output-dir', type=str, default='wt_maps',
                        help='Output directory for maps')
    parser.add_argument('--variables', type=str, nargs='+', default=None,
                        help='Variables to plot (default: key variables)')
    parser.add_argument('--max-k', type=int, default=None,
                        help='Maximum k value to process (for performance)')
    parser.add_argument('--sample-k', type=int, default=None,
                        help='Sample every nth k value (for performance)')
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug logging')
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Debug logging enabled")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Apply performance optimizations
    if args.max_k or args.sample_k:
        logger.info("Performance optimizations enabled:")
        if args.max_k:
            logger.info(f"  - Maximum k value: {args.max_k}")
        if args.sample_k:
            logger.info(f"  - Sampling every {args.sample_k}th k value")

    # Set performance parameters as function attributes (simple approach)
    create_wt_precipitation_maps._max_k = args.max_k
    create_wt_precipitation_maps._sample_k = args.sample_k

    # Create spatial maps with separate lat/lon file
    create_wt_precipitation_maps(args.data_path, args.output_dir, args.variables, args.latlon_path)

    logger.info(f"Weather typing precipitation maps saved to {args.output_dir}")

if __name__ == '__main__':
    main()
