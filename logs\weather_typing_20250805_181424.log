2025-08-05 18:14:24,268 - __main__ - INFO - run_heterogeneity_analysis.py:526 - Starting heterogeneity analysis
2025-08-05 18:14:24,269 - __main__ - INFO - run_heterogeneity_analysis.py:527 - Precipitation data: sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-05 18:14:24,269 - __main__ - INFO - run_heterogeneity_analysis.py:528 - Cluster data: regionalization_w_WT_24_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 18:14:24,269 - __main__ - INFO - run_heterogeneity_analysis.py:529 - Output directory: heterogeneity_results/regionalization_w_WT_24_hierarchical_singlevariable
2025-08-05 18:14:24,269 - __main__ - INFO - run_heterogeneity_analysis.py:530 - Maximum clusters: 125
2025-08-05 18:14:24,269 - __main__ - INFO - run_heterogeneity_analysis.py:531 - Number of simulations: 20
2025-08-05 18:14:24,269 - __main__ - INFO - run_heterogeneity_analysis.py:532 - Remap clusters: True
2025-08-05 18:14:24,270 - __main__ - INFO - run_heterogeneity_analysis.py:541 - Remapping clusters from 1D to 2D grid format
2025-08-05 18:14:24,270 - __main__ - ERROR - run_heterogeneity_analysis.py:551 - Failed to remap clusters: CI results file not found: regionalization_w_WT_24_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 18:14:24,270 - __main__ - INFO - run_heterogeneity_analysis.py:552 - Continuing with original cluster file
2025-08-05 18:14:24,270 - __main__ - INFO - run_heterogeneity_analysis.py:555 - Loading data
2025-08-05 18:14:24,270 - __main__ - INFO - run_heterogeneity_analysis.py:126 - Loading precipitation data from sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc
2025-08-05 18:14:24,471 - __main__ - INFO - run_heterogeneity_analysis.py:131 - Computing annual maxima from time series data
2025-08-05 18:14:34,348 - __main__ - INFO - run_heterogeneity_analysis.py:134 - Loading cluster data from regionalization_w_WT_24_hierarchical_onevariable/CI_results_hierarchical.nc
2025-08-05 18:14:34,349 - __main__ - ERROR - run_heterogeneity_analysis.py:604 - Error in heterogeneity analysis: [Errno 2] No such file or directory: '/lcrc/project/Hydro-model/sgev/GDO/test2/regionalization_w_WT_24_hierarchical_onevariable/CI_results_hierarchical.nc'
Traceback (most recent call last):
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/file_manager.py", line 211, in _acquire_with_cache_info
    file = self._cache[self._key]
           ~~~~~~~~~~~^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/lru_cache.py", line 56, in __getitem__
    value = self._cache[key]
            ~~~~~~~~~~~^^^^^
KeyError: [<class 'netCDF4._netCDF4.Dataset'>, ('/lcrc/project/Hydro-model/sgev/GDO/test2/regionalization_w_WT_24_hierarchical_onevariable/CI_results_hierarchical.nc',), 'r', (('clobber', True), ('diskless', False), ('format', 'NETCDF4'), ('persist', False)), '4ed99e68-0f40-47bc-96e8-3d315474e217']

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/lcrc/project/Hydro-model/sgev/GDO/test2/src/models/run_heterogeneity_analysis.py", line 556, in main
    precip_data, cluster_data = load_data(args.precip_path, cluster_path_to_use)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/lcrc/project/Hydro-model/sgev/GDO/test2/src/models/run_heterogeneity_analysis.py", line 135, in load_data
    cluster_data = xr.open_dataset(cluster_path)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/api.py", line 687, in open_dataset
    backend_ds = backend.open_dataset(
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 666, in open_dataset
    store = NetCDF4DataStore.open(
            ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 452, in open
    return cls(manager, group=group, mode=mode, lock=lock, autoclose=autoclose)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 393, in __init__
    self.format = self.ds.data_model
                  ^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 461, in ds
    return self._acquire()
           ^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/netCDF4_.py", line 455, in _acquire
    with self._manager.acquire_context(needs_lock) as root:
  File "/home/<USER>/.conda/envs/fastclust/lib/python3.11/contextlib.py", line 137, in __enter__
    return next(self.gen)
           ^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/file_manager.py", line 199, in acquire_context
    file, cached = self._acquire_with_cache_info(needs_lock)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/improv/miniconda3/2023-10-09/lib/python3.11/site-packages/xarray/backends/file_manager.py", line 217, in _acquire_with_cache_info
    file = self._opener(*self._args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "src/netCDF4/_netCDF4.pyx", line 2521, in netCDF4._netCDF4.Dataset.__init__
  File "src/netCDF4/_netCDF4.pyx", line 2158, in netCDF4._netCDF4._ensure_nc_success
FileNotFoundError: [Errno 2] No such file or directory: '/lcrc/project/Hydro-model/sgev/GDO/test2/regionalization_w_WT_24_hierarchical_onevariable/CI_results_hierarchical.nc'
