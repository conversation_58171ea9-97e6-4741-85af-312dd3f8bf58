#!/usr/bin/env python3
"""
Simple test to verify day-specific means are preserved.
"""

import numpy as np
import sys
import os

# Add src to path
sys.path.insert(0, 'src')

try:
    from features.data_preprocessing import AtmosClusterer
    print("✅ Import successful")
except Exception as e:
    print(f"❌ Import failed: {e}")
    sys.exit(1)

# Test the preprocess method behavior
print("\n🔍 Testing preprocess method behavior...")

# Create a simple test case
test_data = np.array([
    [1.0, 2.0, 3.0],  # Day 1
    [4.0, 5.0, 6.0],  # Day 2  
    [7.0, 8.0, 9.0],  # Day 3
])

print(f"Original data:\n{test_data}")

# Test 1: Check if mean is preserved when we don't subtract it again
print("\n📊 Test 1: Variance-only standardization")
data_std = np.std(test_data, axis=0, ddof=1)
standardized_variance_only = test_data / data_std
print(f"Std dev: {data_std}")
print(f"Variance-only standardized:\n{standardized_variance_only}")
print(f"Mean preserved: {np.mean(standardized_variance_only, axis=0)}")

# Test 2: Traditional standardization (what was happening before)
print("\n📊 Test 2: Traditional standardization (mean + variance)")
data_mean = np.mean(test_data, axis=0)
data_std = np.std(test_data, axis=0, ddof=1)
standardized_traditional = (test_data - data_mean) / data_std
print(f"Mean: {data_mean}")
print(f"Std dev: {data_std}")
print(f"Traditional standardized:\n{standardized_traditional}")
print(f"Mean after standardization: {np.mean(standardized_traditional, axis=0)}")

print("\n✅ The fix should preserve the mean structure (Test 1) instead of removing it (Test 2)")
print("This means day-specific anomalies computed in add_variable() will be preserved.")
