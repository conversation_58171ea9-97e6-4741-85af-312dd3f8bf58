# Weather Typing and Precipitation Regionalization

## Project Description
This project implements weather typing and precipitation regionalization analysis for climate studies. It uses clustering techniques to identify weather patterns and precipitation regions across the Continental United States (CONUS), and evaluates the homogeneity of these regions using L-moment statistics.

## Quick Start

### **Test Clustering Methods**
```bash
python test_clustering_methods.py
```

### **Run Precipitation Regionalization**

**Without Weather Types:**
```bash
# K-means clustering (default)
python -m src.models.run_precip_regions \
  --maxclust 20

# Hierarchical clustering
python -m src.models.run_precip_regions \
  --maxclust 20 \
  --method hierarchical
```

**With Weather Types:**
```bash
# K-means clustering (default)
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-10 \
  --cluster-stats cluster_precip_stats.nc

# Hierarchical clustering
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-10 \
  --cluster-stats cluster_precip_stats.nc \
  --method hierarchical
```

### **Create Weather Typing Maps**
```bash
python -m src.models.plot_wt_precipitation \
  --data-path cluster_precip_stats.nc \
  --output-dir wt_maps
```

## Workflow Overview

The analysis workflow consists of six main steps:

```
┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐     ┌─────────────────────┐
│                     │     │                     │     │                     │     │                     │
│   Weather Typing    │────▶│    Precipitation    │────▶│    Precipitation    │────▶│    Precipitation    │
│      Analysis       │     │     Statistics      │     │   Regionalization   │     │   Regionalization   │
│                     │     │                     │     │                     │     │   with Weather Types│
└─────────────────────┘     └─────────────────────┘     └─────────────────────┘     └─────────────────────┘
                                                                │                              │
                                                                │                              │
                                                                ▼                              ▼
                                                        ┌─────────────────────┐     ┌─────────────────────┐
                                                        │                     │     │                     │
                                                        │    Heterogeneity    │     │    Heterogeneity    │
                                                        │      Analysis       │     │      Analysis       │
                                                        │                     │     │                     │
                                                        └─────────────────────┘     └─────────────────────┘
                                                                │                              │
                                                                │                              │
                                                                └──────────────┬───────────────┘
                                                                               │
                                                                               ▼
                                                                      ┌─────────────────────┐
                                                                      │                     │
                                                                      │    Heterogeneity    │
                                                                      │     Comparison      │
                                                                      │                     │
                                                                      └─────────────────────┘
```

1. **Weather Typing Analysis**: Identifies distinct weather patterns (weather types) using atmospheric variables.
2. **Precipitation Statistics**: Calculates precipitation statistics for each weather type.
3. **Precipitation Regionalization**: Identifies homogeneous precipitation regions without considering weather types.
4. **Precipitation Regionalization with Weather Types**: Identifies homogeneous precipitation regions considering weather type information.
5. **Heterogeneity Analysis**: Evaluates the homogeneity of precipitation regions using L-moment statistics (H1, H2, H3) as described in Hosking and Wallis (1997).
6. **Heterogeneity Comparison**: Compares heterogeneity results across different regionalization methods to identify optimal cluster configurations.
````

README.md
````markdown
## Project Organization

    ├── LICENSE
    ├── Makefile           <- Makefile with commands like `make data` or `make train`
    ├── README.md          <- The top-level README for developers using this project.
    ├── data
    │   ├── external       <- Data from third party sources.
    │   ├── interim        <- Intermediate data that has been transformed.
    │   ├── processed      <- The final, canonical data sets for modeling.
    │   └── raw            <- The original, immutable data dump.
    │
    ├── examples           <- Example scripts for running the workflow
    │
    ├── models             <- Trained and serialized models, model predictions, or model summaries
    │
    ├── notebooks          <- Jupyter notebooks. Naming convention is a number (for ordering),
    │                         the creator's initials, and a short `-` delimited description, e.g.
    │                         `1.0-jqp-initial-data-exploration`.
    │
    ├── references         <- Data dictionaries, manuals, and all other explanatory materials.
    │
    ├── reports            <- Generated analysis as HTML, PDF, LaTeX, etc.
    │   └── figures        <- Generated graphics and figures to be used in reporting
    │
    ├── requirements.txt   <- The requirements file for reproducing the analysis environment, e.g.
    │                         generated with `pip freeze > requirements.txt`
    │
    ├── setup.py           <- makes project pip installable (pip install -e .) so src can be imported
    ├── src                <- Source code for use in this project.
    │   ├── __init__.py    <- Makes src a Python package
    │   │
    │   ├── data           <- Scripts to download or generate data
    │   │   └── make_dataset.py
    │   │
    │   ├── features       <- Scripts to turn raw data into features for modeling
    │   │   ├── build_features.py
    │   │   ├── data_preprocessing.py
    │   │   └── standardize.py
    │   │
    │   ├── models         <- Scripts to train models and then use trained models to make
    │   │   │                 predictions
    │   │   ├── clustering.py                   <- Enhanced clustering with K-means and hierarchical
    │   │   ├── heterogeneity.py                <- Heterogeneity analysis using L-moments
    │   │   ├── hierarchical_utils.py           <- Hierarchical clustering utilities (NEW)
    │   │   ├── homogeneity.py
    │   │   ├── kmeans_utils.py
    │   │   ├── make_WT_precipfiles.py
    │   │   ├── plot_wt_precipitation.py        <- Weather typing precipitation mapping (NEW)
    │   │   ├── precipitation.py
    │   │   ├── run_heterogeneity_analysis.py   <- Script to run heterogeneity analysis
    │   │   ├── run_heterogeneity_workflow.py   <- Workflow to run heterogeneity on all regions
    │   │   ├── compare_heterogeneity_results.py <- Compare heterogeneity across methods
    │   │   ├── run_precip_regions.py
    │   │   ├── run_precipitation_regions_w_WTs.py <- Enhanced with clustering method selection
    │   │   └── run_wt_clustering.py
    │   │
    │   ├── utils          <- Utility functions and classes
    │   │   ├── config.py
    │   │   ├── data_validator.py
    │   │   ├── exceptions.py
    │   │   └── logging_config.py
    │   │
    │   └── visualization  <- Scripts to create exploratory and results oriented visualizations
    │       └── visualize.py
    │
    └── tox.ini            <- tox file with settings for running tox; see tox.readthedocs.io
````

README.md
````markdown
## Clustering Methods

This project supports two clustering methods for precipitation regionalization:

### **K-means Clustering (Default)**
- **Algorithm**: K-means with multiple random initializations
- **Characteristics**:
  - Fast computation for large datasets
  - Prefers spherical, compact clusters
  - Good for well-separated, similar-sized clusters
- **Best for**: Traditional regionalization approaches

### **Hierarchical Clustering (NEW)**
- **Algorithm**: Ward's agglomerative hierarchical clustering
- **Characteristics**:
  - Deterministic results (no random initialization)
  - Handles arbitrary cluster shapes
  - Reveals natural hierarchy in data
  - Generates dendrogram for visualization
- **Best for**: Exploring nested relationships and irregular cluster shapes

### **Method Selection**
Use the `--method` parameter to choose your clustering approach:
- `--method kmeans` (default)
- `--method hierarchical`

Both methods use the same classifiability index (CI) calculation for fair comparison.

## Running the Workflow

### Option 1: Run Individual Steps

#### Step 1: Weather Typing Analysis

```bash
python -m src.models.run_wt_clustering --outdir ./CONUS_ERA5 --maxclust 35 --nsim 100
```

This step:
- Loads atmospheric variables (geopotential height, mean sea level pressure, wind components)
- Applies EOF analysis to reduce dimensionality
- Performs K-means clustering with classifiability index calculation
- Outputs cluster assignments and classifiability indices for different cluster counts

#### Step 2: Generate Precipitation Statistics for Weather Types

```bash
python -m src.models.make_WT_precipfiles \
  --precip-path sample_data/PRISM_daily_CCSM_interpolated_n5_1981_2020.nc \
  --cluster-path CONUS_ERA5/CI_results.nc \
  --output-path cluster_precip_stats.nc
```

This step:
- Calculates precipitation statistics for each weather type
- Computes seasonal and annual statistics
- Outputs a NetCDF file with precipitation statistics

#### Step 3: Precipitation Regionalization (without Weather Types)

**Option A: K-means Clustering (Default)**
```bash
python -m src.models.run_precip_regions \
  --outdir ./regionalization_wo_WT \
  --maxclust 40 \
  --nsim 100
```

**Option B: Hierarchical Clustering**
```bash
python -m src.models.run_precip_regions \
  --outdir ./regionalization_wo_WT \
  --maxclust 40 \
  --nsim 100 \
  --method hierarchical
```

**Option C: Compare Both Methods**
```bash
# Run K-means clustering
python -m src.models.run_precip_regions \
  --maxclust 20 \
  --method kmeans

# Run hierarchical clustering
python -m src.models.run_precip_regions \
  --maxclust 20 \
  --method hierarchical
```

This step:
- Identifies homogeneous precipitation regions based on precipitation characteristics
- **NEW**: Choose between K-means or Ward's hierarchical agglomerative clustering
- **NEW**: Method-specific output directories for easy comparison
- Outputs region assignments and visualizations

#### Step 4: Precipitation Regionalization with Weather Types

**Option A: K-means Clustering (Default)**
```bash
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-35 \
  --maxclust 40 \
  --nsim 100 \
  --cluster-stats cluster_precip_stats.nc
```

**Option B: Hierarchical Clustering**
```bash
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-35 \
  --maxclust 40 \
  --nsim 100 \
  --cluster-stats cluster_precip_stats.nc \
  --method hierarchical
```

**Option C: Compare Both Methods**
```bash
# Run K-means clustering
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-10 \
  --maxclust 20 \
  --cluster-stats cluster_precip_stats.nc \
  --method kmeans

# Run hierarchical clustering
python -m src.models.run_precipitation_regions_w_WTs \
  --wt-range 2-10 \
  --maxclust 20 \
  --cluster-stats cluster_precip_stats.nc \
  --method hierarchical
```

This step:
- Identifies homogeneous precipitation regions considering weather type information
- **NEW**: Choose between K-means or Ward's hierarchical agglomerative clustering
- Performs regionalization for different numbers of weather types
- Outputs region assignments and visualizations for each weather type count
- **NEW**: Method-specific output directories for easy comparison

#### Step 5: Heterogeneity Analysis

```bash
python -m src.models.run_heterogeneity_workflow \
  --precip-path sample_data/PRISM_1981_2020_AMS.nc \
  --base-dir ./ \
  --output-base-dir heterogeneity_results \
  --max-clusters 40 \
  --nsim 500
```

This step:
- Finds all regionalization output directories (both with and without weather typing)
- For each regionalization result, calculates heterogeneity statistics (H1, H2, H3) using L-moments
- Evaluates the homogeneity of each region using criteria from Hosking and Wallis (1997)
- Outputs tables and visualizations of heterogeneity statistics

You can also run heterogeneity analysis on a single regionalization result:

```bash
python -m src.models.run_heterogeneity_analysis \
  --precip-path sample_data/PRISM_1981_2020_AMS.nc \
  --cluster-path regionalization_wo_WT/CI_results.nc \
  --output-dir heterogeneity_results/regionalization_wo_WT \
  --max-clusters 40 \
  --nsim 500
```

#### Step 6: Compare Heterogeneity Results

```bash
python -m src.models.compare_heterogeneity_results \
  --results-dir heterogeneity_results \
  --output-dir heterogeneity_comparison
```

This step:
- Compares heterogeneity statistics across different regionalization methods
- Creates summary tables and visualizations to identify optimal cluster configurations
- Helps determine which regionalization approach produces the most homogeneous regions
````

README.md
````markdown
### Option 2: Run the Full Workflow

Use the provided example script to run the full workflow:

```bash
python examples/run_full_workflow.py \
  --data-dir ./sample_data \
  --output-dir ./results \
  --wt-maxclust 35 \
  --pr-maxclust 40 \
  --nsim 100
```

You can skip specific steps using the following flags:
- `--skip-wt`: Skip weather typing step
- `--skip-precip-stats`: Skip precipitation statistics step
- `--skip-pr`: Skip precipitation regionalization step
- `--skip-pr-wt`: Skip precipitation regionalization with weather typing step
- `--skip-heterogeneity`: Skip heterogeneity analysis step
- `--skip-comparison`: Skip heterogeneity comparison step

### Option 3: Run with Configuration File

Use a YAML configuration file to specify all parameters:

```bash
python examples/run_with_config.py --config examples/config.yaml
```

You can customize the configuration file (`examples/config.yaml`) to set all parameters for the analysis.

## Testing Clustering Methods

Before running the full analysis, you can test both clustering methods to ensure they work correctly:

```bash
python test_clustering_methods.py
```

This test script:
- Creates synthetic clustered data
- Tests both K-means and hierarchical clustering
- Generates comparison outputs
- Validates that both methods produce reasonable results

## Weather Typing Precipitation Visualization

Create spatial maps of weather typing precipitation data:

```bash
# Create spatial maps for weather typing variables
python -m src.models.plot_wt_precipitation \
  --data-path cluster_precip_stats.nc \
  --output-dir wt_maps \
  --variables mean_annual_max wt_freq_gridpoint wt_freq_ams_gridpoint
```

This creates 2D spatial maps showing:
- Mean annual maximum precipitation by weather type
- Weather type frequency at each gridpoint
- Weather type frequency during annual maximum events
- Separate maps for each k value and cluster

## Makefile Commands

For convenience, you can use the following make commands:

```bash
make weather_typing      # Run weather typing analysis
make precip_regions      # Run precipitation regionalization
make precip_regions_wt   # Run precipitation regionalization with weather typing
make heterogeneity       # Run heterogeneity analysis
make compare_heterogeneity # Compare heterogeneity results
make full_workflow       # Run the full workflow
```
````

README.md
````markdown
## Expected Outputs

The workflow produces the following outputs:

1. **Weather Typing Analysis**:
   - `CONUS_ERA5/CI_results.nc`: Cluster assignments and classifiability indices
   - `CONUS_ERA5/plot_ci.png`: Plot of classifiability indices
   - `CONUS_ERA5/plot_CIci.png`: Plot with confidence intervals

2. **Precipitation Statistics**:
   - `cluster_precip_stats.nc`: Precipitation statistics for each weather type

3. **Precipitation Regionalization (without Weather Types)**:

   **K-means outputs**:
   - `regionalization_wo_WT_kmeans/CI_results_kmeans.nc`: Region assignments
   - `regionalization_wo_WT_kmeans/plot_ci_kmeans.png`: CI plot
   - `regionalization_wo_WT_kmeans/plot_CIci_kmeans.png`: CI with confidence intervals
   - `regionalization_wo_WT_kmeans/plots/`: Visualizations of regions

   **Hierarchical outputs**:
   - `regionalization_wo_WT_hierarchical/CI_results_hierarchical.nc`: Region assignments
   - `regionalization_wo_WT_hierarchical/plot_ci_hierarchical.png`: CI plot
   - `regionalization_wo_WT_hierarchical/plot_CIci_hierarchical.png`: CI with confidence intervals
   - `regionalization_wo_WT_hierarchical/dendrogram.png`: Cluster hierarchy visualization
   - `regionalization_wo_WT_hierarchical/plots/`: Visualizations of regions

4. **Precipitation Regionalization with Weather Types**:

   **K-means outputs**:
   - `regionalization_w_WT_*_kmeans/CI_results_kmeans.nc`: Region assignments
   - `regionalization_w_WT_*_kmeans/plot_ci_kmeans.png`: CI plot
   - `regionalization_w_WT_*_kmeans/plot_CIci_kmeans.png`: CI with confidence intervals
   - `regionalization_w_WT_*_kmeans/plots/`: Visualizations of regions

   **Hierarchical outputs**:
   - `regionalization_w_WT_*_hierarchical/CI_results_hierarchical.nc`: Region assignments
   - `regionalization_w_WT_*_hierarchical/plot_ci_hierarchical.png`: CI plot
   - `regionalization_w_WT_*_hierarchical/plot_CIci_hierarchical.png`: CI with confidence intervals
   - `regionalization_w_WT_*_hierarchical/dendrogram.png`: Cluster hierarchy visualization
   - `regionalization_w_WT_*_hierarchical/plots/`: Visualizations of regions

5. **Heterogeneity Analysis**:
   - `heterogeneity_results/regionalization_wo_WT/heterogeneity_summary.csv`: Summary of heterogeneity statistics
   - `heterogeneity_results/regionalization_wo_WT/heterogeneity_overall_summary.csv`: Overall summary across all k values
   - `heterogeneity_results/regionalization_wo_WT/heterogeneity_k*.csv`: Detailed statistics for each k value
   - `heterogeneity_results/regionalization_wo_WT/heterogeneity_statistics.nc`: NetCDF file with all statistics
   - `heterogeneity_results/regionalization_wo_WT/heterogeneity_statistics.png`: Plot of heterogeneity statistics
   - `heterogeneity_results/regionalization_wo_WT/heterogeneity_heatmap.png`: Heatmap of heterogeneity values

6. **Heterogeneity Comparison**:
   - `heterogeneity_comparison/combined_heterogeneity_summary.csv`: Combined summary across all methods
   - `heterogeneity_comparison/combined_overall_summary.csv`: Combined overall summary
   - `heterogeneity_comparison/best_k_summary.csv`: Summary of best k values for each method
   - `heterogeneity_comparison/top_k_detailed.csv`: Detailed statistics for top k values
   - `heterogeneity_comparison/H1_comparison.png`: Comparison of H1 statistics across methods
   - `heterogeneity_comparison/pct_acceptable_comparison.png`: Comparison of percentage of acceptable regions
   - `heterogeneity_comparison/homogeneity_categories.png`: Breakdown of regions by homogeneity category

## Interpreting Heterogeneity Results

The heterogeneity statistics (H1, H2, H3) are interpreted according to Hosking and Wallis (1997):

- **H < 1**: The region is "acceptably homogeneous"
- **1 ≤ H < 2**: The region is "possibly heterogeneous"
- **H ≥ 2**: The region is "definitely heterogeneous"

H1 is based on L-CV, H2 on L-CV and L-skewness, and H3 on L-skewness and L-kurtosis. H1 is typically given the most weight in the analysis.

## Choosing Between Clustering Methods

### **When to Use K-means**
- **Large datasets**: Faster computation for big data
- **Spherical clusters expected**: When regions are roughly circular/compact
- **Established workflows**: When comparing with previous K-means results
- **Speed priority**: When computational efficiency is important

### **When to Use Hierarchical Clustering**
- **Exploring data structure**: When you want to understand natural groupings
- **Irregular cluster shapes**: When regions may have complex boundaries
- **Deterministic results**: When you need reproducible results without random initialization
- **Hierarchy visualization**: When dendrogram insights are valuable
- **Small to medium datasets**: When computational cost is not a major concern

### **Method Comparison Workflow**
1. **Run both methods** on a subset of your data (e.g., `--wt-range 2-10`)
2. **Compare CI values** to see which method produces better cluster separation
3. **Examine dendrograms** (hierarchical) for natural cluster numbers
4. **Evaluate heterogeneity results** to see which produces more homogeneous regions
5. **Choose the optimal method** based on your specific research goals

### **Performance Considerations**
- **K-means**: O(n×k×i×d) where n=samples, k=clusters, i=iterations, d=dimensions
- **Hierarchical**: O(n³) for full hierarchy, but uses efficient Ward linkage
- **Recommendation**: Test both methods on your data to determine the best approach

## License

This project is licensed under the MIT License - see the LICENSE file for details.
````